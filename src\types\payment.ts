export interface Payment {
  id: string
  transactionId: string
  orderId?: string
  
  // Student Information
  studentId: string
  studentName: string
  studentEmail: string
  studentPhone?: string
  
  // Payment Details
  amount: number
  currency: 'INR' | 'USD' | 'EUR'
  status: 'PENDING' | 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'REFUNDED' | 'PARTIALLY_REFUNDED'
  paymentMethod: 'CREDIT_CARD' | 'DEBIT_CARD' | 'UPI' | 'NET_BANKING' | 'WALLET' | 'EMI' | 'CASH' | 'BANK_TRANSFER'
  paymentGateway: 'RAZORPAY' | 'STRIPE' | 'PAYU' | 'PAYPAL' | 'PHONEPE' | 'GPAY' | 'PAYTM' | 'MANUAL'
  
  // Purchase Information
  itemType: 'COURSE' | 'SUBSCRIPTION' | 'TEST_SERIES' | 'BOOK' | 'LIVE_CLASS' | 'CONSULTATION' | 'OTHER'
  itemId?: string
  itemName: string
  itemDescription?: string
  
  // Subscription Details (if applicable)
  subscriptionId?: string
  subscriptionType?: 'MONTHLY' | 'QUARTERLY' | 'HALF_YEARLY' | 'YEARLY' | 'LIFETIME'
  subscriptionStartDate?: string
  subscriptionEndDate?: string
  
  // Pricing Details
  originalAmount: number
  discountAmount?: number
  discountCode?: string
  taxAmount?: number
  processingFee?: number
  finalAmount: number
  
  // Payment Gateway Details
  gatewayTransactionId?: string
  gatewayOrderId?: string
  gatewayPaymentId?: string
  gatewaySignature?: string
  gatewayResponse?: any
  
  // Timestamps
  createdAt: string
  updatedAt: string
  paidAt?: string
  failedAt?: string
  refundedAt?: string
  
  // Additional Information
  failureReason?: string
  refundReason?: string
  refundAmount?: number
  notes?: string
  tags: string[]
  
  // Billing Address
  billingAddress?: {
    name: string
    email: string
    phone?: string
    address: string
    city: string
    state: string
    country: string
    pincode: string
  }
  
  // Analytics
  conversionSource?: string
  campaignId?: string
  referralCode?: string
  deviceInfo?: string
  ipAddress?: string
}

export interface PaymentPlan {
  id: string
  name: string
  description: string
  type: 'COURSE' | 'SUBSCRIPTION' | 'TEST_SERIES' | 'BUNDLE'
  
  // Pricing
  originalPrice: number
  currentPrice: number
  currency: 'INR' | 'USD' | 'EUR'
  
  // Subscription Details
  duration?: number // in months
  durationType?: 'DAYS' | 'MONTHS' | 'YEARS' | 'LIFETIME'
  
  // Features
  features: string[]
  limitations?: string[]
  
  // Availability
  isActive: boolean
  isPopular: boolean
  isFeatured: boolean
  
  // Validity
  validFrom?: string
  validUntil?: string
  
  // Limits
  maxUsers?: number
  currentUsers: number
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface Subscription {
  id: string
  studentId: string
  studentName: string
  studentEmail: string
  
  // Plan Details
  planId: string
  planName: string
  planType: PaymentPlan['type']
  
  // Subscription Details
  status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'CANCELLED' | 'SUSPENDED' | 'PENDING'
  startDate: string
  endDate: string
  autoRenew: boolean
  
  // Payment Details
  amount: number
  currency: string
  billingCycle: 'MONTHLY' | 'QUARTERLY' | 'HALF_YEARLY' | 'YEARLY' | 'LIFETIME'
  nextBillingDate?: string
  
  // Payment History
  payments: Payment[]
  totalPaid: number
  
  // Usage
  lastAccessDate?: string
  accessCount: number
  
  // Metadata
  createdAt: string
  updatedAt: string
  cancelledAt?: string
  cancellationReason?: string
  notes?: string
}

export interface Refund {
  id: string
  paymentId: string
  transactionId: string
  
  // Student Information
  studentId: string
  studentName: string
  studentEmail: string
  
  // Refund Details
  amount: number
  currency: string
  reason: string
  status: 'PENDING' | 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  type: 'FULL' | 'PARTIAL'
  
  // Gateway Details
  gatewayRefundId?: string
  gatewayResponse?: any
  
  // Processing
  processedBy?: string
  processedAt?: string
  
  // Timestamps
  requestedAt: string
  completedAt?: string
  
  // Additional Information
  notes?: string
  attachments?: string[]
}

export interface PaymentStats {
  // Revenue
  totalRevenue: number
  monthlyRevenue: number
  yearlyRevenue: number
  averageOrderValue: number
  
  // Transactions
  totalTransactions: number
  successfulTransactions: number
  failedTransactions: number
  pendingTransactions: number
  
  // Success Rates
  successRate: number
  failureRate: number
  
  // Payment Methods
  paymentMethodDistribution: {
    method: string
    count: number
    amount: number
    percentage: number
  }[]
  
  // Subscriptions
  activeSubscriptions: number
  expiredSubscriptions: number
  cancelledSubscriptions: number
  subscriptionRevenue: number
  
  // Refunds
  totalRefunds: number
  refundAmount: number
  refundRate: number
  
  // Growth
  monthlyGrowth: number
  yearlyGrowth: number
  
  // Top Performers
  topCourses: {
    id: string
    name: string
    revenue: number
    sales: number
  }[]
  
  topPlans: {
    id: string
    name: string
    revenue: number
    subscribers: number
  }[]
  
  // Time Series Data
  dailyRevenue: {
    date: string
    revenue: number
    transactions: number
  }[]
  
  monthlyRevenue: {
    month: string
    revenue: number
    transactions: number
  }[]
}

export interface CreatePaymentRequest {
  studentId: string
  itemType: Payment['itemType']
  itemId?: string
  itemName: string
  itemDescription?: string
  amount: number
  currency?: Payment['currency']
  paymentMethod: Payment['paymentMethod']
  paymentGateway: Payment['paymentGateway']
  subscriptionType?: Payment['subscriptionType']
  discountCode?: string
  billingAddress?: Payment['billingAddress']
  notes?: string
  tags?: string[]
}

export interface UpdatePaymentRequest {
  id: string
  status?: Payment['status']
  gatewayTransactionId?: string
  gatewayOrderId?: string
  gatewayPaymentId?: string
  gatewaySignature?: string
  gatewayResponse?: any
  failureReason?: string
  notes?: string
}

export interface CreateRefundRequest {
  paymentId: string
  amount: number
  reason: string
  type: Refund['type']
  notes?: string
}

export interface PaymentFilters {
  search?: string
  status?: Payment['status']
  paymentMethod?: Payment['paymentMethod']
  paymentGateway?: Payment['paymentGateway']
  itemType?: Payment['itemType']
  studentId?: string
  amountMin?: number
  amountMax?: number
  dateFrom?: string
  dateTo?: string
  tags?: string[]
  sortBy?: 'createdAt' | 'amount' | 'studentName' | 'status'
  sortOrder?: 'asc' | 'desc'
}

export interface SubscriptionFilters {
  search?: string
  status?: Subscription['status']
  planId?: string
  billingCycle?: Subscription['billingCycle']
  autoRenew?: boolean
  expiringIn?: number // days
  sortBy?: 'createdAt' | 'endDate' | 'amount' | 'studentName'
  sortOrder?: 'asc' | 'desc'
}

export interface PaymentGatewayConfig {
  id: string
  name: string
  type: Payment['paymentGateway']
  isActive: boolean
  
  // Configuration
  apiKey?: string
  secretKey?: string
  webhookSecret?: string
  merchantId?: string
  
  // Settings
  supportedMethods: Payment['paymentMethod'][]
  supportedCurrencies: Payment['currency'][]
  processingFee: number // percentage
  
  // Limits
  minAmount: number
  maxAmount: number
  
  // Features
  supportsRefunds: boolean
  supportsSubscriptions: boolean
  supportsWebhooks: boolean
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface PaymentAnalytics {
  // Revenue Analytics
  revenueByPeriod: {
    period: string
    revenue: number
    transactions: number
    growth: number
  }[]
  
  // Payment Method Analytics
  paymentMethodPerformance: {
    method: string
    transactions: number
    revenue: number
    successRate: number
    averageAmount: number
  }[]
  
  // Course/Item Performance
  itemPerformance: {
    itemId: string
    itemName: string
    itemType: string
    revenue: number
    sales: number
    refunds: number
    netRevenue: number
  }[]
  
  // Geographic Analytics
  revenueByLocation: {
    state: string
    city: string
    revenue: number
    transactions: number
  }[]
  
  // Subscription Analytics
  subscriptionMetrics: {
    newSubscriptions: number
    renewals: number
    cancellations: number
    churnRate: number
    ltv: number // lifetime value
  }
  
  // Failure Analysis
  failureAnalysis: {
    reason: string
    count: number
    amount: number
    percentage: number
  }[]
}
