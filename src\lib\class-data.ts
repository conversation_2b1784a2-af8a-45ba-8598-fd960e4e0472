import { Class, CreateClassRequest, UpdateClassRequest, ClassFilters, ClassStats, ClassResource } from '@/types/class'

// Mock data - In real app, this would come from your API
const mockClasses: Class[] = [
  {
    id: '1',
    title: 'Introduction to Motion',
    description: 'Basic concepts of motion, displacement, velocity, and acceleration',
    subjectId: '1',
    subjectName: 'Mechanics',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    order: 1,
    videoUrl: 'https://example.com/videos/intro-motion.mp4',
    pdfUrl: 'https://example.com/pdfs/intro-motion.pdf',
    testId: 'test_1',
    testName: 'Motion Basics Test',
    duration: 45,
    isPublished: true,
    isFree: true,
    viewCount: 1250,
    completionRate: 85,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    thumbnail: '/images/motion-thumbnail.jpg',
    notes: 'Key concepts: displacement vs distance, velocity vs speed',
    resources: [
      {
        id: 'res_1',
        name: 'Motion Formulas Sheet',
        type: 'PDF',
        url: 'https://example.com/resources/motion-formulas.pdf',
        size: 2048000,
        uploadedAt: '2024-01-15T10:00:00Z'
      }
    ]
  },
  {
    id: '2',
    title: 'Laws of Motion',
    description: 'Newton\'s three laws of motion with practical examples',
    subjectId: '1',
    subjectName: 'Mechanics',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    order: 2,
    videoUrl: 'https://example.com/videos/laws-motion.mp4',
    pdfUrl: 'https://example.com/pdfs/laws-motion.pdf',
    testId: 'test_2',
    testName: 'Newton\'s Laws Test',
    duration: 60,
    isPublished: true,
    isFree: false,
    viewCount: 980,
    completionRate: 78,
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: '2024-01-15T11:00:00Z',
    thumbnail: '/images/laws-motion-thumbnail.jpg',
    notes: 'Focus on force diagrams and applications',
    resources: [
      {
        id: 'res_2',
        name: 'Force Diagram Examples',
        type: 'PDF',
        url: 'https://example.com/resources/force-diagrams.pdf',
        size: 3072000,
        uploadedAt: '2024-01-15T11:00:00Z'
      }
    ]
  },
  {
    id: '3',
    title: 'Plant Cell Structure',
    description: 'Detailed study of plant cell components and organelles',
    subjectId: '3',
    subjectName: 'Botany',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    order: 1,
    videoUrl: 'https://example.com/videos/plant-cell.mp4',
    pdfUrl: 'https://example.com/pdfs/plant-cell.pdf',
    duration: 50,
    isPublished: true,
    isFree: false,
    viewCount: 750,
    completionRate: 92,
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    thumbnail: '/images/plant-cell-thumbnail.jpg',
    notes: 'Important: chloroplast structure and function',
    resources: [
      {
        id: 'res_3',
        name: 'Cell Organelles Diagram',
        type: 'PDF',
        url: 'https://example.com/resources/cell-organelles.pdf',
        size: 1536000,
        uploadedAt: '2024-01-12T10:00:00Z'
      }
    ]
  },
  {
    id: '4',
    title: 'Photosynthesis Process',
    description: 'Light and dark reactions of photosynthesis',
    subjectId: '3',
    subjectName: 'Botany',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    order: 2,
    videoUrl: 'https://example.com/videos/photosynthesis.mp4',
    pdfUrl: 'https://example.com/pdfs/photosynthesis.pdf',
    testId: 'test_3',
    testName: 'Photosynthesis Quiz',
    duration: 55,
    isPublished: true,
    isFree: false,
    viewCount: 680,
    completionRate: 88,
    createdAt: '2024-01-12T11:00:00Z',
    updatedAt: '2024-01-12T11:00:00Z',
    thumbnail: '/images/photosynthesis-thumbnail.jpg',
    notes: 'Calvin cycle is crucial for NEET',
    resources: []
  },
  {
    id: '5',
    title: 'Limits and Continuity',
    description: 'Introduction to limits, continuity, and differentiability',
    subjectId: '5',
    subjectName: 'Calculus',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    order: 1,
    videoUrl: 'https://example.com/videos/limits.mp4',
    duration: 70,
    isPublished: true,
    isFree: false,
    viewCount: 420,
    completionRate: 75,
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    thumbnail: '/images/limits-thumbnail.jpg',
    notes: 'Practice L\'Hospital\'s rule extensively',
    resources: [
      {
        id: 'res_4',
        name: 'Limit Problems Practice',
        type: 'PDF',
        url: 'https://example.com/resources/limit-problems.pdf',
        size: 2560000,
        uploadedAt: '2024-01-10T10:00:00Z'
      }
    ]
  },
  {
    id: '6',
    title: 'Organic Chemistry Introduction',
    description: 'Basic concepts of organic chemistry and nomenclature',
    subjectId: '8',
    subjectName: 'Organic Chemistry Basics',
    courseId: '5',
    courseName: 'Free NEET Chemistry Basics',
    order: 1,
    videoUrl: 'https://example.com/videos/organic-intro.mp4',
    pdfUrl: 'https://example.com/pdfs/organic-intro.pdf',
    duration: 40,
    isPublished: true,
    isFree: true,
    viewCount: 2100,
    completionRate: 95,
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    thumbnail: '/images/organic-intro-thumbnail.jpg',
    notes: 'IUPAC nomenclature is fundamental',
    resources: [
      {
        id: 'res_5',
        name: 'IUPAC Naming Rules',
        type: 'PDF',
        url: 'https://example.com/resources/iupac-rules.pdf',
        size: 1024000,
        uploadedAt: '2024-01-05T10:00:00Z'
      }
    ]
  }
]

export const getClasses = (filters?: ClassFilters): Promise<Class[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredClasses = [...mockClasses]
      
      if (filters) {
        if (filters.subjectId) {
          filteredClasses = filteredClasses.filter(c => c.subjectId === filters.subjectId)
        }
        if (filters.courseId) {
          filteredClasses = filteredClasses.filter(c => c.courseId === filters.courseId)
        }
        if (filters.isPublished !== undefined) {
          filteredClasses = filteredClasses.filter(c => c.isPublished === filters.isPublished)
        }
        if (filters.isFree !== undefined) {
          filteredClasses = filteredClasses.filter(c => c.isFree === filters.isFree)
        }
        if (filters.hasVideo !== undefined) {
          filteredClasses = filteredClasses.filter(c => filters.hasVideo ? !!c.videoUrl : !c.videoUrl)
        }
        if (filters.hasPdf !== undefined) {
          filteredClasses = filteredClasses.filter(c => filters.hasPdf ? !!c.pdfUrl : !c.pdfUrl)
        }
        if (filters.hasTest !== undefined) {
          filteredClasses = filteredClasses.filter(c => filters.hasTest ? !!c.testId : !c.testId)
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredClasses = filteredClasses.filter(c => 
            c.title.toLowerCase().includes(searchLower) ||
            c.description?.toLowerCase().includes(searchLower) ||
            c.subjectName.toLowerCase().includes(searchLower) ||
            c.courseName.toLowerCase().includes(searchLower)
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredClasses.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredClasses)
    }, 100)
  })
}

export const getClassById = (id: string): Promise<Class | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const classItem = mockClasses.find(c => c.id === id) || null
      resolve(classItem)
    }, 100)
  })
}

export const getClassStats = (): Promise<ClassStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: ClassStats = {
        totalClasses: mockClasses.length,
        publishedClasses: mockClasses.filter(c => c.isPublished).length,
        totalDuration: mockClasses.reduce((sum, c) => sum + c.duration, 0),
        averageCompletion: mockClasses.reduce((sum, c) => sum + c.completionRate, 0) / mockClasses.length,
        totalViews: mockClasses.reduce((sum, c) => sum + c.viewCount, 0)
      }
      resolve(stats)
    }, 100)
  })
}

export const createClass = (data: CreateClassRequest): Promise<Class> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Find subject and course names (in real app, this would be a proper lookup)
      const subjectNames: Record<string, { subjectName: string, courseId: string, courseName: string }> = {
        '1': { subjectName: 'Mechanics', courseId: '1', courseName: 'JEE Main Physics Complete Course' },
        '3': { subjectName: 'Botany', courseId: '2', courseName: 'NEET Biology Masterclass' },
        '5': { subjectName: 'Calculus', courseId: '3', courseName: 'JEE Advanced Mathematics' },
        '8': { subjectName: 'Organic Chemistry Basics', courseId: '5', courseName: 'Free NEET Chemistry Basics' }
      }
      
      const subjectInfo = subjectNames[data.subjectId] || { subjectName: 'Unknown Subject', courseId: '1', courseName: 'Unknown Course' }
      
      const newClass: Class = {
        id: Date.now().toString(),
        ...data,
        ...subjectInfo,
        viewCount: 0,
        completionRate: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        resources: data.resources || []
      }
      mockClasses.push(newClass)
      resolve(newClass)
    }, 200)
  })
}

export const updateClass = (data: UpdateClassRequest): Promise<Class> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockClasses.findIndex(c => c.id === data.id)
      if (index === -1) {
        reject(new Error('Class not found'))
        return
      }
      
      mockClasses[index] = {
        ...mockClasses[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockClasses[index])
    }, 200)
  })
}

export const deleteClass = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockClasses.findIndex(c => c.id === id)
      if (index === -1) {
        reject(new Error('Class not found'))
        return
      }
      
      mockClasses.splice(index, 1)
      resolve()
    }, 200)
  })
}
