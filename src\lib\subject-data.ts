import { Subject, CreateSubjectRequest, UpdateSubjectRequest, SubjectFilters } from '@/types/subject'

// Mock data - In real app, this would come from your API
const mockSubjects: Subject[] = [
  {
    id: '1',
    name: 'Mechanics',
    description: 'Classical mechanics, motion, forces, and energy',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    examId: '1',
    examName: 'JEE Main',
    order: 1,
    isActive: true,
    totalClasses: 25,
    totalDuration: 1200, // 20 hours
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    color: '#3B82F6',
    icon: '⚙️'
  },
  {
    id: '2',
    name: 'Thermodynamics',
    description: 'Heat, temperature, and energy transfer',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    examId: '1',
    examName: 'JEE Main',
    order: 2,
    isActive: true,
    totalClasses: 18,
    totalDuration: 900, // 15 hours
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    color: '#EF4444',
    icon: '🔥'
  },
  {
    id: '3',
    name: 'Bo<PERSON>',
    description: 'Plant biology, structure, and functions',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    examId: '3',
    examName: 'NEET UG',
    order: 1,
    isActive: true,
    totalClasses: 30,
    totalDuration: 1500, // 25 hours
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    color: '#10B981',
    icon: '🌱'
  },
  {
    id: '4',
    name: 'Zoology',
    description: 'Animal biology, anatomy, and physiology',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    examId: '3',
    examName: 'NEET UG',
    order: 2,
    isActive: true,
    totalClasses: 28,
    totalDuration: 1400, // 23.3 hours
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    color: '#F59E0B',
    icon: '🦁'
  },
  {
    id: '5',
    name: 'Calculus',
    description: 'Differential and integral calculus',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    examId: '2',
    examName: 'JEE Advanced',
    order: 1,
    isActive: true,
    totalClasses: 22,
    totalDuration: 1100, // 18.3 hours
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    color: '#8B5CF6',
    icon: '📊'
  },
  {
    id: '6',
    name: 'Algebra',
    description: 'Linear algebra, matrices, and equations',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    examId: '2',
    examName: 'JEE Advanced',
    order: 2,
    isActive: true,
    totalClasses: 20,
    totalDuration: 1000, // 16.7 hours
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    color: '#EC4899',
    icon: '🔢'
  },
  {
    id: '7',
    name: 'Optics',
    description: 'Light, reflection, refraction, and wave optics',
    courseId: '4',
    courseName: 'Class 12 Physics CBSE',
    examId: '4',
    examName: 'Class 12 CBSE',
    order: 1,
    isActive: true,
    totalClasses: 15,
    totalDuration: 750, // 12.5 hours
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
    color: '#06B6D4',
    icon: '🔍'
  },
  {
    id: '8',
    name: 'Organic Chemistry Basics',
    description: 'Introduction to organic chemistry concepts',
    courseId: '5',
    courseName: 'Free NEET Chemistry Basics',
    examId: '3',
    examName: 'NEET UG',
    order: 1,
    isActive: true,
    totalClasses: 12,
    totalDuration: 600, // 10 hours
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    color: '#84CC16',
    icon: '🧪'
  }
]

export const getSubjects = (filters?: SubjectFilters): Promise<Subject[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredSubjects = [...mockSubjects]
      
      if (filters) {
        if (filters.courseId) {
          filteredSubjects = filteredSubjects.filter(s => s.courseId === filters.courseId)
        }
        if (filters.examId) {
          filteredSubjects = filteredSubjects.filter(s => s.examId === filters.examId)
        }
        if (filters.isActive !== undefined) {
          filteredSubjects = filteredSubjects.filter(s => s.isActive === filters.isActive)
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredSubjects = filteredSubjects.filter(s => 
            s.name.toLowerCase().includes(searchLower) ||
            s.description.toLowerCase().includes(searchLower) ||
            s.courseName.toLowerCase().includes(searchLower)
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredSubjects.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredSubjects)
    }, 100)
  })
}

export const getSubjectById = (id: string): Promise<Subject | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const subject = mockSubjects.find(s => s.id === id) || null
      resolve(subject)
    }, 100)
  })
}

export const createSubject = (data: CreateSubjectRequest): Promise<Subject> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Find course and exam names (in real app, this would be a proper lookup)
      const courseNames: Record<string, { courseName: string, examId: string, examName: string }> = {
        '1': { courseName: 'JEE Main Physics Complete Course', examId: '1', examName: 'JEE Main' },
        '2': { courseName: 'NEET Biology Masterclass', examId: '3', examName: 'NEET UG' },
        '3': { courseName: 'JEE Advanced Mathematics', examId: '2', examName: 'JEE Advanced' },
        '4': { courseName: 'Class 12 Physics CBSE', examId: '4', examName: 'Class 12 CBSE' },
        '5': { courseName: 'Free NEET Chemistry Basics', examId: '3', examName: 'NEET UG' }
      }
      
      const courseInfo = courseNames[data.courseId] || { courseName: 'Unknown Course', examId: '1', examName: 'Unknown Exam' }
      
      const newSubject: Subject = {
        id: Date.now().toString(),
        ...data,
        ...courseInfo,
        totalClasses: 0,
        totalDuration: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      mockSubjects.push(newSubject)
      resolve(newSubject)
    }, 200)
  })
}

export const updateSubject = (data: UpdateSubjectRequest): Promise<Subject> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockSubjects.findIndex(s => s.id === data.id)
      if (index === -1) {
        reject(new Error('Subject not found'))
        return
      }
      
      mockSubjects[index] = {
        ...mockSubjects[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockSubjects[index])
    }, 200)
  })
}

export const deleteSubject = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockSubjects.findIndex(s => s.id === id)
      if (index === -1) {
        reject(new Error('Subject not found'))
        return
      }
      
      mockSubjects.splice(index, 1)
      resolve()
    }, 200)
  })
}
