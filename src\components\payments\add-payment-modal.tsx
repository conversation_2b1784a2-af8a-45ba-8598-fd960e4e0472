'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X, Plus, CheckCircle, AlertCircle, CreditCard, DollarSign, User, Package, Receipt } from 'lucide-react'
import { CreatePaymentRequest } from '@/types/payment'
import { Student } from '@/types/student'

interface AddPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (data: CreatePaymentRequest) => Promise<void>
  students: Student[]
}

export function AddPaymentModal({ isOpen, onClose, onAdd, students }: AddPaymentModalProps) {
  const [formData, setFormData] = useState<Partial<CreatePaymentRequest>>({
    currency: 'INR',
    paymentMethod: 'UPI',
    paymentGateway: 'RAZORPAY',
    itemType: 'COURSE',
    tags: []
  })
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [error, setError] = useState<string>()
  const [tagInput, setTagInput] = useState('')

  const paymentMethods = [
    { value: 'UPI', label: 'UPI' },
    { value: 'CREDIT_CARD', label: 'Credit Card' },
    { value: 'DEBIT_CARD', label: 'Debit Card' },
    { value: 'NET_BANKING', label: 'Net Banking' },
    { value: 'WALLET', label: 'Wallet' },
    { value: 'CASH', label: 'Cash' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' }
  ]

  const paymentGateways = [
    { value: 'RAZORPAY', label: 'Razorpay' },
    { value: 'STRIPE', label: 'Stripe' },
    { value: 'PAYU', label: 'PayU' },
    { value: 'PAYPAL', label: 'PayPal' },
    { value: 'PHONEPE', label: 'PhonePe' },
    { value: 'GPAY', label: 'Google Pay' },
    { value: 'PAYTM', label: 'Paytm' },
    { value: 'MANUAL', label: 'Manual Entry' }
  ]

  const itemTypes = [
    { value: 'COURSE', label: 'Course', description: 'Individual course purchase' },
    { value: 'SUBSCRIPTION', label: 'Subscription', description: 'Subscription plan' },
    { value: 'TEST_SERIES', label: 'Test Series', description: 'Test series package' },
    { value: 'BOOK', label: 'Book', description: 'Digital or physical book' },
    { value: 'LIVE_CLASS', label: 'Live Class', description: 'Live class session' },
    { value: 'CONSULTATION', label: 'Consultation', description: 'One-on-one consultation' },
    { value: 'OTHER', label: 'Other', description: 'Other items' }
  ]

  const currencies = [
    { value: 'INR', label: 'INR (₹)', symbol: '₹' },
    { value: 'USD', label: 'USD ($)', symbol: '$' },
    { value: 'EUR', label: 'EUR (€)', symbol: '€' }
  ]

  const subscriptionTypes = [
    { value: 'MONTHLY', label: 'Monthly' },
    { value: 'QUARTERLY', label: 'Quarterly' },
    { value: 'HALF_YEARLY', label: 'Half Yearly' },
    { value: 'YEARLY', label: 'Yearly' },
    { value: 'LIFETIME', label: 'Lifetime' }
  ]

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.studentId || !formData.itemName || !formData.amount) {
      setError('Please fill in all required fields')
      return
    }

    if (formData.amount <= 0) {
      setError('Amount must be greater than 0')
      return
    }

    setLoading(true)
    setError(undefined)

    try {
      const paymentData: CreatePaymentRequest = {
        studentId: formData.studentId!,
        itemType: formData.itemType!,
        itemId: formData.itemId,
        itemName: formData.itemName!,
        itemDescription: formData.itemDescription,
        amount: formData.amount!,
        currency: formData.currency!,
        paymentMethod: formData.paymentMethod!,
        paymentGateway: formData.paymentGateway!,
        subscriptionType: formData.subscriptionType,
        discountCode: formData.discountCode,
        notes: formData.notes,
        tags: formData.tags || []
      }

      await onAdd(paymentData)
      setCompleted(true)
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add payment')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      currency: 'INR',
      paymentMethod: 'UPI',
      paymentGateway: 'RAZORPAY',
      itemType: 'COURSE',
      tags: []
    })
    setLoading(false)
    setCompleted(false)
    setError(undefined)
    setTagInput('')
  }

  const selectedStudent = students.find(s => s.id === formData.studentId)
  const currencySymbol = currencies.find(c => c.value === formData.currency)?.symbol || '₹'

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold flex items-center space-x-2">
            <CreditCard className="w-5 h-5 text-blue-600" />
            <span>Add Manual Payment</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Payment Added Successfully!</h3>
              <p className="text-gray-600">The payment has been recorded and processed.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              {/* Student Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Student Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Student *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Select 
                      value={formData.studentId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, studentId: value }))}
                    >
                      <SelectTrigger className="pl-10">
                        <SelectValue placeholder="Choose a student" />
                      </SelectTrigger>
                      <SelectContent>
                        {students.map((student) => (
                          <SelectItem key={student.id} value={student.id}>
                            <div>
                              <div className="font-medium">{student.firstName} {student.lastName}</div>
                              <div className="text-xs text-gray-500">{student.email}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {selectedStudent && (
                    <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-sm">
                          {selectedStudent.firstName.charAt(0)}{selectedStudent.lastName.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium">{selectedStudent.firstName} {selectedStudent.lastName}</div>
                          <div className="text-sm text-gray-600">{selectedStudent.email}</div>
                          {selectedStudent.phone && (
                            <div className="text-sm text-gray-600">{selectedStudent.phone}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Item Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Item Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Item Type *
                    </label>
                    <div className="relative">
                      <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Select 
                        value={formData.itemType} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, itemType: value as any }))}
                      >
                        <SelectTrigger className="pl-10">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {itemTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-xs text-gray-500">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Item ID
                    </label>
                    <Input
                      value={formData.itemId || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, itemId: e.target.value }))}
                      placeholder="e.g., COURSE_001"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Item Name *
                  </label>
                  <Input
                    value={formData.itemName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, itemName: e.target.value }))}
                    placeholder="e.g., JEE Main Physics Complete Course"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Item Description
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    value={formData.itemDescription || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, itemDescription: e.target.value }))}
                    placeholder="Brief description of the item..."
                  />
                </div>

                {formData.itemType === 'SUBSCRIPTION' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Subscription Type
                    </label>
                    <Select 
                      value={formData.subscriptionType || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, subscriptionType: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select subscription type" />
                      </SelectTrigger>
                      <SelectContent>
                        {subscriptionTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              {/* Payment Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Payment Details</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Amount *
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        type="number"
                        value={formData.amount || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                        placeholder="0.00"
                        className="pl-10"
                        step="0.01"
                        min="0"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency *
                    </label>
                    <Select 
                      value={formData.currency} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.map((currency) => (
                          <SelectItem key={currency.value} value={currency.value}>
                            {currency.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Discount Code
                    </label>
                    <Input
                      value={formData.discountCode || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, discountCode: e.target.value }))}
                      placeholder="e.g., SAVE20"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Payment Method *
                    </label>
                    <Select 
                      value={formData.paymentMethod} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, paymentMethod: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Payment Gateway *
                    </label>
                    <Select 
                      value={formData.paymentGateway} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, paymentGateway: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentGateways.map((gateway) => (
                          <SelectItem key={gateway.value} value={gateway.value}>
                            {gateway.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.notes || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Any additional notes about this payment..."
                  />
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tags
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add a tag"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                    />
                    <Button type="button" onClick={handleAddTag} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags?.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                        <span>{tag}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Payment Summary */}
              {formData.amount && (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Summary</h4>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Amount:</span>
                      <span className="font-medium">{currencySymbol}{formData.amount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Method:</span>
                      <span>{formData.paymentMethod?.replace('_', ' ')}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Gateway:</span>
                      <span>{formData.paymentGateway}</span>
                    </div>
                    {formData.discountCode && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Discount Code:</span>
                        <span>{formData.discountCode}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <Receipt className="w-4 h-4" />
                  <span>{loading ? 'Processing...' : 'Add Payment'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
