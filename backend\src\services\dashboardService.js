const db = require('../config/database');
const jwt = require('jsonwebtoken');
const { AppError } = require('../utils/errorHandler');
const { calculateDateRange, formatCurrency, calculatePercentageChange } = require('../utils/helpers');
const cacheService = require('./cacheService');
const realTimeService = require('./realTimeService');

/**
 * Dashboard Service
 * Contains all business logic for dashboard operations
 */
class DashboardService {
  /**
   * Get comprehensive dashboard overview data
   */
  async getOverviewData({ period, comparePrevious, timezone, userId }) {
    try {
      const cacheKey = `dashboard_overview_${userId}_${period}_${comparePrevious}`;
      const cachedData = await cacheService.get(cacheKey);

      if (cachedData) {
        return cachedData;
      }

      const dateRange = calculateDateRange(period, timezone);
      const previousDateRange = comparePrevious ? calculateDateRange(period, timezone, true) : null;

      // Get key metrics
      const keyMetrics = await this.getKeyMetrics(dateRange, previousDateRange);

      // Get growth metrics
      const growthMetrics = await this.getGrowthMetrics(dateRange, previousDateRange);

      // Get recent activities
      const recentActivities = await this.getRecentActivities(10);

      // Get revenue chart data
      const revenueChart = await this.getRevenueChartData(dateRange);

      // Get top courses
      const topCourses = await this.getTopCourses(5);

      // Get upcoming events
      const upcomingEvents = await this.getUpcomingEvents(10);

      // Get alerts
      const alerts = await this.getSystemAlerts();

      const overviewData = {
        key_metrics: keyMetrics,
        growth_metrics: growthMetrics,
        recent_activities: recentActivities,
        revenue_chart: revenueChart,
        top_courses: topCourses,
        upcoming_events: upcomingEvents,
        alerts: alerts
      };

      // Cache for 5 minutes
      await cacheService.set(cacheKey, overviewData, 300);

      return overviewData;
    } catch (error) {
      throw new AppError(`Failed to get overview data: ${error.message}`, 500);
    }
  }

  /**
   * Get key metrics for dashboard
   */
  async getKeyMetrics(dateRange, previousDateRange) {
    const { startDate, endDate } = dateRange;

    // Current period metrics
    const [
      totalStudents,
      activeStudents,
      totalCourses,
      activeCourses,
      totalRevenue,
      monthlyRevenue,
      totalTests,
      testsThisMonth,
      liveClassesToday,
      upcomingClasses
    ] = await Promise.all([
      this.getTotalStudents(endDate),
      this.getActiveStudents(startDate, endDate),
      this.getTotalCourses(endDate),
      this.getActiveCourses(endDate),
      this.getTotalRevenue(startDate, endDate),
      this.getMonthlyRevenue(startDate, endDate),
      this.getTotalTests(startDate, endDate),
      this.getTestsThisMonth(),
      this.getLiveClassesToday(),
      this.getUpcomingClasses()
    ]);

    let previousMetrics = {};
    if (previousDateRange) {
      const { startDate: prevStart, endDate: prevEnd } = previousDateRange;

      const [
        prevTotalStudents,
        prevActiveStudents,
        prevTotalCourses,
        prevActiveCourses,
        prevTotalRevenue,
        prevMonthlyRevenue,
        prevTotalTests,
        prevTestsThisMonth,
        prevLiveClassesToday,
        prevUpcomingClasses
      ] = await Promise.all([
        this.getTotalStudents(prevEnd),
        this.getActiveStudents(prevStart, prevEnd),
        this.getTotalCourses(prevEnd),
        this.getActiveCourses(prevEnd),
        this.getTotalRevenue(prevStart, prevEnd),
        this.getMonthlyRevenue(prevStart, prevEnd),
        this.getTotalTests(prevStart, prevEnd),
        this.getTestsThisMonth(prevStart, prevEnd),
        this.getLiveClassesToday(prevStart),
        this.getUpcomingClasses(prevStart)
      ]);

      previousMetrics = {
        totalStudents: prevTotalStudents,
        activeStudents: prevActiveStudents,
        totalCourses: prevTotalCourses,
        activeCourses: prevActiveCourses,
        totalRevenue: prevTotalRevenue,
        monthlyRevenue: prevMonthlyRevenue,
        totalTests: prevTotalTests,
        testsThisMonth: prevTestsThisMonth,
        liveClassesToday: prevLiveClassesToday,
        upcomingClasses: prevUpcomingClasses
      };
    }

    return {
      total_students: {
        value: totalStudents,
        change: previousMetrics.totalStudents ? calculatePercentageChange(totalStudents, previousMetrics.totalStudents) : 0,
        change_type: this.getChangeType(totalStudents, previousMetrics.totalStudents),
        previous_value: previousMetrics.totalStudents || 0
      },
      active_students: {
        value: activeStudents,
        change: previousMetrics.activeStudents ? calculatePercentageChange(activeStudents, previousMetrics.activeStudents) : 0,
        change_type: this.getChangeType(activeStudents, previousMetrics.activeStudents),
        previous_value: previousMetrics.activeStudents || 0
      },
      total_courses: {
        value: totalCourses,
        change: previousMetrics.totalCourses ? calculatePercentageChange(totalCourses, previousMetrics.totalCourses) : 0,
        change_type: this.getChangeType(totalCourses, previousMetrics.totalCourses),
        previous_value: previousMetrics.totalCourses || 0
      },
      active_courses: {
        value: activeCourses,
        change: previousMetrics.activeCourses ? calculatePercentageChange(activeCourses, previousMetrics.activeCourses) : 0,
        change_type: this.getChangeType(activeCourses, previousMetrics.activeCourses),
        previous_value: previousMetrics.activeCourses || 0
      },
      total_revenue: {
        value: totalRevenue,
        change: previousMetrics.totalRevenue ? calculatePercentageChange(totalRevenue, previousMetrics.totalRevenue) : 0,
        change_type: this.getChangeType(totalRevenue, previousMetrics.totalRevenue),
        previous_value: previousMetrics.totalRevenue || 0,
        currency: 'INR'
      },
      monthly_revenue: {
        value: monthlyRevenue,
        change: previousMetrics.monthlyRevenue ? calculatePercentageChange(monthlyRevenue, previousMetrics.monthlyRevenue) : 0,
        change_type: this.getChangeType(monthlyRevenue, previousMetrics.monthlyRevenue),
        previous_value: previousMetrics.monthlyRevenue || 0,
        currency: 'INR'
      },
      total_tests: {
        value: totalTests,
        change: previousMetrics.totalTests ? calculatePercentageChange(totalTests, previousMetrics.totalTests) : 0,
        change_type: this.getChangeType(totalTests, previousMetrics.totalTests),
        previous_value: previousMetrics.totalTests || 0
      },
      tests_this_month: {
        value: testsThisMonth,
        change: previousMetrics.testsThisMonth ? calculatePercentageChange(testsThisMonth, previousMetrics.testsThisMonth) : 0,
        change_type: this.getChangeType(testsThisMonth, previousMetrics.testsThisMonth),
        previous_value: previousMetrics.testsThisMonth || 0
      },
      live_classes_today: {
        value: liveClassesToday,
        change: previousMetrics.liveClassesToday ? calculatePercentageChange(liveClassesToday, previousMetrics.liveClassesToday) : 0,
        change_type: this.getChangeType(liveClassesToday, previousMetrics.liveClassesToday),
        previous_value: previousMetrics.liveClassesToday || 0
      },
      upcoming_classes: {
        value: upcomingClasses,
        change: previousMetrics.upcomingClasses ? calculatePercentageChange(upcomingClasses, previousMetrics.upcomingClasses) : 0,
        change_type: this.getChangeType(upcomingClasses, previousMetrics.upcomingClasses),
        previous_value: previousMetrics.upcomingClasses || 0
      }
    };
  }

  /**
   * Get change type based on current and previous values
   */
  getChangeType(current, previous) {
    if (!previous || current === previous) return 'neutral';
    return current > previous ? 'increase' : 'decrease';
  }

  /**
   * Get total students count
   */
  async getTotalStudents(endDate) {
    const query = `
      SELECT COUNT(*) as count 
      FROM students 
      WHERE created_at <= ? AND deleted_at IS NULL
    `;
    const result = await db.query(query, [endDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get active students count
   */
  async getActiveStudents(startDate, endDate) {
    const query = `
      SELECT COUNT(DISTINCT s.id) as count 
      FROM students s
      INNER JOIN enrollments e ON s.id = e.student_id
      WHERE e.status = 'active' 
        AND e.created_at BETWEEN ? AND ?
        AND s.deleted_at IS NULL
    `;
    const result = await db.query(query, [startDate, endDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get total courses count
   */
  async getTotalCourses(endDate) {
    const query = `
      SELECT COUNT(*) as count 
      FROM courses 
      WHERE created_at <= ? AND deleted_at IS NULL
    `;
    const result = await db.query(query, [endDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get active courses count
   */
  async getActiveCourses(endDate) {
    const query = `
      SELECT COUNT(*) as count 
      FROM courses 
      WHERE status = 'published' 
        AND created_at <= ? 
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [endDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get total revenue
   */
  async getTotalRevenue(startDate, endDate) {
    const query = `
      SELECT COALESCE(SUM(amount), 0) as total 
      FROM payments 
      WHERE status = 'completed' 
        AND payment_date BETWEEN ? AND ?
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [startDate, endDate]);
    return result[0]?.total || 0;
  }

  /**
   * Get monthly revenue
   */
  async getMonthlyRevenue(startDate, endDate) {
    const query = `
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments
      WHERE status = 'completed'
        AND payment_date >= DATE_FORMAT(?, '%Y-%m-01')
        AND payment_date <= ?
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [endDate, endDate]);
    return result[0]?.total || 0;
  }

  /**
   * Get total tests count
   */
  async getTotalTests(startDate, endDate) {
    const query = `
      SELECT COUNT(*) as count
      FROM tests
      WHERE created_at BETWEEN ? AND ?
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [startDate, endDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get tests this month count
   */
  async getTestsThisMonth(startDate = null, endDate = null) {
    const currentDate = endDate || new Date().toISOString();
    const monthStart = startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString();

    const query = `
      SELECT COUNT(*) as count
      FROM tests
      WHERE created_at >= ?
        AND created_at <= ?
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [monthStart, currentDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get live classes today count
   */
  async getLiveClassesToday(date = null) {
    const today = date || new Date().toISOString().split('T')[0];

    const query = `
      SELECT COUNT(*) as count
      FROM live_classes
      WHERE DATE(start_time) = ?
        AND status IN ('scheduled', 'live')
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [today]);
    return result[0]?.count || 0;
  }

  /**
   * Get upcoming classes count
   */
  async getUpcomingClasses(date = null) {
    const currentDate = date || new Date().toISOString();

    const query = `
      SELECT COUNT(*) as count
      FROM live_classes
      WHERE start_time > ?
        AND status = 'scheduled'
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [currentDate]);
    return result[0]?.count || 0;
  }

  /**
   * Get growth metrics
   */
  async getGrowthMetrics(dateRange, previousDateRange) {
    const { startDate, endDate } = dateRange;

    // Calculate growth metrics
    const [
      studentGrowth,
      revenueGrowth,
      completionRate,
      averageScore
    ] = await Promise.all([
      this.calculateStudentGrowth(startDate, endDate, previousDateRange),
      this.calculateRevenueGrowth(startDate, endDate, previousDateRange),
      this.calculateCourseCompletionRate(startDate, endDate),
      this.calculateAverageScore(startDate, endDate)
    ]);

    return {
      student_growth: {
        percentage: studentGrowth.percentage,
        trend: studentGrowth.trend,
        target: 20.0,
        achievement: (studentGrowth.percentage / 20.0) * 100
      },
      revenue_growth: {
        percentage: revenueGrowth.percentage,
        trend: revenueGrowth.trend,
        target: 25.0,
        achievement: (revenueGrowth.percentage / 25.0) * 100
      },
      course_completion_rate: {
        percentage: completionRate,
        trend: completionRate >= 75 ? 'upward' : 'downward',
        target: 80.0,
        achievement: (completionRate / 80.0) * 100
      },
      average_score: {
        percentage: averageScore,
        trend: averageScore >= 75 ? 'upward' : 'downward',
        target: 75.0,
        achievement: (averageScore / 75.0) * 100
      }
    };
  }

  /**
   * Calculate student growth
   */
  async calculateStudentGrowth(startDate, endDate, previousDateRange) {
    const currentStudents = await this.getActiveStudents(startDate, endDate);

    if (!previousDateRange) {
      return { percentage: 0, trend: 'neutral' };
    }

    const { startDate: prevStart, endDate: prevEnd } = previousDateRange;
    const previousStudents = await this.getActiveStudents(prevStart, prevEnd);

    const percentage = calculatePercentageChange(currentStudents, previousStudents);
    const trend = percentage > 0 ? 'upward' : percentage < 0 ? 'downward' : 'neutral';

    return { percentage, trend };
  }

  /**
   * Calculate revenue growth
   */
  async calculateRevenueGrowth(startDate, endDate, previousDateRange) {
    const currentRevenue = await this.getTotalRevenue(startDate, endDate);

    if (!previousDateRange) {
      return { percentage: 0, trend: 'neutral' };
    }

    const { startDate: prevStart, endDate: prevEnd } = previousDateRange;
    const previousRevenue = await this.getTotalRevenue(prevStart, prevEnd);

    const percentage = calculatePercentageChange(currentRevenue, previousRevenue);
    const trend = percentage > 0 ? 'upward' : percentage < 0 ? 'downward' : 'neutral';

    return { percentage, trend };
  }

  /**
   * Calculate course completion rate
   */
  async calculateCourseCompletionRate(startDate, endDate) {
    const query = `
      SELECT
        COUNT(*) as total_enrollments,
        SUM(CASE WHEN progress >= 100 THEN 1 ELSE 0 END) as completed_enrollments
      FROM enrollments
      WHERE created_at BETWEEN ? AND ?
        AND deleted_at IS NULL
    `;
    const result = await db.query(query, [startDate, endDate]);

    const { total_enrollments, completed_enrollments } = result[0] || {};

    if (!total_enrollments || total_enrollments === 0) {
      return 0;
    }

    return (completed_enrollments / total_enrollments) * 100;
  }

  /**
   * Calculate average score
   */
  async calculateAverageScore(startDate, endDate) {
    const query = `
      SELECT AVG(score) as average_score
      FROM test_results tr
      INNER JOIN tests t ON tr.test_id = t.id
      WHERE t.created_at BETWEEN ? AND ?
        AND tr.deleted_at IS NULL
        AND t.deleted_at IS NULL
    `;
    const result = await db.query(query, [startDate, endDate]);
    return result[0]?.average_score || 0;
  }

  /**
   * Get recent activities
   */
  async getRecentActivities(limit = 10) {
    const query = `
      SELECT
        'student_enrollment' as type,
        CONCAT('New Student Enrolled') as title,
        CONCAT(s.first_name, ' ', s.last_name, ' enrolled in ', c.title) as description,
        e.created_at as timestamp,
        s.id as user_id,
        CONCAT(s.first_name, ' ', s.last_name) as user_name,
        s.avatar as user_avatar,
        JSON_OBJECT(
          'course_name', c.title,
          'amount', p.amount,
          'payment_status', p.status
        ) as metadata,
        'medium' as priority,
        'enrollment' as category
      FROM enrollments e
      INNER JOIN students s ON e.student_id = s.id
      INNER JOIN courses c ON e.course_id = c.id
      LEFT JOIN payments p ON e.id = p.enrollment_id
      WHERE e.deleted_at IS NULL

      UNION ALL

      SELECT
        'payment_received' as type,
        'Payment Received' as title,
        CONCAT('₹', FORMAT(p.amount, 0), ' payment received from ', s.first_name, ' ', s.last_name) as description,
        p.payment_date as timestamp,
        s.id as user_id,
        CONCAT(s.first_name, ' ', s.last_name) as user_name,
        s.avatar as user_avatar,
        JSON_OBJECT(
          'amount', p.amount,
          'payment_method', p.payment_method,
          'transaction_id', p.transaction_id
        ) as metadata,
        'high' as priority,
        'payment' as category
      FROM payments p
      INNER JOIN students s ON p.student_id = s.id
      WHERE p.status = 'completed' AND p.deleted_at IS NULL

      UNION ALL

      SELECT
        'test_completed' as type,
        'Mock Test Completed' as title,
        CONCAT(t.title, ' completed by ', COUNT(tr.id), ' students') as description,
        MAX(tr.completed_at) as timestamp,
        NULL as user_id,
        NULL as user_name,
        NULL as user_avatar,
        JSON_OBJECT(
          'test_name', t.title,
          'students_appeared', COUNT(tr.id),
          'average_score', AVG(tr.score),
          'highest_score', MAX(tr.score)
        ) as metadata,
        'medium' as priority,
        'test' as category
      FROM tests t
      INNER JOIN test_results tr ON t.id = tr.test_id
      WHERE t.deleted_at IS NULL AND tr.deleted_at IS NULL
      GROUP BY t.id, t.title

      ORDER BY timestamp DESC
      LIMIT ?
    `;

    const result = await db.query(query, [limit]);

    return result.map(activity => ({
      id: `activity_${Date.now()}_${Math.random()}`,
      type: activity.type,
      title: activity.title,
      description: activity.description,
      timestamp: activity.timestamp,
      user: activity.user_id ? {
        id: activity.user_id,
        name: activity.user_name,
        avatar: activity.user_avatar || `https://cdn.utkrishta.com/avatars/default.jpg`
      } : null,
      metadata: typeof activity.metadata === 'string' ? JSON.parse(activity.metadata) : activity.metadata,
      priority: activity.priority,
      category: activity.category
    }));
  }

  /**
   * Get revenue chart data
   */
  async getRevenueChartData(dateRange) {
    const { startDate, endDate } = dateRange;

    const query = `
      SELECT
        DATE(payment_date) as date,
        SUM(amount) as revenue,
        COUNT(DISTINCT student_id) as students,
        COUNT(*) as courses_sold,
        SUM(CASE WHEN refund_amount > 0 THEN refund_amount ELSE 0 END) as refunds
      FROM payments
      WHERE status = 'completed'
        AND payment_date BETWEEN ? AND ?
        AND deleted_at IS NULL
      GROUP BY DATE(payment_date)
      ORDER BY date ASC
    `;

    const result = await db.query(query, [startDate, endDate]);

    const totalRevenue = result.reduce((sum, row) => sum + (row.revenue || 0), 0);
    const totalDays = result.length || 1;
    const averageDaily = totalRevenue / totalDays;

    const peakDay = result.reduce((max, row) =>
      (row.revenue || 0) > (max.revenue || 0) ? row : max,
      { date: null, revenue: 0 }
    );

    return {
      period: '30d',
      currency: 'INR',
      data: result.map(row => ({
        date: row.date,
        revenue: row.revenue || 0,
        students: row.students || 0,
        courses_sold: row.courses_sold || 0,
        refunds: row.refunds || 0
      })),
      total_revenue: totalRevenue,
      average_daily: Math.round(averageDaily),
      peak_day: {
        date: peakDay.date,
        revenue: peakDay.revenue || 0
      }
    };
  }

  /**
   * Get top courses
   */
  async getTopCourses(limit = 5) {
    const query = `
      SELECT
        c.id,
        c.title as name,
        CONCAT(i.first_name, ' ', i.last_name) as instructor,
        COUNT(e.id) as enrollments,
        SUM(p.amount) as revenue,
        AVG(r.rating) as rating,
        AVG(e.progress) as completion_rate,
        (COUNT(e.id) - LAG(COUNT(e.id), 1, 0) OVER (ORDER BY COUNT(e.id) DESC)) /
        NULLIF(LAG(COUNT(e.id), 1, 1) OVER (ORDER BY COUNT(e.id) DESC), 0) * 100 as growth
      FROM courses c
      INNER JOIN instructors i ON c.instructor_id = i.id
      LEFT JOIN enrollments e ON c.id = e.course_id AND e.deleted_at IS NULL
      LEFT JOIN payments p ON e.id = p.enrollment_id AND p.status = 'completed'
      LEFT JOIN course_reviews r ON c.id = r.course_id AND r.deleted_at IS NULL
      WHERE c.status = 'published' AND c.deleted_at IS NULL
      GROUP BY c.id, c.title, i.first_name, i.last_name
      ORDER BY enrollments DESC, revenue DESC
      LIMIT ?
    `;

    const result = await db.query(query, [limit]);

    return result.map(course => ({
      id: course.id,
      name: course.name,
      instructor: course.instructor,
      enrollments: course.enrollments || 0,
      revenue: course.revenue || 0,
      rating: parseFloat((course.rating || 0).toFixed(1)),
      completion_rate: parseFloat((course.completion_rate || 0).toFixed(1)),
      growth: parseFloat((course.growth || 0).toFixed(1))
    }));
  }

  /**
   * Get upcoming events
   */
  async getUpcomingEvents(limit = 10) {
    const query = `
      SELECT
        'live_class' as type,
        lc.id,
        lc.title,
        lc.start_time as scheduled_at,
        lc.duration,
        CONCAT(i.first_name, ' ', i.last_name) as instructor_name,
        i.id as instructor_id,
        i.avatar as instructor_avatar,
        COUNT(e.id) as students_enrolled,
        c.title as course,
        lc.meeting_url,
        lc.status
      FROM live_classes lc
      INNER JOIN courses c ON lc.course_id = c.id
      INNER JOIN instructors i ON c.instructor_id = i.id
      LEFT JOIN enrollments e ON c.id = e.course_id AND e.status = 'active'
      WHERE lc.start_time > NOW()
        AND lc.status = 'scheduled'
        AND lc.deleted_at IS NULL
      GROUP BY lc.id

      UNION ALL

      SELECT
        'test' as type,
        t.id,
        t.title,
        t.start_time as scheduled_at,
        t.duration,
        NULL as instructor_name,
        NULL as instructor_id,
        NULL as instructor_avatar,
        COUNT(tr.id) as students_registered,
        NULL as course,
        NULL as meeting_url,
        t.status
      FROM tests t
      LEFT JOIN test_registrations tr ON t.id = tr.test_id
      WHERE t.start_time > NOW()
        AND t.status = 'upcoming'
        AND t.deleted_at IS NULL
      GROUP BY t.id

      ORDER BY scheduled_at ASC
      LIMIT ?
    `;

    const result = await db.query(query, [limit]);

    return result.map(event => ({
      id: event.id,
      type: event.type,
      title: event.title,
      scheduled_at: event.scheduled_at,
      duration: event.duration,
      instructor: event.instructor_name ? {
        id: event.instructor_id,
        name: event.instructor_name,
        avatar: event.instructor_avatar || 'https://cdn.utkrishta.com/instructors/default.jpg'
      } : null,
      students_enrolled: event.students_enrolled || 0,
      students_registered: event.students_registered || 0,
      course: event.course,
      meeting_url: event.meeting_url,
      status: event.status,
      total_marks: event.type === 'test' ? 720 : null
    }));
  }

  /**
   * Get system alerts
   */
  async getSystemAlerts() {
    const alerts = [];

    // Check for overdue payments
    const overduePayments = await this.getOverduePaymentsCount();
    if (overduePayments > 0) {
      alerts.push({
        id: 'overdue_payments',
        type: 'warning',
        title: 'Overdue Payments',
        message: `${overduePayments} payments are overdue and require attention`,
        action_url: '/payments?status=overdue',
        action_text: 'View Overdue Payments',
        created_at: new Date().toISOString()
      });
    }

    // Check for low attendance classes
    const lowAttendanceClasses = await this.getLowAttendanceClassesCount();
    if (lowAttendanceClasses > 0) {
      alerts.push({
        id: 'low_attendance',
        type: 'info',
        title: 'Low Attendance Alert',
        message: `${lowAttendanceClasses} classes have attendance below 70%`,
        action_url: '/live-classes?attendance=low',
        action_text: 'Review Classes',
        created_at: new Date().toISOString()
      });
    }

    // Check for pending content reviews
    const pendingReviews = await this.getPendingContentReviewsCount();
    if (pendingReviews > 0) {
      alerts.push({
        id: 'pending_reviews',
        type: 'warning',
        title: 'Content Reviews Pending',
        message: `${pendingReviews} content items are pending review`,
        action_url: '/content-reviews?status=pending',
        action_text: 'Review Content',
        created_at: new Date().toISOString()
      });
    }

    return alerts;
  }

  /**
   * Get overdue payments count
   */
  async getOverduePaymentsCount() {
    const query = `
      SELECT COUNT(*) as count
      FROM payments
      WHERE status = 'pending'
        AND due_date < NOW()
        AND deleted_at IS NULL
    `;
    const result = await db.query(query);
    return result[0]?.count || 0;
  }

  /**
   * Get low attendance classes count
   */
  async getLowAttendanceClassesCount() {
    const query = `
      SELECT COUNT(*) as count
      FROM (
        SELECT lc.id
        FROM live_classes lc
        LEFT JOIN class_attendance ca ON lc.id = ca.class_id
        WHERE lc.status = 'completed'
          AND lc.deleted_at IS NULL
        GROUP BY lc.id
        HAVING (COUNT(ca.id) / lc.max_capacity * 100) < 70
      ) as low_attendance_classes
    `;
    const result = await db.query(query);
    return result[0]?.count || 0;
  }

  /**
   * Get pending content reviews count
   */
  async getPendingContentReviewsCount() {
    const query = `
      SELECT COUNT(*) as count
      FROM content_reviews
      WHERE status IN ('pending', 'under_review')
        AND deleted_at IS NULL
    `;
    const result = await db.query(query);
    return result[0]?.count || 0;
  }

  /**
   * Get detailed analytics data
   */
  async getDetailedAnalytics({ period, metrics, comparePrevious, granularity, timezone, userId }) {
    const dateRange = calculateDateRange(period, timezone);
    const previousDateRange = comparePrevious ? calculateDateRange(period, timezone, true) : null;

    const analyticsData = {
      period,
      granularity,
      timezone,
      date_range: dateRange,
      metrics: {}
    };

    // If specific metrics are requested, only fetch those
    const requestedMetrics = metrics || [
      'student_enrollments',
      'revenue',
      'course_completions',
      'test_scores',
      'attendance_rates'
    ];

    for (const metric of requestedMetrics) {
      switch (metric) {
        case 'student_enrollments':
          analyticsData.metrics.student_enrollments = await this.getStudentEnrollmentAnalytics(dateRange, previousDateRange, granularity);
          break;
        case 'revenue':
          analyticsData.metrics.revenue = await this.getRevenueAnalytics(dateRange, previousDateRange, granularity);
          break;
        case 'course_completions':
          analyticsData.metrics.course_completions = await this.getCourseCompletionAnalytics(dateRange, previousDateRange, granularity);
          break;
        case 'test_scores':
          analyticsData.metrics.test_scores = await this.getTestScoreAnalytics(dateRange, previousDateRange, granularity);
          break;
        case 'attendance_rates':
          analyticsData.metrics.attendance_rates = await this.getAttendanceAnalytics(dateRange, previousDateRange, granularity);
          break;
      }
    }

    return analyticsData;
  }

  /**
   * Get student enrollment analytics
   */
  async getStudentEnrollmentAnalytics(dateRange, previousDateRange, granularity) {
    const { startDate, endDate } = dateRange;

    let groupBy = 'DATE(created_at)';
    if (granularity === 'week') {
      groupBy = 'YEARWEEK(created_at)';
    } else if (granularity === 'month') {
      groupBy = 'DATE_FORMAT(created_at, "%Y-%m")';
    }

    const query = `
      SELECT
        ${groupBy} as period,
        COUNT(*) as enrollments,
        COUNT(DISTINCT student_id) as unique_students,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_enrollments
      FROM enrollments
      WHERE created_at BETWEEN ? AND ?
        AND deleted_at IS NULL
      GROUP BY ${groupBy}
      ORDER BY period ASC
    `;

    const currentData = await db.query(query, [startDate, endDate]);

    let previousData = [];
    if (previousDateRange) {
      const { startDate: prevStart, endDate: prevEnd } = previousDateRange;
      previousData = await db.query(query, [prevStart, prevEnd]);
    }

    const totalEnrollments = currentData.reduce((sum, row) => sum + row.enrollments, 0);
    const totalUniqueStudents = currentData.reduce((sum, row) => sum + row.unique_students, 0);
    const previousTotalEnrollments = previousData.reduce((sum, row) => sum + row.enrollments, 0);

    return {
      current_period: {
        data: currentData,
        total_enrollments: totalEnrollments,
        unique_students: totalUniqueStudents,
        average_per_period: Math.round(totalEnrollments / (currentData.length || 1))
      },
      previous_period: previousDateRange ? {
        total_enrollments: previousTotalEnrollments,
        change_percentage: calculatePercentageChange(totalEnrollments, previousTotalEnrollments)
      } : null,
      insights: {
        trend: this.calculateTrend(currentData.map(d => d.enrollments)),
        peak_period: this.findPeakPeriod(currentData, 'enrollments'),
        growth_rate: previousTotalEnrollments ? calculatePercentageChange(totalEnrollments, previousTotalEnrollments) : 0
      }
    };
  }

  /**
   * Calculate trend from data array
   */
  calculateTrend(data) {
    if (data.length < 2) return 'stable';

    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));

    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

    if (secondAvg > firstAvg * 1.1) return 'increasing';
    if (secondAvg < firstAvg * 0.9) return 'decreasing';
    return 'stable';
  }

  /**
   * Find peak period from data
   */
  findPeakPeriod(data, field) {
    if (!data.length) return null;

    return data.reduce((max, current) =>
      current[field] > max[field] ? current : max
    );
  }

  /**
   * Verify WebSocket token
   */
  async verifyWebSocketToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const query = 'SELECT * FROM admin_users WHERE id = ? AND status = "active"';
      const result = await db.query(query, [decoded.id]);
      return result[0] || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Subscribe to real-time updates
   */
  async subscribeToRealTimeUpdates(ws, userId) {
    // Add WebSocket to real-time service
    realTimeService.addConnection(ws, userId);

    // Send initial data
    const initialData = await this.getOverviewData({
      period: '1d',
      comparePrevious: false,
      timezone: 'Asia/Kolkata',
      userId
    });

    ws.send(JSON.stringify({
      type: 'initial_data',
      data: initialData,
      timestamp: new Date().toISOString()
    }));
  }

  /**
   * Unsubscribe from real-time updates
   */
  unsubscribeFromRealTimeUpdates(ws, userId) {
    realTimeService.removeConnection(ws, userId);
  }
}

module.exports = new DashboardService();
