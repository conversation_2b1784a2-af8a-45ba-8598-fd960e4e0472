# 🚀 Utkrishta Coaching Admin Panel - API Requirements

## 📋 Overview

This document outlines all API endpoints required for the Utkrishta Coaching Business Admin Panel. The APIs are organized by modules with detailed specifications for request/response formats, authentication, validation, and business logic.

## 🔐 Base Configuration

### Base URLs

```
Production: https://api.utkrishta.com/v1
Development: http://localhost:5000/api/v1
```

### Authentication Headers

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
```

### Standard Response Format

```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "errors": [],
  "meta": {
    "timestamp": "2024-01-25T10:30:00Z",
    "version": "1.0.0",
    "request_id": "uuid"
  }
}
```

---

## 1️⃣ DASHBOARD ANALYTICS OVERVIEW APIs

### 1.1 Main Dashboard Overview API

#### GET /dashboard/overview

**Purpose**: Get comprehensive dashboard statistics and metrics for the main admin dashboard
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `period` (optional): `7d`, `30d`, `90d`, `1y` (default: `30d`)
- `compare_previous` (optional): `true`/`false` (default: `false`)
- `timezone` (optional): `Asia/Kolkata` (default: user timezone)

**Response**:

```json
{
  "success": true,
  "data": {
    "key_metrics": {
      "total_students": {
        "value": 1247,
        "change": 15.3,
        "change_type": "increase",
        "previous_value": 1082
      },
      "active_students": {
        "value": 1089,
        "change": 12.7,
        "change_type": "increase",
        "previous_value": 966
      },
      "total_courses": {
        "value": 45,
        "change": 7.1,
        "change_type": "increase",
        "previous_value": 42
      },
      "active_courses": {
        "value": 38,
        "change": 5.6,
        "change_type": "increase",
        "previous_value": 36
      },
      "total_revenue": {
        "value": 2450000,
        "change": 23.7,
        "change_type": "increase",
        "previous_value": 1980000,
        "currency": "INR"
      },
      "monthly_revenue": {
        "value": 345000,
        "change": 18.9,
        "change_type": "increase",
        "previous_value": 290000,
        "currency": "INR"
      },
      "total_tests": {
        "value": 156,
        "change": 12.2,
        "change_type": "increase",
        "previous_value": 139
      },
      "tests_this_month": {
        "value": 23,
        "change": 21.1,
        "change_type": "increase",
        "previous_value": 19
      },
      "live_classes_today": {
        "value": 8,
        "change": 0,
        "change_type": "neutral",
        "previous_value": 8
      },
      "upcoming_classes": {
        "value": 12,
        "change": 9.1,
        "change_type": "increase",
        "previous_value": 11
      }
    },
    "growth_metrics": {
      "student_growth": {
        "percentage": 15.3,
        "trend": "upward",
        "target": 20.0,
        "achievement": 76.5
      },
      "revenue_growth": {
        "percentage": 23.7,
        "trend": "upward",
        "target": 25.0,
        "achievement": 94.8
      },
      "course_completion_rate": {
        "percentage": 78.5,
        "trend": "upward",
        "target": 80.0,
        "achievement": 98.1
      },
      "average_score": {
        "percentage": 76.2,
        "trend": "upward",
        "target": 75.0,
        "achievement": 101.6
      }
    },
    "recent_activities": [
      {
        "id": "uuid",
        "type": "student_enrollment",
        "title": "New Student Enrolled",
        "description": "Rahul Sharma enrolled in JEE Main Physics course",
        "timestamp": "2024-01-25T10:30:00Z",
        "user": {
          "id": "uuid",
          "name": "Rahul Sharma",
          "avatar": "https://cdn.utkrishta.com/avatars/rahul.jpg"
        },
        "metadata": {
          "course_name": "JEE Main Physics",
          "amount": 15000,
          "payment_status": "completed"
        },
        "priority": "medium",
        "category": "enrollment"
      },
      {
        "id": "uuid",
        "type": "payment_received",
        "title": "Payment Received",
        "description": "₹25,000 payment received from Priya Patel",
        "timestamp": "2024-01-25T09:45:00Z",
        "user": {
          "id": "uuid",
          "name": "Priya Patel",
          "avatar": "https://cdn.utkrishta.com/avatars/priya.jpg"
        },
        "metadata": {
          "amount": 25000,
          "payment_method": "online",
          "transaction_id": "TXN123456"
        },
        "priority": "high",
        "category": "payment"
      },
      {
        "id": "uuid",
        "type": "test_completed",
        "title": "Mock Test Completed",
        "description": "JEE Main Mock Test #5 completed by 234 students",
        "timestamp": "2024-01-25T08:30:00Z",
        "metadata": {
          "test_name": "JEE Main Mock Test #5",
          "students_appeared": 234,
          "average_score": 76.8,
          "highest_score": 295
        },
        "priority": "medium",
        "category": "test"
      }
    ],
    "revenue_chart": {
      "period": "30d",
      "currency": "INR",
      "data": [
        {
          "date": "2024-01-01",
          "revenue": 45000,
          "students": 23,
          "courses_sold": 12,
          "refunds": 2000
        },
        {
          "date": "2024-01-02",
          "revenue": 52000,
          "students": 28,
          "courses_sold": 15,
          "refunds": 1500
        }
      ],
      "total_revenue": 2450000,
      "average_daily": 81667,
      "peak_day": {
        "date": "2024-01-15",
        "revenue": 125000
      }
    },
    "top_courses": [
      {
        "id": "uuid",
        "name": "JEE Main Physics Complete",
        "instructor": "Dr. Rajesh Kumar",
        "enrollments": 234,
        "revenue": 3510000,
        "rating": 4.8,
        "completion_rate": 82.5,
        "growth": 18.7
      },
      {
        "id": "uuid",
        "name": "NEET Biology Masterclass",
        "instructor": "Dr. Priya Singh",
        "enrollments": 189,
        "revenue": 2835000,
        "rating": 4.7,
        "completion_rate": 79.3,
        "growth": 15.2
      }
    ],
    "upcoming_events": [
      {
        "id": "uuid",
        "type": "live_class",
        "title": "JEE Main Physics - Laws of Motion",
        "scheduled_at": "2024-01-25T16:00:00Z",
        "duration": 90,
        "instructor": {
          "id": "uuid",
          "name": "Dr. Rajesh Kumar",
          "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg"
        },
        "students_enrolled": 45,
        "course": "JEE Main Physics Complete",
        "meeting_url": "https://zoom.us/j/123456789",
        "status": "scheduled"
      },
      {
        "id": "uuid",
        "type": "test",
        "title": "NEET Biology Mock Test #3",
        "scheduled_at": "2024-01-26T10:00:00Z",
        "duration": 180,
        "students_registered": 156,
        "total_marks": 720,
        "status": "upcoming"
      }
    ],
    "alerts": [
      {
        "id": "uuid",
        "type": "warning",
        "title": "Low Attendance Alert",
        "message": "Chemistry class attendance dropped to 65% this week",
        "severity": "medium",
        "action_required": true,
        "created_at": "2024-01-25T08:00:00Z"
      },
      {
        "id": "uuid",
        "type": "info",
        "title": "Payment Gateway Update",
        "message": "Razorpay maintenance scheduled for tonight 11 PM - 1 AM",
        "severity": "low",
        "action_required": false,
        "created_at": "2024-01-25T07:30:00Z"
      }
    ]
  },
  "meta": {
    "period": "30d",
    "timezone": "Asia/Kolkata",
    "last_updated": "2024-01-25T10:30:00Z",
    "cache_expires": "2024-01-25T10:35:00Z"
  }
}
```

### 1.2 Detailed Analytics API

#### GET /dashboard/analytics

**Purpose**: Get detailed analytics data with period comparisons and deep insights
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `period` (required): `7d`, `30d`, `90d`, `1y`
- `metrics` (optional): Comma-separated list: `revenue,students,courses,tests,classes`
- `compare_previous` (optional): `true`/`false` (default: `true`)
- `granularity` (optional): `hour`, `day`, `week`, `month` (default: `day`)
- `timezone` (optional): `Asia/Kolkata`

**Response**:

```json
{
  "success": true,
  "data": {
    "period_info": {
      "period": "30d",
      "start_date": "2023-12-26T00:00:00Z",
      "end_date": "2024-01-25T23:59:59Z",
      "comparison_period": {
        "start_date": "2023-11-26T00:00:00Z",
        "end_date": "2023-12-25T23:59:59Z"
      },
      "granularity": "day",
      "timezone": "Asia/Kolkata"
    },
    "revenue_analytics": {
      "current_period": {
        "total_revenue": 2450000,
        "average_daily": 81667,
        "median_daily": 75000,
        "peak_day": {
          "date": "2024-01-15",
          "revenue": 125000
        },
        "lowest_day": {
          "date": "2024-01-07",
          "revenue": 35000
        }
      },
      "comparison": {
        "previous_total": 1980000,
        "growth_percentage": 23.7,
        "growth_amount": 470000,
        "trend": "upward"
      },
      "revenue_sources": [
        {
          "source": "course_sales",
          "amount": 1960000,
          "percentage": 80.0,
          "change": 25.3
        },
        {
          "source": "test_series",
          "amount": 294000,
          "percentage": 12.0,
          "change": 18.7
        },
        {
          "source": "study_materials",
          "amount": 147000,
          "percentage": 6.0,
          "change": 15.2
        },
        {
          "source": "consultations",
          "amount": 49000,
          "percentage": 2.0,
          "change": 22.5
        }
      ],
      "daily_breakdown": [
        {
          "date": "2024-01-01",
          "revenue": 45000,
          "course_sales": 36000,
          "test_series": 5400,
          "study_materials": 2700,
          "consultations": 900,
          "refunds": 2000,
          "net_revenue": 43000
        }
      ]
    },
    "student_analytics": {
      "current_period": {
        "new_enrollments": 165,
        "total_active": 1089,
        "retention_rate": 87.3,
        "churn_rate": 12.7,
        "average_course_per_student": 2.3
      },
      "comparison": {
        "previous_enrollments": 142,
        "growth_percentage": 16.2,
        "retention_improvement": 3.4
      },
      "enrollment_sources": [
        {
          "source": "website",
          "count": 89,
          "percentage": 53.9,
          "conversion_rate": 12.3
        },
        {
          "source": "referral",
          "count": 41,
          "percentage": 24.8,
          "conversion_rate": 34.7
        },
        {
          "source": "social_media",
          "count": 23,
          "percentage": 13.9,
          "conversion_rate": 8.9
        },
        {
          "source": "offline",
          "count": 12,
          "percentage": 7.3,
          "conversion_rate": 45.2
        }
      ],
      "geographic_distribution": [
        {
          "state": "Maharashtra",
          "count": 234,
          "percentage": 21.5,
          "growth": 18.7
        },
        {
          "state": "Delhi",
          "count": 189,
          "percentage": 17.4,
          "growth": 15.2
        },
        {
          "state": "Uttar Pradesh",
          "count": 156,
          "percentage": 14.3,
          "growth": 22.1
        }
      ]
    },
    "course_analytics": {
      "performance_metrics": {
        "total_courses": 45,
        "active_courses": 38,
        "average_completion_rate": 78.5,
        "average_rating": 4.6,
        "total_enrollments": 2847
      },
      "top_performing_courses": [
        {
          "id": "uuid",
          "name": "JEE Main Physics Complete",
          "enrollments": 234,
          "completion_rate": 82.5,
          "rating": 4.8,
          "revenue": 3510000,
          "growth": 18.7
        }
      ],
      "subject_wise_performance": [
        {
          "subject": "Physics",
          "courses": 15,
          "enrollments": 987,
          "avg_completion": 81.2,
          "avg_rating": 4.7,
          "revenue": 14805000
        },
        {
          "subject": "Chemistry",
          "courses": 12,
          "enrollments": 756,
          "avg_completion": 76.8,
          "avg_rating": 4.5,
          "revenue": 11340000
        },
        {
          "subject": "Mathematics",
          "courses": 13,
          "enrollments": 823,
          "avg_completion": 79.3,
          "avg_rating": 4.6,
          "revenue": 12345000
        },
        {
          "subject": "Biology",
          "courses": 5,
          "enrollments": 281,
          "avg_completion": 74.2,
          "avg_rating": 4.4,
          "revenue": 4215000
        }
      ]
    }
  }
}
```

### 1.3 Real-time Dashboard Updates API

#### GET /dashboard/real-time

**Purpose**: WebSocket endpoint for real-time dashboard updates
**Authentication**: Required (Admin/Super Admin)
**Protocol**: WebSocket
**Connection URL**: `wss://api.utkrishta.com/v1/dashboard/real-time?token=<jwt_token>`

**Real-time Events**:

```json
{
  "event": "student_enrolled",
  "data": {
    "student_id": "uuid",
    "student_name": "Rahul Sharma",
    "course_id": "uuid",
    "course_name": "JEE Main Physics",
    "amount": 15000,
    "timestamp": "2024-01-25T10:30:00Z"
  }
}

{
  "event": "payment_received",
  "data": {
    "transaction_id": "TXN123456",
    "student_id": "uuid",
    "amount": 25000,
    "payment_method": "razorpay",
    "timestamp": "2024-01-25T10:31:00Z"
  }
}

{
  "event": "test_completed",
  "data": {
    "test_id": "uuid",
    "test_name": "JEE Main Mock Test #5",
    "students_appeared": 234,
    "average_score": 76.8,
    "timestamp": "2024-01-25T10:32:00Z"
  }
}

{
  "event": "class_started",
  "data": {
    "class_id": "uuid",
    "class_name": "Physics - Laws of Motion",
    "instructor": "Dr. Rajesh Kumar",
    "students_joined": 45,
    "timestamp": "2024-01-25T16:00:00Z"
  }
}

{
  "event": "metrics_update",
  "data": {
    "total_students": 1248,
    "active_students": 1090,
    "today_revenue": 125000,
    "live_classes": 3,
    "timestamp": "2024-01-25T10:33:00Z"
  }
}
```

### 1.4 Dashboard Widgets API

#### GET /dashboard/widgets

**Purpose**: Get customizable dashboard widgets with chart data
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `widget_types` (optional): Comma-separated list: `revenue_chart,student_growth,course_performance,test_analytics`
- `period` (optional): `7d`, `30d`, `90d` (default: `30d`)

**Response**:

```json
{
  "success": true,
  "data": {
    "widgets": [
      {
        "id": "revenue_chart",
        "title": "Revenue Analytics",
        "type": "line_chart",
        "size": "large",
        "position": { "x": 0, "y": 0, "w": 8, "h": 4 },
        "data": {
          "labels": ["Jan 1", "Jan 2", "Jan 3", "Jan 4", "Jan 5"],
          "datasets": [
            {
              "label": "Daily Revenue",
              "data": [45000, 52000, 48000, 67000, 59000],
              "borderColor": "#3b82f6",
              "backgroundColor": "rgba(59, 130, 246, 0.1)"
            },
            {
              "label": "Previous Period",
              "data": [38000, 41000, 39000, 52000, 47000],
              "borderColor": "#94a3b8",
              "backgroundColor": "rgba(148, 163, 184, 0.1)"
            }
          ]
        },
        "config": {
          "responsive": true,
          "plugins": {
            "legend": { "position": "top" },
            "title": { "display": true, "text": "Revenue Trend (Last 30 Days)" }
          }
        }
      },
      {
        "id": "student_growth",
        "title": "Student Growth",
        "type": "area_chart",
        "size": "medium",
        "position": { "x": 8, "y": 0, "w": 4, "h": 4 },
        "data": {
          "labels": ["Week 1", "Week 2", "Week 3", "Week 4"],
          "datasets": [
            {
              "label": "New Enrollments",
              "data": [42, 38, 45, 40],
              "borderColor": "#10b981",
              "backgroundColor": "rgba(16, 185, 129, 0.2)"
            }
          ]
        }
      },
      {
        "id": "course_performance",
        "title": "Top Performing Courses",
        "type": "bar_chart",
        "size": "medium",
        "position": { "x": 0, "y": 4, "w": 6, "h": 3 },
        "data": {
          "labels": [
            "JEE Physics",
            "NEET Biology",
            "JEE Chemistry",
            "NEET Physics"
          ],
          "datasets": [
            {
              "label": "Enrollments",
              "data": [234, 189, 156, 134],
              "backgroundColor": ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"]
            }
          ]
        }
      },
      {
        "id": "test_analytics",
        "title": "Test Performance",
        "type": "doughnut_chart",
        "size": "small",
        "position": { "x": 6, "y": 4, "w": 3, "h": 3 },
        "data": {
          "labels": [
            "Excellent (90+)",
            "Good (75-89)",
            "Average (60-74)",
            "Below Average (<60)"
          ],
          "datasets": [
            {
              "data": [23, 45, 28, 4],
              "backgroundColor": ["#10b981", "#3b82f6", "#f59e0b", "#ef4444"]
            }
          ]
        }
      }
    ],
    "layout_config": {
      "grid_columns": 12,
      "grid_rows": 8,
      "margin": [10, 10],
      "container_padding": [20, 20],
      "row_height": 60
    }
  }
}
```

#### POST /dashboard/widgets/layout

**Purpose**: Save custom dashboard widget layout
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "layout": [
    {
      "widget_id": "revenue_chart",
      "position": { "x": 0, "y": 0, "w": 8, "h": 4 },
      "visible": true
    },
    {
      "widget_id": "student_growth",
      "position": { "x": 8, "y": 0, "w": 4, "h": 4 },
      "visible": true
    }
  ],
  "user_id": "uuid"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Dashboard layout saved successfully",
  "data": {
    "layout_id": "uuid",
    "saved_at": "2024-01-25T10:30:00Z"
  }
}
```

### 1.5 Dashboard Reports API

#### GET /dashboard/reports

**Purpose**: Get comprehensive dashboard reports with insights and recommendations
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `report_type` (required): `daily`, `weekly`, `monthly`
- `date` (optional): `2024-01-25` (default: today)
- `include_recommendations` (optional): `true`/`false` (default: `true`)

**Response**:

```json
{
  "success": true,
  "data": {
    "report": {
      "type": "daily",
      "date": "2024-01-25",
      "generated_at": "2024-01-25T23:59:59Z",
      "summary": {
        "total_revenue": 125000,
        "new_students": 12,
        "courses_sold": 8,
        "tests_conducted": 3,
        "live_classes": 5,
        "support_tickets": 2
      },
      "highlights": [
        "Highest revenue day this month",
        "JEE Main Physics course reached 500 enrollments",
        "Student satisfaction score improved to 4.8",
        "Zero payment failures today"
      ],
      "performance_indicators": {
        "revenue_vs_target": {
          "actual": 125000,
          "target": 100000,
          "achievement": 125.0,
          "status": "exceeded"
        },
        "enrollment_vs_target": {
          "actual": 12,
          "target": 15,
          "achievement": 80.0,
          "status": "below_target"
        },
        "class_attendance": {
          "actual": 87.3,
          "target": 85.0,
          "achievement": 102.7,
          "status": "exceeded"
        }
      },
      "detailed_metrics": {
        "revenue_breakdown": {
          "course_sales": 100000,
          "test_series": 15000,
          "study_materials": 7500,
          "consultations": 2500
        },
        "student_activities": {
          "new_registrations": 12,
          "course_completions": 8,
          "test_attempts": 156,
          "class_attendance": 234
        },
        "instructor_performance": [
          {
            "instructor": "Dr. Rajesh Kumar",
            "classes_conducted": 2,
            "attendance_rate": 92.3,
            "student_rating": 4.9
          }
        ]
      },
      "alerts_and_issues": [
        {
          "type": "warning",
          "title": "Low Attendance Alert",
          "description": "Chemistry class attendance dropped to 65%",
          "severity": "medium",
          "action_required": true
        }
      ],
      "recommendations": [
        {
          "category": "revenue",
          "title": "Optimize Course Pricing",
          "description": "Consider introducing early bird discounts for upcoming courses",
          "priority": "medium",
          "expected_impact": "15% increase in enrollments"
        },
        {
          "category": "student_engagement",
          "title": "Improve Chemistry Class Engagement",
          "description": "Add more interactive elements to chemistry classes",
          "priority": "high",
          "expected_impact": "10% improvement in attendance"
        }
      ],
      "trends": {
        "revenue_trend": "upward",
        "enrollment_trend": "stable",
        "satisfaction_trend": "upward",
        "completion_trend": "upward"
      }
    }
  }
}
```

### 1.6 Dashboard Export API

#### GET /dashboard/export

**Purpose**: Export dashboard data in various formats
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `format` (required): `pdf`, `excel`, `csv`
- `data_type` (required): `overview`, `analytics`, `reports`
- `period` (optional): `7d`, `30d`, `90d`, `1y` (default: `30d`)
- `include_charts` (optional): `true`/`false` (default: `true`)

**Response**:

```json
{
  "success": true,
  "data": {
    "export_id": "uuid",
    "download_url": "https://cdn.utkrishta.com/exports/dashboard-analytics-2024-01-25.pdf",
    "file_size": 2048000,
    "expires_at": "2024-01-26T10:30:00Z",
    "generated_at": "2024-01-25T10:30:00Z"
  }
}
```

---

## 🎯 Dashboard API Summary

### **Key Features Implemented:**

1. **📊 Comprehensive Overview**: Complete dashboard metrics with growth indicators
2. **📈 Detailed Analytics**: Deep insights with period comparisons and breakdowns
3. **⚡ Real-time Updates**: WebSocket support for live dashboard updates
4. **🎛️ Customizable Widgets**: Drag-and-drop dashboard with chart configurations
5. **📋 Automated Reports**: Daily, weekly, monthly reports with recommendations
6. **📤 Data Export**: Multiple format support (PDF, Excel, CSV)

### **API Endpoints Summary:**

| Endpoint                    | Method    | Purpose               | Key Features                               |
| --------------------------- | --------- | --------------------- | ------------------------------------------ |
| `/dashboard/overview`       | GET       | Main dashboard data   | Key metrics, activities, charts, alerts    |
| `/dashboard/analytics`      | GET       | Detailed analytics    | Period comparisons, breakdowns, trends     |
| `/dashboard/real-time`      | WebSocket | Live updates          | Real-time events, metrics updates          |
| `/dashboard/widgets`        | GET       | Widget data           | Chart configurations, customizable layouts |
| `/dashboard/widgets/layout` | POST      | Save layout           | Custom dashboard arrangements              |
| `/dashboard/reports`        | GET       | Comprehensive reports | Insights, recommendations, trends          |
| `/dashboard/export`         | GET       | Data export           | PDF, Excel, CSV formats                    |

### **Business Logic Covered:**

✅ **Revenue Analytics**: Daily/monthly revenue tracking with growth metrics
✅ **Student Management**: Enrollment tracking, retention analysis, geographic distribution
✅ **Course Performance**: Completion rates, ratings, subject-wise analytics
✅ **Real-time Monitoring**: Live updates for enrollments, payments, classes
✅ **Customizable Interface**: Drag-and-drop widgets, personalized layouts
✅ **Automated Reporting**: Daily insights with actionable recommendations
✅ **Data Export**: Professional reports in multiple formats

### **Technical Features:**

🔐 **Authentication**: JWT-based security for all endpoints
📱 **Responsive**: Mobile-friendly data structures
⚡ **Performance**: Optimized queries with caching support
🔄 **Real-time**: WebSocket integration for live updates
📊 **Charts**: Ready-to-use chart configurations
🎨 **Customization**: Flexible widget system
📈 **Analytics**: Advanced metrics with comparisons

**🚀 The Dashboard Analytics APIs are now complete and production-ready for backend implementation!**

---

## 2️⃣ EXAM & COURSE MANAGEMENT APIs

### 2.1 Course Management APIs

#### GET /courses

**Purpose**: Get list of courses with comprehensive filtering and search
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, description, instructor name
- `status` (optional): `active`, `draft`, `archived`, `published`
- `category` (optional): Course category ID or slug
- `subject` (optional): Subject ID or code
- `instructor` (optional): Instructor ID
- `difficulty` (optional): `beginner`, `intermediate`, `advanced`
- `price_min` (optional): Minimum price filter
- `price_max` (optional): Maximum price filter
- `sort_by` (optional): `created_at`, `updated_at`, `title`, `price`, `enrollments`, `rating`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)
- `include_stats` (optional): `true`/`false` (default: `false`)

**Response**:

```json
{
  "success": true,
  "data": {
    "courses": [
      {
        "id": "uuid",
        "title": "JEE Main Physics Complete Course",
        "slug": "jee-main-physics-complete",
        "description": "Comprehensive physics course covering all JEE Main topics with detailed explanations and problem-solving techniques",
        "short_description": "Master JEE Main Physics with expert guidance",
        "thumbnail": "https://cdn.utkrishta.com/courses/jee-physics-thumb.jpg",
        "banner_image": "https://cdn.utkrishta.com/courses/jee-physics-banner.jpg",
        "category": {
          "id": "uuid",
          "name": "JEE Main",
          "slug": "jee-main"
        },
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "instructor": {
          "id": "uuid",
          "name": "Dr. Rajesh Kumar",
          "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
          "qualification": "Ph.D. in Physics, IIT Delhi",
          "experience": 15,
          "rating": 4.8,
          "total_students": 2340
        },
        "pricing": {
          "original_price": 18000,
          "current_price": 15000,
          "discount_percentage": 16.67,
          "currency": "INR",
          "is_free": false,
          "payment_plans": [
            {
              "type": "full_payment",
              "price": 15000,
              "discount": 0
            },
            {
              "type": "installment_3",
              "price": 16000,
              "installments": 3,
              "per_installment": 5334
            }
          ]
        },
        "course_details": {
          "duration": "6 months",
          "total_lectures": 120,
          "total_hours": 180,
          "difficulty_level": "intermediate",
          "language": "Hindi/English",
          "prerequisites": ["Class 11 Physics basics"]
        },
        "content_overview": {
          "total_modules": 12,
          "total_videos": 120,
          "total_pdfs": 45,
          "total_tests": 25,
          "total_assignments": 30,
          "live_classes": 24
        },
        "enrollment_info": {
          "total_enrolled": 567,
          "enrollment_limit": 1000,
          "enrollment_start": "2024-01-01T00:00:00Z",
          "enrollment_end": "2024-12-31T23:59:59Z",
          "course_start": "2024-02-01T00:00:00Z",
          "course_end": "2024-08-01T23:59:59Z"
        },
        "performance_metrics": {
          "average_rating": 4.8,
          "total_reviews": 234,
          "completion_rate": 78.5,
          "average_score": 76.2
        },
        "features": [
          "Live classes",
          "Recorded lectures",
          "PDF notes",
          "Mock tests",
          "Doubt clearing sessions"
        ],
        "tags": ["jee_main", "physics", "iit", "engineering"],
        "status": "published",
        "is_featured": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 45,
      "total_pages": 2,
      "has_next": true,
      "has_prev": false
    },
    "filters_applied": {
      "search": "physics",
      "status": "published",
      "category": "jee-main"
    },
    "summary": {
      "total_courses": 45,
      "published_courses": 38,
      "draft_courses": 7,
      "total_enrollments": 12450,
      "total_revenue": 186750000
    }
  }
}
```

#### GET /courses/{id}

**Purpose**: Get detailed course information with complete analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Course UUID or slug

**Response**:

```json
{
  "success": true,
  "data": {
    "course": {
      "id": "uuid",
      "title": "JEE Main Physics Complete Course",
      "slug": "jee-main-physics-complete",
      "description": "Comprehensive physics course designed specifically for JEE Main preparation. Covers all topics from mechanics to modern physics with detailed explanations, problem-solving techniques, and regular assessments.",
      "short_description": "Master JEE Main Physics with expert guidance and comprehensive study materials",
      "thumbnail": "https://cdn.utkrishta.com/courses/jee-physics-thumb.jpg",
      "banner_image": "https://cdn.utkrishta.com/courses/jee-physics-banner.jpg",
      "category": {
        "id": "uuid",
        "name": "JEE Main",
        "slug": "jee-main"
      },
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "instructor": {
        "id": "uuid",
        "name": "Dr. Rajesh Kumar",
        "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
        "qualification": "Ph.D. in Physics, IIT Delhi",
        "experience": 15,
        "rating": 4.8,
        "total_students": 2340,
        "bio": "Dr. Rajesh Kumar is a renowned physics educator with 15 years of experience in coaching students for competitive exams.",
        "specializations": ["Mechanics", "Thermodynamics", "Modern Physics"]
      },
      "pricing": {
        "original_price": 18000,
        "current_price": 15000,
        "discount_percentage": 16.67,
        "currency": "INR",
        "is_free": false,
        "payment_plans": [
          {
            "type": "full_payment",
            "price": 15000,
            "discount": 0,
            "savings": 3000
          },
          {
            "type": "installment_3",
            "price": 16000,
            "installments": 3,
            "per_installment": 5334,
            "processing_fee": 1000
          }
        ],
        "refund_policy": {
          "refundable_days": 7,
          "refund_percentage": 100,
          "conditions": ["No content accessed", "Within 7 days of purchase"]
        }
      },
      "course_details": {
        "duration": "6 months",
        "total_lectures": 120,
        "total_hours": 180,
        "difficulty_level": "intermediate",
        "language": "Hindi/English",
        "prerequisites": ["Class 11 Physics basics", "Basic Mathematics"],
        "learning_outcomes": [
          "Master all JEE Main Physics topics",
          "Solve complex numerical problems",
          "Understand conceptual physics",
          "Score 90+ in JEE Main Physics"
        ],
        "certification": {
          "available": true,
          "criteria": "80% course completion + 70% average score",
          "certificate_url": "https://cdn.utkrishta.com/certificates/template.pdf"
        }
      },
      "curriculum": {
        "total_modules": 12,
        "total_lessons": 120,
        "modules": [
          {
            "id": "uuid",
            "title": "Mechanics",
            "description": "Laws of motion, work-energy theorem, rotational dynamics",
            "order": 1,
            "duration": "4 weeks",
            "lessons_count": 15,
            "tests_count": 3,
            "assignments_count": 5,
            "is_free": false,
            "completion_rate": 85.2
          },
          {
            "id": "uuid",
            "title": "Thermodynamics",
            "description": "Heat, temperature, kinetic theory",
            "order": 2,
            "duration": "3 weeks",
            "lessons_count": 12,
            "tests_count": 2,
            "assignments_count": 4,
            "is_free": false,
            "completion_rate": 78.9
          }
        ]
      },
      "enrollment_analytics": {
        "total_enrolled": 567,
        "active_students": 489,
        "completed_students": 78,
        "dropout_rate": 13.8,
        "average_progress": 65.4,
        "enrollment_trend": [
          {
            "month": "2024-01",
            "enrollments": 89,
            "revenue": 1335000
          }
        ]
      },
      "performance_metrics": {
        "average_rating": 4.8,
        "total_reviews": 234,
        "completion_rate": 78.5,
        "average_score": 76.2,
        "pass_rate": 89.3,
        "student_satisfaction": 92.1
      },
      "features": [
        "Live classes twice a week",
        "Recorded lectures with lifetime access",
        "Comprehensive PDF notes",
        "Weekly mock tests",
        "Doubt clearing sessions",
        "Personal mentorship",
        "Mobile app access"
      ],
      "schedule": {
        "enrollment_start": "2024-01-01T00:00:00Z",
        "enrollment_end": "2024-12-31T23:59:59Z",
        "course_start": "2024-02-01T00:00:00Z",
        "course_end": "2024-08-01T23:59:59Z",
        "live_classes": [
          {
            "day": "Monday",
            "time": "18:00-19:30",
            "timezone": "Asia/Kolkata"
          },
          {
            "day": "Thursday",
            "time": "18:00-19:30",
            "timezone": "Asia/Kolkata"
          }
        ]
      },
      "tags": ["jee_main", "physics", "iit", "engineering", "competitive_exam"],
      "status": "published",
      "is_featured": true,
      "visibility": "public",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-20T10:30:00Z",
      "published_at": "2024-01-15T10:00:00Z"
    }
  }
}
```

#### POST /courses

**Purpose**: Create new course
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "title": "NEET Biology Masterclass",
  "description": "Complete biology course for NEET preparation with detailed coverage of all topics",
  "short_description": "Master biology for NEET success",
  "category_id": "uuid",
  "subject_id": "uuid",
  "instructor_id": "uuid",
  "pricing": {
    "original_price": 18000,
    "current_price": 15000,
    "currency": "INR",
    "is_free": false,
    "payment_plans": [
      {
        "type": "full_payment",
        "price": 15000
      },
      {
        "type": "installment_3",
        "price": 16000,
        "installments": 3
      }
    ]
  },
  "course_details": {
    "duration": "8 months",
    "difficulty_level": "intermediate",
    "language": "Hindi",
    "prerequisites": ["Class 11 Biology basics"],
    "learning_outcomes": [
      "Master NEET Biology syllabus",
      "Score 350+ in NEET Biology",
      "Understand complex biological processes"
    ]
  },
  "schedule": {
    "enrollment_start": "2024-02-01",
    "enrollment_end": "2024-12-31",
    "course_start": "2024-02-15",
    "course_end": "2024-10-15"
  },
  "features": ["Live classes", "PDF notes", "Mock tests", "Doubt clearing"],
  "tags": ["neet", "biology", "masterclass"],
  "is_featured": false,
  "status": "draft",
  "certificate_available": true
}
```

**Response**:

```json
{
  "success": true,
  "message": "Course created successfully",
  "data": {
    "course": {
      "id": "uuid",
      "title": "NEET Biology Masterclass",
      "slug": "neet-biology-masterclass",
      "status": "draft",
      "created_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### PUT /courses/{id}

**Purpose**: Update course information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Course UUID
  **Request Body**: Same as POST but with optional fields

#### DELETE /courses/{id}

**Purpose**: Delete course (soft delete)
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "message": "Course deleted successfully",
  "data": {
    "deleted_at": "2024-01-25T10:30:00Z"
  }
}
```

### 2.2 Exam Management APIs

#### GET /exams

**Purpose**: Get list of exams with comprehensive details
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in exam name, description, conducting body
- `type` (optional): `entrance`, `scholarship`, `competitive`, `mock`
- `category` (optional): `engineering`, `medical`, `commerce`, `arts`
- `status` (optional): `active`, `upcoming`, `completed`, `cancelled`
- `year` (optional): Exam year filter
- `sort_by` (optional): `created_at`, `exam_date`, `name`, `applications`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "exams": [
      {
        "id": "uuid",
        "name": "JEE Main",
        "code": "JEE_MAIN",
        "description": "Joint Entrance Examination for engineering colleges in India",
        "type": "entrance",
        "category": "engineering",
        "conducting_body": "National Testing Agency (NTA)",
        "official_website": "https://jeemain.nta.nic.in",
        "logo": "https://cdn.utkrishta.com/exams/jee-main-logo.png",
        "exam_details": {
          "duration": 180,
          "total_marks": 300,
          "total_questions": 90,
          "subjects": [
            {
              "name": "Physics",
              "questions": 30,
              "marks": 100
            },
            {
              "name": "Chemistry",
              "questions": 30,
              "marks": 100
            },
            {
              "name": "Mathematics",
              "questions": 30,
              "marks": 100
            }
          ],
          "exam_mode": "online",
          "language_options": ["Hindi", "English", "Gujarati", "Marathi"],
          "negative_marking": true,
          "negative_marks": -1,
          "question_types": ["MCQ", "Numerical"]
        },
        "schedule": {
          "registration_start": "2024-01-01T00:00:00Z",
          "registration_end": "2024-02-15T23:59:59Z",
          "exam_sessions": [
            {
              "session": "Session 1",
              "start_date": "2024-04-01T00:00:00Z",
              "end_date": "2024-04-15T23:59:59Z",
              "shifts": [
                {
                  "shift": "Morning",
                  "time": "09:00-12:00",
                  "subjects": ["Physics", "Chemistry", "Mathematics"]
                },
                {
                  "shift": "Afternoon",
                  "time": "15:00-18:00",
                  "subjects": ["Physics", "Chemistry", "Mathematics"]
                }
              ]
            }
          ],
          "result_date": "2024-05-30T00:00:00Z"
        },
        "eligibility": {
          "age_limit": "No upper age limit",
          "qualification": "Passed Class 12 with Physics, Chemistry, Mathematics",
          "minimum_percentage": {
            "general": 75,
            "obc": 75,
            "sc_st": 65
          },
          "subjects_required": ["Physics", "Chemistry", "Mathematics"]
        },
        "fees": {
          "general": 650,
          "obc": 650,
          "sc_st": 325,
          "pwd": 325,
          "currency": "INR"
        },
        "statistics": {
          "total_applicants": 1200000,
          "total_seats": 250000,
          "competition_ratio": 4.8,
          "cutoff_percentile": {
            "general": 89.75,
            "obc": 74.31,
            "sc": 54.01,
            "st": 44.33
          }
        },
        "courses_available": 18,
        "students_enrolled": 6789,
        "mock_tests_available": 45,
        "study_materials": 234,
        "status": "active",
        "is_featured": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 12,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    },
    "summary": {
      "total_exams": 12,
      "active_exams": 8,
      "upcoming_exams": 3,
      "total_students": 25670,
      "total_courses": 89
    }
  }
}
```

#### GET /exams/{id}

**Purpose**: Get detailed exam information
**Authentication**: Required (Admin/Super Admin)
**Response**: Complete exam object with all details, syllabus, and analytics

#### POST /exams

**Purpose**: Create new exam
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "NEET UG",
  "code": "NEET_UG",
  "description": "National Eligibility cum Entrance Test for undergraduate medical courses",
  "type": "entrance",
  "category": "medical",
  "conducting_body": "National Testing Agency (NTA)",
  "official_website": "https://neet.nta.nic.in",
  "exam_details": {
    "duration": 200,
    "total_marks": 720,
    "total_questions": 180,
    "subjects": [
      {
        "name": "Physics",
        "questions": 45,
        "marks": 180
      },
      {
        "name": "Chemistry",
        "questions": 45,
        "marks": 180
      },
      {
        "name": "Biology",
        "questions": 90,
        "marks": 360
      }
    ],
    "exam_mode": "offline",
    "language_options": ["Hindi", "English", "Tamil", "Telugu"],
    "negative_marking": true,
    "negative_marks": -1,
    "question_types": ["MCQ"]
  },
  "schedule": {
    "registration_start": "2024-02-01T00:00:00Z",
    "registration_end": "2024-03-15T23:59:59Z",
    "exam_sessions": [
      {
        "session": "Single Session",
        "start_date": "2024-05-05T00:00:00Z",
        "end_date": "2024-05-05T23:59:59Z"
      }
    ],
    "result_date": "2024-06-15T00:00:00Z"
  },
  "eligibility": {
    "age_limit": "17-25 years",
    "qualification": "Passed Class 12 with Physics, Chemistry, Biology",
    "minimum_percentage": {
      "general": 50,
      "obc": 40,
      "sc_st": 40
    },
    "subjects_required": ["Physics", "Chemistry", "Biology"]
  },
  "fees": {
    "general": 1600,
    "obc": 1600,
    "sc_st": 900,
    "pwd": 900,
    "currency": "INR"
  }
}
```

#### PUT /exams/{id}

**Purpose**: Update exam information
**Authentication**: Required (Admin/Super Admin)
**Request Body**: Same as POST but with optional fields

#### DELETE /exams/{id}

**Purpose**: Delete exam
**Authentication**: Required (Admin/Super Admin)

#### GET /exams/{id}/analytics

**Purpose**: Get exam-specific analytics and performance data
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "data": {
    "enrollment_analytics": {
      "total_students_enrolled": 6789,
      "courses_offered": 18,
      "total_revenue": 101835000,
      "enrollment_trend": [
        {
          "month": "2024-01",
          "enrollments": 567,
          "revenue": 8505000
        }
      ]
    },
    "performance_analytics": {
      "average_mock_score": 78.5,
      "students_above_cutoff": 4567,
      "predicted_qualifiers": 3456,
      "subject_wise_performance": [
        {
          "subject": "Physics",
          "average_score": 76.2,
          "difficulty_rating": 7.8,
          "most_difficult_topics": ["Modern Physics", "Optics"]
        }
      ]
    },
    "geographical_distribution": [
      {
        "state": "Maharashtra",
        "student_count": 1234,
        "percentage": 18.2,
        "average_score": 79.5
      }
    ]
  }
}
```

### 2.3 Course Content Management APIs

#### GET /courses/{id}/modules

**Purpose**: Get course modules and lessons
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "data": {
    "modules": [
      {
        "id": "uuid",
        "title": "Mechanics",
        "description": "Fundamental concepts of mechanics including laws of motion",
        "order": 1,
        "duration": "4 weeks",
        "is_free": false,
        "status": "active",
        "lessons": [
          {
            "id": "uuid",
            "title": "Laws of Motion",
            "description": "Newton's laws and their applications",
            "type": "video",
            "duration": 90,
            "order": 1,
            "is_free": true,
            "content_url": "https://cdn.utkrishta.com/videos/laws-of-motion.mp4",
            "thumbnail": "https://cdn.utkrishta.com/thumbnails/laws-of-motion.jpg",
            "status": "active",
            "resources": [
              {
                "id": "uuid",
                "title": "Laws of Motion Notes",
                "type": "pdf",
                "url": "https://cdn.utkrishta.com/pdfs/laws-of-motion.pdf",
                "size": 2048000
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### POST /courses/{id}/modules

**Purpose**: Add new module to course
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "title": "Thermodynamics",
  "description": "Heat, temperature, and kinetic theory concepts",
  "order": 2,
  "duration": "3 weeks",
  "is_free": false,
  "learning_objectives": [
    "Understand heat transfer mechanisms",
    "Apply laws of thermodynamics"
  ]
}
```

#### GET /courses/{id}/students

**Purpose**: Get students enrolled in specific course
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**: `?page=1&limit=25&status=active&progress_min=50`
**Response**:

```json
{
  "success": true,
  "data": {
    "enrollment_summary": {
      "total_enrolled": 567,
      "active_students": 489,
      "completed_students": 78,
      "average_progress": 65.4
    },
    "students": [
      {
        "student_id": "uuid",
        "name": "Rahul Sharma",
        "email": "<EMAIL>",
        "enrollment_date": "2024-01-15T10:30:00Z",
        "progress": 78.5,
        "status": "active",
        "last_activity": "2024-01-25T14:30:00Z",
        "modules_completed": 9,
        "total_modules": 12,
        "average_score": 82.3,
        "time_spent": 145
      }
    ]
  }
}
```

---

## 🎯 Exam & Course Management API Summary

### **Key Features Implemented:**

1. **📚 Complete Course Management**: CRUD operations with detailed course information
2. **🎓 Comprehensive Exam System**: Full exam lifecycle management
3. **📊 Advanced Analytics**: Performance tracking and insights
4. **👥 Student Management**: Enrollment tracking and progress monitoring
5. **📖 Content Organization**: Module and lesson management
6. **💰 Pricing Management**: Flexible pricing plans and payment options

### **API Endpoints Summary:**

| Module      | Endpoint                 | Method | Purpose                     |
| ----------- | ------------------------ | ------ | --------------------------- |
| **Courses** | `/courses`               | GET    | List courses with filtering |
|             | `/courses/{id}`          | GET    | Detailed course information |
|             | `/courses`               | POST   | Create new course           |
|             | `/courses/{id}`          | PUT    | Update course               |
|             | `/courses/{id}`          | DELETE | Delete course               |
|             | `/courses/{id}/modules`  | GET    | Get course modules          |
|             | `/courses/{id}/modules`  | POST   | Add module                  |
|             | `/courses/{id}/students` | GET    | Course enrollments          |
| **Exams**   | `/exams`                 | GET    | List exams with details     |
|             | `/exams/{id}`            | GET    | Detailed exam information   |
|             | `/exams`                 | POST   | Create new exam             |
|             | `/exams/{id}`            | PUT    | Update exam                 |
|             | `/exams/{id}`            | DELETE | Delete exam                 |
|             | `/exams/{id}/analytics`  | GET    | Exam analytics              |

### **Business Logic Covered:**

✅ **Course Lifecycle**: Draft → Published → Archived workflow
✅ **Pricing Management**: Multiple payment plans, discounts, refunds
✅ **Content Organization**: Hierarchical module/lesson structure
✅ **Student Tracking**: Enrollment, progress, completion analytics
✅ **Exam Management**: Complete exam configuration and scheduling
✅ **Performance Analytics**: Subject-wise analysis, geographic distribution
✅ **Revenue Tracking**: Course-wise revenue and enrollment trends

### **Technical Features:**

🔐 **Secure Authentication**: JWT-based access control
📱 **Mobile Optimized**: Responsive data structures
🔍 **Advanced Search**: Multi-criteria filtering and sorting
📊 **Rich Analytics**: Performance metrics and insights
⚡ **Optimized Queries**: Efficient pagination and caching
🎯 **Flexible Structure**: Modular content organization
💳 **Payment Integration**: Multiple payment plan support

**🚀 The Exam & Course Management APIs are now complete and production-ready for backend implementation!**

---

## 3️⃣ SUBJECT & CLASS MANAGEMENT APIs

### 3.1 Subject Management APIs

#### GET /subjects

**Purpose**: Get list of subjects with comprehensive details
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in subject name, description, topics
- `category` (optional): Subject category (science, commerce, arts)
- `exam` (optional): Filter by exam type (jee_main, neet, etc.)
- `status` (optional): `active`, `inactive`, `draft`
- `difficulty` (optional): `easy`, `medium`, `hard`
- `sort_by` (optional): `name`, `created_at`, `students_count`, `courses_count`
- `sort_order` (optional): `asc`, `desc` (default: `asc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "subjects": [
      {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY",
        "description": "Study of matter, energy, and their interactions in the physical world",
        "category": "Science",
        "icon": "atom",
        "color": "#3b82f6",
        "banner_image": "https://cdn.utkrishta.com/subjects/physics-banner.jpg",
        "thumbnail": "https://cdn.utkrishta.com/subjects/physics-thumb.jpg",
        "exams": [
          {
            "exam_id": "uuid",
            "exam_name": "JEE Main",
            "weightage": 33.33,
            "total_marks": 100,
            "questions": 30
          },
          {
            "exam_id": "uuid",
            "exam_name": "NEET",
            "weightage": 25,
            "total_marks": 180,
            "questions": 45
          }
        ],
        "topics": [
          {
            "id": "uuid",
            "name": "Mechanics",
            "description": "Laws of motion, work-energy, rotational dynamics",
            "difficulty_level": "medium",
            "weightage": 25,
            "subtopics_count": 15,
            "estimated_hours": 40
          },
          {
            "id": "uuid",
            "name": "Thermodynamics",
            "description": "Heat, temperature, kinetic theory",
            "difficulty_level": "hard",
            "weightage": 15,
            "subtopics_count": 8,
            "estimated_hours": 25
          }
        ],
        "statistics": {
          "total_topics": 45,
          "courses_count": 12,
          "students_enrolled": 3456,
          "total_tests": 89,
          "average_score": 76.2,
          "difficulty_rating": 7.8,
          "completion_rate": 78.5
        },
        "difficulty_level": "high",
        "prerequisites": ["Mathematics basics", "Class 11 Physics"],
        "learning_outcomes": [
          "Understand fundamental physics concepts",
          "Solve numerical problems effectively",
          "Apply physics in real-world scenarios"
        ],
        "study_materials": {
          "video_lectures": 156,
          "pdf_notes": 45,
          "practice_questions": 2340,
          "mock_tests": 23,
          "assignments": 67
        },
        "instructors": [
          {
            "instructor_id": "uuid",
            "name": "Dr. Rajesh Kumar",
            "specialization": "Mechanics & Thermodynamics",
            "experience": 15,
            "rating": 4.8
          }
        ],
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 12,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    },
    "summary": {
      "total_subjects": 12,
      "active_subjects": 10,
      "inactive_subjects": 2,
      "total_topics": 456,
      "total_courses": 89,
      "total_students": 25670
    }
  }
}
```

#### GET /subjects/{id}

**Purpose**: Get detailed subject information with complete analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Subject UUID or code

**Response**:

```json
{
  "success": true,
  "data": {
    "subject": {
      "id": "uuid",
      "name": "Physics",
      "code": "PHY",
      "description": "Comprehensive physics subject covering all major topics for competitive exam preparation",
      "category": "Science",
      "icon": "atom",
      "color": "#3b82f6",
      "banner_image": "https://cdn.utkrishta.com/subjects/physics-banner.jpg",
      "detailed_syllabus": {
        "total_chapters": 25,
        "total_topics": 156,
        "estimated_study_hours": 300,
        "chapters": [
          {
            "id": "uuid",
            "name": "Mechanics",
            "description": "Study of motion and forces",
            "order": 1,
            "topics": [
              {
                "id": "uuid",
                "name": "Laws of Motion",
                "description": "Newton's laws and their applications",
                "difficulty": "medium",
                "weightage": 8,
                "estimated_hours": 12,
                "subtopics": [
                  "Newton's First Law",
                  "Newton's Second Law",
                  "Newton's Third Law",
                  "Applications and Problem Solving"
                ]
              }
            ]
          }
        ]
      },
      "exam_mapping": [
        {
          "exam_id": "uuid",
          "exam_name": "JEE Main",
          "total_marks": 100,
          "questions": 30,
          "weightage": 33.33,
          "difficulty_distribution": {
            "easy": 30,
            "medium": 50,
            "hard": 20
          },
          "topic_weightage": [
            {
              "topic": "Mechanics",
              "weightage": 25,
              "marks": 25,
              "questions": 7
            }
          ]
        }
      ],
      "performance_analytics": {
        "overall_statistics": {
          "total_students": 3456,
          "average_score": 76.2,
          "pass_rate": 84.5,
          "improvement_rate": 15.3,
          "difficulty_rating": 7.8
        },
        "topic_wise_performance": [
          {
            "topic": "Mechanics",
            "average_score": 82.3,
            "difficulty_rating": 6.5,
            "student_feedback": "Well explained concepts",
            "common_mistakes": [
              "Sign convention errors",
              "Unit conversion mistakes"
            ]
          }
        ],
        "monthly_trends": [
          {
            "month": "2024-01",
            "students_enrolled": 234,
            "average_score": 78.5,
            "completion_rate": 82.1
          }
        ]
      },
      "study_resources": {
        "video_lectures": {
          "total": 156,
          "duration": 234,
          "most_watched": [
            {
              "title": "Laws of Motion Explained",
              "views": 2345,
              "rating": 4.8
            }
          ]
        },
        "pdf_notes": {
          "total": 45,
          "downloads": 12340,
          "most_downloaded": [
            {
              "title": "Physics Formula Sheet",
              "downloads": 2340,
              "rating": 4.9
            }
          ]
        }
      }
    }
  }
}
```

#### POST /subjects

**Purpose**: Create new subject
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "Chemistry",
  "code": "CHE",
  "description": "Study of matter, its properties, composition, and reactions",
  "category": "Science",
  "icon": "flask",
  "color": "#10b981",
  "exams": ["uuid1", "uuid2"],
  "topics": [
    {
      "name": "Organic Chemistry",
      "description": "Study of carbon compounds",
      "difficulty_level": "hard",
      "weightage": 30,
      "estimated_hours": 60
    }
  ],
  "prerequisites": ["Basic Chemistry", "Class 11 Chemistry"],
  "learning_outcomes": [
    "Understand chemical reactions",
    "Master organic chemistry concepts",
    "Solve numerical problems"
  ],
  "difficulty_level": "medium",
  "is_active": true
}
```

**Response**:

```json
{
  "success": true,
  "message": "Subject created successfully",
  "data": {
    "subject": {
      "id": "uuid",
      "name": "Chemistry",
      "code": "CHE",
      "status": "active",
      "created_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### PUT /subjects/{id}

**Purpose**: Update subject information
**Authentication**: Required (Admin/Super Admin)
**Request Body**: Same as POST but with optional fields

#### DELETE /subjects/{id}

**Purpose**: Delete subject (soft delete)
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "message": "Subject deleted successfully",
  "data": {
    "deleted_at": "2024-01-25T10:30:00Z"
  }
}
```

#### GET /subjects/{id}/topics

**Purpose**: Get detailed topics for a subject
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "data": {
    "topics": [
      {
        "id": "uuid",
        "name": "Mechanics",
        "description": "Study of motion, forces, and energy",
        "order": 1,
        "difficulty_level": "medium",
        "weightage": 25,
        "estimated_hours": 40,
        "subtopics": [
          {
            "id": "uuid",
            "name": "Laws of Motion",
            "description": "Newton's three laws and applications",
            "order": 1,
            "difficulty": "medium",
            "estimated_hours": 12,
            "key_concepts": [
              "Inertia",
              "Force and acceleration",
              "Action-reaction pairs"
            ],
            "formulas": ["F = ma", "F₁₂ = -F₂₁"]
          }
        ],
        "learning_objectives": [
          "Understand motion concepts",
          "Apply Newton's laws",
          "Solve motion problems"
        ]
      }
    ]
  }
}
```

#### POST /subjects/{id}/topics

**Purpose**: Add new topic to subject
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "Modern Physics",
  "description": "Quantum mechanics, relativity, atomic structure",
  "order": 10,
  "difficulty_level": "hard",
  "weightage": 15,
  "estimated_hours": 35,
  "learning_objectives": [
    "Understand quantum concepts",
    "Learn about atomic structure",
    "Apply modern physics principles"
  ],
  "subtopics": [
    {
      "name": "Photoelectric Effect",
      "description": "Einstein's explanation of photoelectric effect",
      "difficulty": "medium",
      "estimated_hours": 8
    }
  ]
}
```

### 3.2 Class Management APIs

#### GET /classes

**Purpose**: Get list of classes with comprehensive details
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in class title, instructor name
- `type` (optional): `live`, `recorded`, `hybrid`
- `status` (optional): `scheduled`, `live`, `completed`, `cancelled`
- `date_from` (optional): Filter classes from date (YYYY-MM-DD)
- `date_to` (optional): Filter classes to date (YYYY-MM-DD)
- `instructor` (optional): Instructor ID filter
- `subject` (optional): Subject ID filter
- `course` (optional): Course ID filter
- `sort_by` (optional): `scheduled_at`, `created_at`, `title`, `attendance`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "classes": [
      {
        "id": "uuid",
        "title": "JEE Main Physics - Laws of Motion",
        "description": "Comprehensive discussion on Newton's laws with problem-solving techniques",
        "type": "live",
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete",
          "subject": "Physics"
        },
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "instructor": {
          "id": "uuid",
          "name": "Dr. Rajesh Kumar",
          "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
          "qualification": "Ph.D. in Physics",
          "experience": 15,
          "rating": 4.8
        },
        "schedule": {
          "start_time": "2024-01-25T16:00:00Z",
          "end_time": "2024-01-25T17:30:00Z",
          "duration": 90,
          "timezone": "Asia/Kolkata",
          "is_recurring": false,
          "class_number": 15,
          "total_classes": 60
        },
        "meeting_details": {
          "platform": "zoom",
          "meeting_id": "123-456-789",
          "meeting_url": "https://zoom.us/j/123456789",
          "passcode": "physics123",
          "waiting_room_enabled": true,
          "recording_enabled": true,
          "auto_recording": true
        },
        "enrollment": {
          "max_capacity": 100,
          "enrolled_students": 67,
          "attended_students": 58,
          "waitlist_count": 5,
          "attendance_rate": 86.6
        },
        "content": {
          "topics_covered": [
            "Newton's First Law",
            "Newton's Second Law",
            "Applications and Problem Solving"
          ],
          "learning_objectives": [
            "Understand inertia concept",
            "Apply F=ma in problems",
            "Solve complex motion problems"
          ],
          "materials": [
            {
              "id": "uuid",
              "title": "Laws of Motion Notes",
              "type": "pdf",
              "url": "https://cdn.utkrishta.com/materials/laws-of-motion.pdf",
              "size": 2048000
            }
          ]
        },
        "recording": {
          "is_recorded": true,
          "recording_url": "https://cdn.utkrishta.com/recordings/class-123.mp4",
          "recording_duration": 88,
          "processing_status": "completed",
          "views": 234
        },
        "analytics": {
          "attendance_rate": 86.6,
          "engagement_score": 8.2,
          "questions_asked": 23,
          "average_rating": 4.7,
          "completion_rate": 92.3
        },
        "status": "completed",
        "is_public": false,
        "tags": ["jee_main", "physics", "mechanics", "newton_laws"],
        "created_at": "2024-01-20T10:00:00Z",
        "updated_at": "2024-01-25T17:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 156,
      "total_pages": 7,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_classes": 156,
      "scheduled_classes": 23,
      "completed_classes": 89,
      "cancelled_classes": 5,
      "live_classes": 2,
      "average_attendance": 84.5
    }
  }
}
```

#### GET /classes/{id}

**Purpose**: Get detailed class information
**Authentication**: Required (Admin/Super Admin)
**Response**: Complete class object with all details, attendance, recordings, and analytics

#### POST /classes

**Purpose**: Create new class
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "title": "NEET Biology - Human Physiology",
  "description": "Detailed study of human organ systems with diagrams and examples",
  "type": "live",
  "course_id": "uuid",
  "subject_id": "uuid",
  "instructor_id": "uuid",
  "schedule": {
    "start_time": "2024-02-01T16:00:00Z",
    "duration": 90,
    "timezone": "Asia/Kolkata",
    "is_recurring": true,
    "recurrence_pattern": {
      "type": "weekly",
      "days": ["monday", "wednesday", "friday"],
      "end_date": "2024-06-30",
      "total_occurrences": 36
    }
  },
  "meeting_details": {
    "platform": "zoom",
    "waiting_room_enabled": true,
    "recording_enabled": true,
    "auto_recording": true,
    "auto_generate_meeting": true
  },
  "enrollment": {
    "max_capacity": 150,
    "registration_required": true,
    "registration_deadline_hours": 2,
    "allow_waitlist": true
  },
  "content": {
    "topics_covered": ["Circulatory System", "Respiratory System"],
    "learning_objectives": [
      "Understand heart structure",
      "Learn blood circulation",
      "Master respiratory mechanisms"
    ]
  },
  "is_public": false,
  "tags": ["neet", "biology", "physiology"],
  "send_notifications": true
}
```

#### PUT /classes/{id}

**Purpose**: Update class information
**Authentication**: Required (Admin/Super Admin)
**Request Body**: Same as POST but with optional fields

#### DELETE /classes/{id}

**Purpose**: Delete class (soft delete)
**Authentication**: Required (Admin/Super Admin)

#### POST /classes/{id}/start

**Purpose**: Start live class session
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "message": "Class session started successfully",
  "data": {
    "meeting_url": "https://zoom.us/j/123456789",
    "instructor_url": "https://zoom.us/j/123456789?role=1",
    "started_at": "2024-01-25T16:00:00Z",
    "status": "live"
  }
}
```

#### POST /classes/{id}/end

**Purpose**: End live class session
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "message": "Class session ended successfully",
  "data": {
    "ended_at": "2024-01-25T17:30:00Z",
    "duration": 90,
    "attendance_count": 58,
    "recording_url": "https://cdn.utkrishta.com/recordings/class-123.mp4",
    "status": "completed"
  }
}
```

#### GET /classes/{id}/attendance

**Purpose**: Get class attendance details
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "data": {
    "attendance_summary": {
      "total_enrolled": 67,
      "students_attended": 58,
      "attendance_rate": 86.6,
      "on_time_attendance": 52,
      "late_attendance": 6,
      "early_leavers": 8,
      "average_duration": 82
    },
    "attendance_list": [
      {
        "student_id": "uuid",
        "student_name": "Rahul Sharma",
        "student_email": "<EMAIL>",
        "join_time": "2024-01-25T16:02:00Z",
        "leave_time": "2024-01-25T17:28:00Z",
        "duration_attended": 86,
        "status": "present",
        "participation_score": 8.5,
        "questions_asked": 3,
        "chat_messages": 8
      }
    ],
    "attendance_analytics": {
      "punctuality_rate": 77.6,
      "completion_rate": 86.2,
      "engagement_rate": 82.8,
      "average_participation": 7.8
    }
  }
}
```

#### POST /classes/{id}/attendance

**Purpose**: Mark attendance for class (bulk update)
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "attendance": [
    {
      "student_id": "uuid",
      "status": "present",
      "join_time": "2024-01-25T16:02:00Z",
      "leave_time": "2024-01-25T17:28:00Z",
      "participation_score": 8.5
    },
    {
      "student_id": "uuid",
      "status": "absent",
      "reason": "sick"
    }
  ],
  "auto_calculate_duration": true,
  "send_notifications": true
}
```

#### GET /classes/stats

**Purpose**: Get overall class statistics
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_classes": 156,
      "scheduled_classes": 23,
      "completed_classes": 89,
      "cancelled_classes": 5,
      "live_classes": 2,
      "average_attendance": 84.5,
      "total_students": 2340,
      "total_hours_taught": 13920
    },
    "attendance_analytics": {
      "overall_attendance_rate": 84.5,
      "punctuality_rate": 78.9,
      "completion_rate": 86.7,
      "engagement_rate": 82.3
    },
    "subject_wise_stats": [
      {
        "subject": "Physics",
        "classes_count": 45,
        "attendance_rate": 87.3,
        "student_satisfaction": 4.6,
        "average_duration": 88
      }
    ],
    "instructor_performance": [
      {
        "instructor_id": "uuid",
        "name": "Dr. Rajesh Kumar",
        "classes_conducted": 45,
        "average_attendance": 87.3,
        "student_rating": 4.8,
        "engagement_score": 8.9
      }
    ]
  }
}
```

---

## 🎯 Subject & Class Management API Summary

### **Key Features Implemented:**

1. **📘 Complete Subject Management**: CRUD operations with detailed subject information
2. **🏫 Comprehensive Class System**: Full class lifecycle management
3. **📊 Advanced Analytics**: Performance tracking and insights
4. **👥 Attendance Management**: Real-time attendance tracking and analytics
5. **📖 Topic Organization**: Hierarchical topic and subtopic management
6. **🎓 Instructor Integration**: Complete instructor performance tracking

### **API Endpoints Summary:**

| Module       | Endpoint                   | Method | Purpose                      |
| ------------ | -------------------------- | ------ | ---------------------------- |
| **Subjects** | `/subjects`                | GET    | List subjects with filtering |
|              | `/subjects/{id}`           | GET    | Detailed subject information |
|              | `/subjects`                | POST   | Create new subject           |
|              | `/subjects/{id}`           | PUT    | Update subject               |
|              | `/subjects/{id}`           | DELETE | Delete subject               |
|              | `/subjects/{id}/topics`    | GET    | Get subject topics           |
|              | `/subjects/{id}/topics`    | POST   | Add topic                    |
| **Classes**  | `/classes`                 | GET    | List classes with details    |
|              | `/classes/{id}`            | GET    | Detailed class information   |
|              | `/classes`                 | POST   | Create new class             |
|              | `/classes/{id}`            | PUT    | Update class                 |
|              | `/classes/{id}`            | DELETE | Delete class                 |
|              | `/classes/{id}/start`      | POST   | Start live class             |
|              | `/classes/{id}/end`        | POST   | End live class               |
|              | `/classes/{id}/attendance` | GET    | Class attendance             |
|              | `/classes/{id}/attendance` | POST   | Mark attendance              |
|              | `/classes/stats`           | GET    | Class statistics             |

### **Business Logic Covered:**

✅ **Subject Hierarchy**: Subjects → Topics → Subtopics structure
✅ **Class Lifecycle**: Scheduled → Live → Completed workflow
✅ **Attendance Tracking**: Real-time attendance with participation scores
✅ **Performance Analytics**: Subject-wise and instructor performance
✅ **Meeting Integration**: Zoom/Teams integration for live classes
✅ **Recording Management**: Automatic recording and processing
✅ **Notification System**: Automated notifications for class events

### **Technical Features:**

🔐 **Secure Authentication**: JWT-based access control
📱 **Mobile Optimized**: Responsive data structures
🔍 **Advanced Search**: Multi-criteria filtering and sorting
📊 **Rich Analytics**: Performance metrics and insights
⚡ **Real-time Updates**: Live class status and attendance
🎯 **Flexible Scheduling**: Recurring classes and time zone support
📹 **Video Integration**: Meeting platform integration

**🚀 The Subject & Class Management APIs are now complete and production-ready for backend implementation!**

---

## 4️⃣ PDF NOTES UPLOAD & MANAGEMENT APIs

### 4.1 PDF Notes Upload APIs

#### POST /pdf-notes/upload

**Purpose**: Upload PDF notes with metadata
**Authentication**: Required (Admin/Super Admin)
**Request**: Multipart form data
**Form Fields**:

- `file`: PDF file (required, max size: 50MB)
- `title`: Note title (required, max length: 200)
- `description`: Note description (optional, max length: 1000)
- `subject_id`: Subject ID (required)
- `course_id`: Course ID (optional)
- `topic_id`: Topic ID (optional)
- `tags`: Comma-separated tags (optional)
- `is_free`: Boolean (default: false)
- `difficulty_level`: `easy`, `medium`, `hard` (optional)
- `price`: Price in INR (optional, default: 0)

**Response**:

```json
{
  "success": true,
  "message": "PDF notes uploaded successfully",
  "data": {
    "pdf_note": {
      "id": "uuid",
      "title": "Physics Formula Sheet - Mechanics",
      "description": "Comprehensive formula sheet covering all mechanics topics",
      "filename": "physics-mechanics-formulas.pdf",
      "original_filename": "Mechanics Formulas.pdf",
      "file_size": 2048000,
      "file_url": "https://cdn.utkrishta.com/pdf-notes/physics-mechanics-formulas.pdf",
      "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/physics-mechanics-formulas.jpg",
      "preview_url": "https://cdn.utkrishta.com/previews/physics-mechanics-formulas.html",
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete"
      },
      "topic": {
        "id": "uuid",
        "name": "Mechanics"
      },
      "metadata": {
        "pages": 15,
        "file_type": "application/pdf",
        "created_date": "2024-01-25T10:30:00Z",
        "author": "Dr. Rajesh Kumar",
        "version": "1.0",
        "language": "English"
      },
      "processing_status": "completed",
      "ocr_status": "completed",
      "searchable_text": true,
      "tags": ["formulas", "mechanics", "jee_main", "physics"],
      "difficulty_level": "medium",
      "is_free": false,
      "price": 0,
      "download_count": 0,
      "view_count": 0,
      "rating": 0,
      "review_count": 0,
      "status": "active",
      "uploaded_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin"
      },
      "created_at": "2024-01-25T10:30:00Z",
      "updated_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### POST /pdf-notes/bulk-upload

**Purpose**: Upload multiple PDF notes at once
**Authentication**: Required (Admin/Super Admin)
**Request**: Multipart form data with multiple files
**Form Fields**:

- `files[]`: Multiple PDF files (required, max 10 files)
- `subject_id`: Subject ID (required)
- `course_id`: Course ID (optional)
- `topic_id`: Topic ID (optional)
- `default_tags`: Default tags for all files (optional)
- `is_free`: Default free status (default: false)
- `difficulty_level`: Default difficulty (optional)

**Response**:

```json
{
  "success": true,
  "message": "Bulk upload initiated successfully",
  "data": {
    "batch_id": "uuid",
    "uploaded_files": 8,
    "failed_files": 2,
    "total_files": 10,
    "results": [
      {
        "filename": "physics-formulas.pdf",
        "status": "success",
        "pdf_note_id": "uuid",
        "message": "Uploaded successfully"
      },
      {
        "filename": "chemistry-notes.pdf",
        "status": "failed",
        "error": "File size exceeds limit (50MB)",
        "error_code": "FILE_SIZE_EXCEEDED"
      }
    ],
    "processing_status": "in_progress",
    "estimated_completion": "2024-01-25T11:00:00Z"
  }
}
```

#### GET /pdf-notes/upload-status/{batch_id}

**Purpose**: Get bulk upload processing status
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `batch_id`: Batch upload UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "batch_id": "uuid",
    "status": "processing",
    "progress": 75,
    "total_files": 10,
    "processed_files": 7,
    "successful_files": 6,
    "failed_files": 1,
    "estimated_completion": "2024-01-25T11:00:00Z",
    "current_file": "mathematics-calculus.pdf",
    "processing_details": [
      {
        "filename": "physics-formulas.pdf",
        "status": "completed",
        "pdf_note_id": "uuid",
        "processing_time": 45
      },
      {
        "filename": "chemistry-notes.pdf",
        "status": "failed",
        "error": "OCR processing failed",
        "retry_count": 2
      }
    ]
  }
}
```

### 4.2 PDF Notes Management APIs

#### GET /pdf-notes

**Purpose**: Get list of PDF notes with advanced filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, description, tags
- `subject` (optional): Subject ID filter
- `course` (optional): Course ID filter
- `topic` (optional): Topic ID filter
- `difficulty` (optional): `easy`, `medium`, `hard`
- `is_free` (optional): `true`, `false`
- `status` (optional): `active`, `inactive`, `processing`
- `uploaded_by` (optional): Uploader ID filter
- `sort_by` (optional): `created_at`, `title`, `download_count`, `rating`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "pdf_notes": [
      {
        "id": "uuid",
        "title": "Physics Formula Sheet - Mechanics",
        "description": "Comprehensive formula sheet covering all mechanics topics",
        "filename": "physics-mechanics-formulas.pdf",
        "file_size": 2048000,
        "file_url": "https://cdn.utkrishta.com/pdf-notes/physics-mechanics-formulas.pdf",
        "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/physics-mechanics-formulas.jpg",
        "preview_url": "https://cdn.utkrishta.com/previews/physics-mechanics-formulas.html",
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete"
        },
        "topic": {
          "id": "uuid",
          "name": "Mechanics"
        },
        "metadata": {
          "pages": 15,
          "author": "Dr. Rajesh Kumar",
          "version": "1.0",
          "language": "English"
        },
        "tags": ["formulas", "mechanics", "jee_main", "physics"],
        "difficulty_level": "medium",
        "is_free": false,
        "price": 0,
        "download_count": 1234,
        "view_count": 5678,
        "rating": 4.8,
        "review_count": 89,
        "status": "active",
        "uploaded_by": {
          "id": "uuid",
          "name": "Admin User",
          "role": "admin"
        },
        "created_at": "2024-01-25T10:30:00Z",
        "updated_at": "2024-01-25T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 456,
      "total_pages": 19,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_notes": 456,
      "active_notes": 423,
      "inactive_notes": 33,
      "total_downloads": 123456,
      "total_views": 567890,
      "average_rating": 4.6
    }
  }
}
```

#### GET /pdf-notes/{id}

**Purpose**: Get detailed PDF note information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "pdf_note": {
      "id": "uuid",
      "title": "Physics Formula Sheet - Mechanics",
      "description": "Comprehensive formula sheet covering all mechanics topics including laws of motion, work-energy theorem, rotational dynamics, and more.",
      "filename": "physics-mechanics-formulas.pdf",
      "original_filename": "Mechanics Formulas.pdf",
      "file_size": 2048000,
      "file_url": "https://cdn.utkrishta.com/pdf-notes/physics-mechanics-formulas.pdf",
      "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/physics-mechanics-formulas.jpg",
      "preview_url": "https://cdn.utkrishta.com/previews/physics-mechanics-formulas.html",
      "download_url": "https://cdn.utkrishta.com/downloads/physics-mechanics-formulas.pdf",
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete",
        "instructor": "Dr. Rajesh Kumar"
      },
      "topic": {
        "id": "uuid",
        "name": "Mechanics",
        "description": "Study of motion and forces"
      },
      "metadata": {
        "pages": 15,
        "file_type": "application/pdf",
        "created_date": "2024-01-25T10:30:00Z",
        "author": "Dr. Rajesh Kumar",
        "version": "1.0",
        "language": "English",
        "keywords": ["mechanics", "formulas", "physics", "jee"],
        "file_hash": "sha256:abc123..."
      },
      "processing_info": {
        "processing_status": "completed",
        "ocr_status": "completed",
        "searchable_text": true,
        "text_extraction_accuracy": 98.5,
        "processing_time": 45
      },
      "content_analysis": {
        "topics_covered": [
          "Laws of Motion",
          "Work-Energy Theorem",
          "Rotational Dynamics",
          "Simple Harmonic Motion"
        ],
        "formulas_count": 67,
        "diagrams_count": 23,
        "examples_count": 15,
        "difficulty_analysis": {
          "easy": 30,
          "medium": 50,
          "hard": 20
        }
      },
      "tags": ["formulas", "mechanics", "jee_main", "physics"],
      "difficulty_level": "medium",
      "is_free": false,
      "price": 0,
      "access_level": "premium",
      "download_count": 1234,
      "view_count": 5678,
      "unique_viewers": 3456,
      "rating": 4.8,
      "review_count": 89,
      "status": "active",
      "uploaded_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin",
        "avatar": "https://cdn.utkrishta.com/avatars/admin.jpg"
      },
      "reviews": [
        {
          "id": "uuid",
          "student_id": "uuid",
          "student_name": "Rahul Sharma",
          "rating": 5,
          "comment": "Excellent formula sheet! Very helpful for quick revision.",
          "created_at": "2024-01-20T15:30:00Z"
        }
      ],
      "related_notes": [
        {
          "id": "uuid",
          "title": "Physics Solved Examples - Mechanics",
          "rating": 4.7,
          "download_count": 987
        }
      ],
      "created_at": "2024-01-25T10:30:00Z",
      "updated_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### PUT /pdf-notes/{id}

**Purpose**: Update PDF note metadata
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID
  **Request Body**:

```json
{
  "title": "Updated Physics Formula Sheet - Mechanics",
  "description": "Updated comprehensive formula sheet with new examples",
  "subject_id": "uuid",
  "course_id": "uuid",
  "topic_id": "uuid",
  "tags": ["formulas", "mechanics", "jee_main", "physics", "updated"],
  "difficulty_level": "medium",
  "is_free": false,
  "price": 0,
  "status": "active"
}
```

**Response**:

```json
{
  "success": true,
  "message": "PDF note updated successfully",
  "data": {
    "pdf_note": {
      "id": "uuid",
      "title": "Updated Physics Formula Sheet - Mechanics",
      "updated_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### DELETE /pdf-notes/{id}

**Purpose**: Delete PDF note (soft delete)
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "message": "PDF note deleted successfully",
  "data": {
    "deleted_at": "2024-01-25T10:30:00Z"
  }
}
```

#### GET /pdf-notes/{id}/download

**Purpose**: Download PDF note file
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID
  **Response**: File download with appropriate headers

```
Content-Type: application/pdf
Content-Disposition: attachment; filename="physics-mechanics-formulas.pdf"
Content-Length: 2048000
```

#### GET /pdf-notes/{id}/preview

**Purpose**: Get PDF preview/viewer URL
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "preview_url": "https://cdn.utkrishta.com/previews/physics-mechanics-formulas.html",
    "viewer_type": "pdf_js",
    "pages": 15,
    "thumbnail_urls": [
      "https://cdn.utkrishta.com/thumbnails/physics-mechanics-formulas-page-1.jpg",
      "https://cdn.utkrishta.com/thumbnails/physics-mechanics-formulas-page-2.jpg"
    ],
    "viewer_config": {
      "enable_download": true,
      "enable_print": true,
      "enable_search": true,
      "zoom_levels": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
    }
  }
}
```

### 4.3 PDF Notes Search & Analytics APIs

#### GET /pdf-notes/search

**Purpose**: Advanced search in PDF notes content
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `query` (required): Search query
- `subject` (optional): Subject filter
- `difficulty` (optional): Difficulty filter
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25)

**Response**:

```json
{
  "success": true,
  "data": {
    "search_results": [
      {
        "pdf_note_id": "uuid",
        "title": "Physics Formula Sheet - Mechanics",
        "relevance_score": 95.6,
        "matched_content": [
          {
            "page": 3,
            "snippet": "Newton's laws of motion are fundamental principles...",
            "highlight": "Newton's laws"
          }
        ],
        "subject": "Physics",
        "topic": "Mechanics",
        "rating": 4.8,
        "download_count": 1234
      }
    ],
    "search_metadata": {
      "query": "newton laws",
      "total_results": 23,
      "search_time": 0.045,
      "suggestions": ["newton's laws", "laws of motion", "mechanics formulas"]
    },
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 23,
      "total_pages": 1
    }
  }
}
```

#### GET /pdf-notes/{id}/analytics

**Purpose**: Get PDF note analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "usage_analytics": {
      "total_downloads": 1234,
      "total_views": 5678,
      "unique_viewers": 3456,
      "average_view_duration": 8.5,
      "bounce_rate": 15.2
    },
    "rating_analytics": {
      "average_rating": 4.8,
      "total_reviews": 89,
      "rating_distribution": {
        "5": 56,
        "4": 23,
        "3": 8,
        "2": 2,
        "1": 0
      }
    },
    "download_trends": [
      {
        "date": "2024-01-01",
        "downloads": 45,
        "views": 123
      }
    ],
    "popular_pages": [
      {
        "page_number": 3,
        "view_count": 234,
        "average_time": 45
      }
    ],
    "user_engagement": {
      "most_active_users": [
        {
          "student_id": "uuid",
          "student_name": "Rahul Sharma",
          "downloads": 5,
          "views": 12
        }
      ],
      "geographic_distribution": [
        {
          "state": "Maharashtra",
          "downloads": 234,
          "percentage": 18.9
        }
      ]
    }
  }
}
```

#### GET /pdf-notes/stats

**Purpose**: Get overall PDF notes statistics
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_notes": 456,
      "active_notes": 423,
      "inactive_notes": 33,
      "total_downloads": 123456,
      "total_views": 567890,
      "total_file_size": 2147483648,
      "average_rating": 4.6
    },
    "subject_distribution": [
      {
        "subject": "Physics",
        "notes_count": 156,
        "downloads": 45678,
        "average_rating": 4.7
      },
      {
        "subject": "Chemistry",
        "notes_count": 134,
        "downloads": 38901,
        "average_rating": 4.5
      },
      {
        "subject": "Mathematics",
        "notes_count": 166,
        "downloads": 38877,
        "average_rating": 4.6
      }
    ],
    "difficulty_analysis": [
      {
        "difficulty": "easy",
        "count": 137,
        "average_downloads": 234,
        "average_rating": 4.8
      },
      {
        "difficulty": "medium",
        "count": 228,
        "average_downloads": 345,
        "average_rating": 4.6
      },
      {
        "difficulty": "hard",
        "count": 91,
        "average_downloads": 189,
        "average_rating": 4.4
      }
    ],
    "popular_notes": [
      {
        "id": "uuid",
        "title": "Physics Formula Sheet - Complete",
        "downloads": 5678,
        "rating": 4.9,
        "subject": "Physics"
      }
    ],
    "upload_trends": [
      {
        "month": "2024-01",
        "uploads": 23,
        "downloads": 12345,
        "views": 45678
      }
    ],
    "top_uploaders": [
      {
        "uploader_id": "uuid",
        "name": "Dr. Rajesh Kumar",
        "uploads": 45,
        "total_downloads": 23456,
        "average_rating": 4.8
      }
    ]
  }
}
```

### 4.4 PDF Processing & OCR APIs

#### POST /pdf-notes/{id}/reprocess

**Purpose**: Reprocess PDF for OCR and text extraction
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "message": "PDF reprocessing started",
  "data": {
    "job_id": "uuid",
    "estimated_completion": "2024-01-25T11:00:00Z",
    "processing_steps": [
      "OCR text extraction",
      "Content analysis",
      "Thumbnail generation",
      "Search indexing"
    ]
  }
}
```

#### GET /pdf-notes/{id}/processing-status

**Purpose**: Get PDF processing status
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "processing_status": "completed",
    "ocr_status": "completed",
    "progress": 100,
    "text_extraction_accuracy": 98.5,
    "searchable_text": true,
    "processing_time": 45,
    "error_message": null,
    "processing_steps": [
      {
        "step": "OCR text extraction",
        "status": "completed",
        "duration": 30
      },
      {
        "step": "Content analysis",
        "status": "completed",
        "duration": 10
      },
      {
        "step": "Thumbnail generation",
        "status": "completed",
        "duration": 3
      },
      {
        "step": "Search indexing",
        "status": "completed",
        "duration": 2
      }
    ]
  }
}
```

#### GET /pdf-notes/{id}/text-content

**Purpose**: Get extracted text content from PDF
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: PDF note UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "text_content": "Physics Formula Sheet - Mechanics\n\nNewton's Laws of Motion...",
    "pages": [
      {
        "page_number": 1,
        "text": "Physics Formula Sheet - Mechanics\n\nPage 1 content...",
        "confidence": 98.5,
        "word_count": 234
      }
    ],
    "extraction_metadata": {
      "total_characters": 15678,
      "total_words": 2345,
      "language": "en",
      "confidence": 98.5,
      "extraction_method": "tesseract_ocr"
    }
  }
}
```

---

## 🎯 PDF Notes Upload & Management API Summary

### **Key Features Implemented:**

1. **📤 Complete Upload System**: Single and bulk PDF upload with metadata
2. **🔍 Advanced Search**: Full-text search with OCR integration
3. **📊 Rich Analytics**: Usage tracking, ratings, and performance metrics
4. **🖼️ Preview System**: PDF.js integration with thumbnail generation
5. **⚙️ Processing Pipeline**: OCR, text extraction, and content analysis
6. **📱 Mobile Optimized**: Responsive data structures and APIs

### **API Endpoints Summary:**

| Module                 | Endpoint                              | Method | Purpose             |
| ---------------------- | ------------------------------------- | ------ | ------------------- |
| **Upload**             | `/pdf-notes/upload`                   | POST   | Single PDF upload   |
|                        | `/pdf-notes/bulk-upload`              | POST   | Bulk PDF upload     |
|                        | `/pdf-notes/upload-status/{batch_id}` | GET    | Upload status       |
| **Management**         | `/pdf-notes`                          | GET    | List PDF notes      |
|                        | `/pdf-notes/{id}`                     | GET    | Detailed PDF info   |
|                        | `/pdf-notes/{id}`                     | PUT    | Update PDF metadata |
|                        | `/pdf-notes/{id}`                     | DELETE | Delete PDF          |
|                        | `/pdf-notes/{id}/download`            | GET    | Download PDF        |
|                        | `/pdf-notes/{id}/preview`             | GET    | PDF preview         |
| **Search & Analytics** | `/pdf-notes/search`                   | GET    | Advanced search     |
|                        | `/pdf-notes/{id}/analytics`           | GET    | PDF analytics       |
|                        | `/pdf-notes/stats`                    | GET    | Overall statistics  |
| **Processing**         | `/pdf-notes/{id}/reprocess`           | POST   | Reprocess PDF       |
|                        | `/pdf-notes/{id}/processing-status`   | GET    | Processing status   |
|                        | `/pdf-notes/{id}/text-content`        | GET    | Extracted text      |

### **Business Logic Covered:**

✅ **Upload Management**: Single and bulk upload with validation
✅ **Content Processing**: OCR, text extraction, thumbnail generation
✅ **Search Integration**: Full-text search with relevance scoring
✅ **Analytics Tracking**: Downloads, views, ratings, user engagement
✅ **Preview System**: PDF.js viewer with zoom and search
✅ **Access Control**: Free/premium content with pricing
✅ **Quality Assurance**: Processing status and error handling

### **Technical Features:**

🔐 **Secure Upload**: File validation, size limits, virus scanning
📱 **Mobile Optimized**: Responsive preview and download
🔍 **Advanced Search**: OCR-powered full-text search
📊 **Rich Analytics**: Usage patterns and performance metrics
⚡ **Async Processing**: Background OCR and content analysis
🎯 **CDN Integration**: Fast file delivery and caching
💾 **Storage Management**: Efficient file organization and cleanup

### **Processing Pipeline:**

1. **Upload Validation**: File type, size, and security checks
2. **Storage**: Secure file storage with CDN integration
3. **OCR Processing**: Text extraction with accuracy scoring
4. **Content Analysis**: Topic detection and difficulty assessment
5. **Thumbnail Generation**: Page previews and image optimization
6. **Search Indexing**: Full-text search preparation
7. **Analytics Setup**: Usage tracking initialization

**🚀 The PDF Notes Upload & Management APIs are now complete and production-ready for backend implementation!**

---

## 5️⃣ VIDEO UPLOAD INTERFACE APIs

### 5.1 Video Upload APIs

#### POST /videos/upload

**Purpose**: Upload video files with metadata for courses and lessons
**Authentication**: Required (Admin/Super Admin)
**Request**: Multipart form data
**Form Fields**:

- `file`: Video file (required, max size: 2GB)
- `title`: Video title (required, max length: 200)
- `description`: Video description (optional, max length: 2000)
- `subject_id`: Subject ID (required)
- `course_id`: Course ID (optional)
- `module_id`: Module ID (optional)
- `lesson_id`: Lesson ID (optional)
- `tags`: Comma-separated tags (optional)
- `is_free`: Boolean (default: false)
- `difficulty_level`: `easy`, `medium`, `hard` (optional)
- `video_type`: `lecture`, `demo`, `solution`, `concept` (required)
- `duration_estimate`: Estimated duration in seconds (optional)

**Response**:

```json
{
  "success": true,
  "message": "Video uploaded successfully",
  "data": {
    "video": {
      "id": "uuid",
      "title": "JEE Physics - Laws of Motion Explained",
      "description": "Comprehensive explanation of Newton's laws with practical examples",
      "filename": "jee-physics-laws-motion.mp4",
      "original_filename": "Laws of Motion Lecture.mp4",
      "file_size": 524288000,
      "file_url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion.mp4",
      "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/jee-physics-laws-motion.jpg",
      "preview_url": "https://cdn.utkrishta.com/previews/jee-physics-laws-motion-preview.mp4",
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete"
      },
      "module": {
        "id": "uuid",
        "title": "Mechanics"
      },
      "lesson": {
        "id": "uuid",
        "title": "Laws of Motion"
      },
      "metadata": {
        "duration": 0,
        "resolution": "pending",
        "format": "mp4",
        "codec": "pending",
        "bitrate": 0,
        "fps": 0,
        "file_type": "video/mp4"
      },
      "processing_status": "queued",
      "transcoding_status": "pending",
      "thumbnail_status": "pending",
      "upload_progress": 100,
      "tags": ["jee_main", "physics", "mechanics", "newton_laws"],
      "difficulty_level": "medium",
      "video_type": "lecture",
      "is_free": false,
      "view_count": 0,
      "like_count": 0,
      "rating": 0,
      "review_count": 0,
      "status": "processing",
      "uploaded_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin"
      },
      "created_at": "2024-01-25T10:30:00Z",
      "updated_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### POST /videos/upload-chunked

**Purpose**: Upload large video files using chunked upload
**Authentication**: Required (Admin/Super Admin)
**Request**: Multipart form data
**Form Fields**:

- `chunk`: Video chunk file (required)
- `chunk_number`: Current chunk number (required)
- `total_chunks`: Total number of chunks (required)
- `upload_id`: Unique upload session ID (required)
- `filename`: Original filename (required)
- `file_size`: Total file size (required)
- `chunk_size`: Size of current chunk (required)

**Response**:

```json
{
  "success": true,
  "message": "Chunk uploaded successfully",
  "data": {
    "upload_id": "uuid",
    "chunk_number": 3,
    "total_chunks": 10,
    "uploaded_chunks": 3,
    "progress": 30.0,
    "remaining_chunks": 7,
    "next_chunk_url": "/videos/upload-chunked",
    "status": "uploading"
  }
}
```

#### POST /videos/upload-complete

**Purpose**: Complete chunked upload and initiate processing
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "upload_id": "uuid",
  "title": "JEE Physics - Laws of Motion Explained",
  "description": "Comprehensive explanation of Newton's laws",
  "subject_id": "uuid",
  "course_id": "uuid",
  "module_id": "uuid",
  "lesson_id": "uuid",
  "tags": ["jee_main", "physics", "mechanics"],
  "difficulty_level": "medium",
  "video_type": "lecture",
  "is_free": false
}
```

**Response**:

```json
{
  "success": true,
  "message": "Video upload completed successfully",
  "data": {
    "video": {
      "id": "uuid",
      "title": "JEE Physics - Laws of Motion Explained",
      "upload_id": "uuid",
      "processing_status": "queued",
      "estimated_processing_time": "15-20 minutes",
      "created_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### GET /videos/upload-status/{upload_id}

**Purpose**: Get chunked upload status
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `upload_id`: Upload session UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "upload_id": "uuid",
    "status": "uploading",
    "progress": 75.0,
    "uploaded_chunks": 7,
    "total_chunks": 10,
    "remaining_chunks": 3,
    "file_size": 524288000,
    "uploaded_size": 393216000,
    "upload_speed": "2.5 MB/s",
    "estimated_completion": "2024-01-25T10:35:00Z",
    "failed_chunks": [],
    "retry_count": 0
  }
}
```

### 5.2 Video Management APIs

#### GET /videos

**Purpose**: Get list of videos with comprehensive filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, description, tags
- `subject` (optional): Subject ID filter
- `course` (optional): Course ID filter
- `module` (optional): Module ID filter
- `video_type` (optional): `lecture`, `demo`, `solution`, `concept`
- `difficulty` (optional): `easy`, `medium`, `hard`
- `status` (optional): `processing`, `ready`, `failed`, `archived`
- `is_free` (optional): `true`, `false`
- `uploaded_by` (optional): Uploader ID filter
- `sort_by` (optional): `created_at`, `title`, `duration`, `view_count`, `rating`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "videos": [
      {
        "id": "uuid",
        "title": "JEE Physics - Laws of Motion Explained",
        "description": "Comprehensive explanation of Newton's laws with practical examples",
        "filename": "jee-physics-laws-motion.mp4",
        "file_size": 524288000,
        "file_url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion.mp4",
        "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/jee-physics-laws-motion.jpg",
        "preview_url": "https://cdn.utkrishta.com/previews/jee-physics-laws-motion-preview.mp4",
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete"
        },
        "module": {
          "id": "uuid",
          "title": "Mechanics"
        },
        "lesson": {
          "id": "uuid",
          "title": "Laws of Motion"
        },
        "metadata": {
          "duration": 1800,
          "resolution": "1920x1080",
          "format": "mp4",
          "codec": "h264",
          "bitrate": 2500,
          "fps": 30,
          "file_type": "video/mp4"
        },
        "processing_info": {
          "processing_status": "completed",
          "transcoding_status": "completed",
          "thumbnail_status": "completed",
          "processing_time": 900
        },
        "quality_variants": [
          {
            "quality": "1080p",
            "url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion-1080p.mp4",
            "file_size": 524288000,
            "bitrate": 2500
          },
          {
            "quality": "720p",
            "url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion-720p.mp4",
            "file_size": 314572800,
            "bitrate": 1500
          },
          {
            "quality": "480p",
            "url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion-480p.mp4",
            "file_size": 157286400,
            "bitrate": 800
          }
        ],
        "tags": ["jee_main", "physics", "mechanics", "newton_laws"],
        "difficulty_level": "medium",
        "video_type": "lecture",
        "is_free": false,
        "view_count": 2345,
        "like_count": 189,
        "rating": 4.7,
        "review_count": 67,
        "status": "ready",
        "uploaded_by": {
          "id": "uuid",
          "name": "Admin User",
          "role": "admin"
        },
        "created_at": "2024-01-25T10:30:00Z",
        "updated_at": "2024-01-25T11:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 234,
      "total_pages": 10,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_videos": 234,
      "ready_videos": 198,
      "processing_videos": 12,
      "failed_videos": 3,
      "total_duration": 456789,
      "total_file_size": 52428800000,
      "total_views": 123456
    }
  }
}
```

#### GET /videos/{id}

**Purpose**: Get detailed video information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Video UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "video": {
      "id": "uuid",
      "title": "JEE Physics - Laws of Motion Explained",
      "description": "Comprehensive explanation of Newton's laws with practical examples and problem-solving techniques",
      "filename": "jee-physics-laws-motion.mp4",
      "original_filename": "Laws of Motion Lecture.mp4",
      "file_size": 524288000,
      "file_url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion.mp4",
      "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/jee-physics-laws-motion.jpg",
      "preview_url": "https://cdn.utkrishta.com/previews/jee-physics-laws-motion-preview.mp4",
      "streaming_url": "https://stream.utkrishta.com/videos/jee-physics-laws-motion/playlist.m3u8",
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete",
        "instructor": "Dr. Rajesh Kumar"
      },
      "module": {
        "id": "uuid",
        "title": "Mechanics",
        "order": 1
      },
      "lesson": {
        "id": "uuid",
        "title": "Laws of Motion",
        "order": 1
      },
      "metadata": {
        "duration": 1800,
        "resolution": "1920x1080",
        "format": "mp4",
        "codec": "h264",
        "bitrate": 2500,
        "fps": 30,
        "file_type": "video/mp4",
        "aspect_ratio": "16:9",
        "audio_codec": "aac",
        "audio_bitrate": 128
      },
      "processing_info": {
        "processing_status": "completed",
        "transcoding_status": "completed",
        "thumbnail_status": "completed",
        "processing_time": 900,
        "processed_at": "2024-01-25T11:00:00Z"
      },
      "quality_variants": [
        {
          "quality": "1080p",
          "url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion-1080p.mp4",
          "file_size": 524288000,
          "bitrate": 2500,
          "resolution": "1920x1080"
        },
        {
          "quality": "720p",
          "url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion-720p.mp4",
          "file_size": 314572800,
          "bitrate": 1500,
          "resolution": "1280x720"
        },
        {
          "quality": "480p",
          "url": "https://cdn.utkrishta.com/videos/jee-physics-laws-motion-480p.mp4",
          "file_size": 157286400,
          "bitrate": 800,
          "resolution": "854x480"
        }
      ],
      "chapters": [
        {
          "id": "uuid",
          "title": "Introduction to Newton's Laws",
          "start_time": 0,
          "end_time": 300,
          "thumbnail": "https://cdn.utkrishta.com/chapters/intro-newton-laws.jpg"
        },
        {
          "id": "uuid",
          "title": "First Law - Law of Inertia",
          "start_time": 300,
          "end_time": 600,
          "thumbnail": "https://cdn.utkrishta.com/chapters/first-law.jpg"
        }
      ],
      "tags": ["jee_main", "physics", "mechanics", "newton_laws"],
      "difficulty_level": "medium",
      "video_type": "lecture",
      "is_free": false,
      "view_count": 2345,
      "like_count": 189,
      "dislike_count": 12,
      "rating": 4.7,
      "review_count": 67,
      "status": "ready",
      "uploaded_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin",
        "avatar": "https://cdn.utkrishta.com/avatars/admin.jpg"
      },
      "analytics": {
        "total_watch_time": 4230000,
        "average_watch_time": 1800,
        "completion_rate": 76.8,
        "engagement_rate": 82.3,
        "retention_curve": [
          { "time": 0, "retention": 100 },
          { "time": 300, "retention": 95 },
          { "time": 600, "retention": 88 },
          { "time": 900, "retention": 82 },
          { "time": 1200, "retention": 78 },
          { "time": 1500, "retention": 72 },
          { "time": 1800, "retention": 68 }
        ]
      },
      "reviews": [
        {
          "id": "uuid",
          "student_id": "uuid",
          "student_name": "Rahul Sharma",
          "rating": 5,
          "comment": "Excellent explanation! Very clear and easy to understand.",
          "created_at": "2024-01-20T15:30:00Z"
        }
      ],
      "related_videos": [
        {
          "id": "uuid",
          "title": "JEE Physics - Work Energy Theorem",
          "duration": 1500,
          "rating": 4.6,
          "view_count": 1876
        }
      ],
      "created_at": "2024-01-25T10:30:00Z",
      "updated_at": "2024-01-25T11:00:00Z"
    }
  }
}
```

#### PUT /videos/{id}

**Purpose**: Update video metadata
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Video UUID
  **Request Body**:

```json
{
  "title": "Updated JEE Physics - Laws of Motion Explained",
  "description": "Updated comprehensive explanation with new examples",
  "subject_id": "uuid",
  "course_id": "uuid",
  "module_id": "uuid",
  "lesson_id": "uuid",
  "tags": ["jee_main", "physics", "mechanics", "newton_laws", "updated"],
  "difficulty_level": "medium",
  "video_type": "lecture",
  "is_free": false,
  "status": "ready"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Video updated successfully",
  "data": {
    "video": {
      "id": "uuid",
      "title": "Updated JEE Physics - Laws of Motion Explained",
      "updated_at": "2024-01-25T12:00:00Z"
    }
  }
}
```

#### DELETE /videos/{id}

**Purpose**: Delete video (soft delete)
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Video UUID

**Response**:

```json
{
  "success": true,
  "message": "Video deleted successfully",
  "data": {
    "deleted_at": "2024-01-25T12:00:00Z"
  }
}
```

#### GET /videos/{id}/processing-status

**Purpose**: Get video processing status
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Video UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "processing_status": "completed",
    "transcoding_status": "completed",
    "thumbnail_status": "completed",
    "progress": 100,
    "processing_time": 900,
    "error_message": null,
    "processing_steps": [
      {
        "step": "Video analysis",
        "status": "completed",
        "duration": 120,
        "started_at": "2024-01-25T10:30:00Z",
        "completed_at": "2024-01-25T10:32:00Z"
      },
      {
        "step": "Transcoding to 1080p",
        "status": "completed",
        "duration": 300,
        "started_at": "2024-01-25T10:32:00Z",
        "completed_at": "2024-01-25T10:37:00Z"
      },
      {
        "step": "Transcoding to 720p",
        "status": "completed",
        "duration": 240,
        "started_at": "2024-01-25T10:37:00Z",
        "completed_at": "2024-01-25T10:41:00Z"
      },
      {
        "step": "Transcoding to 480p",
        "status": "completed",
        "duration": 180,
        "started_at": "2024-01-25T10:41:00Z",
        "completed_at": "2024-01-25T10:44:00Z"
      },
      {
        "step": "Thumbnail generation",
        "status": "completed",
        "duration": 60,
        "started_at": "2024-01-25T10:44:00Z",
        "completed_at": "2024-01-25T10:45:00Z"
      }
    ],
    "quality_variants_ready": ["1080p", "720p", "480p"],
    "thumbnails_generated": 5,
    "chapters_detected": 2
  }
}
```

### 5.3 Video Analytics & Processing APIs

#### GET /videos/{id}/analytics

**Purpose**: Get detailed video analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Video UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "viewing_analytics": {
      "total_views": 2345,
      "unique_viewers": 1876,
      "total_watch_time": 4230000,
      "average_watch_time": 1800,
      "completion_rate": 76.8,
      "engagement_rate": 82.3,
      "bounce_rate": 12.4
    },
    "retention_analytics": {
      "retention_curve": [
        { "time": 0, "retention": 100, "viewers": 2345 },
        { "time": 300, "retention": 95, "viewers": 2228 },
        { "time": 600, "retention": 88, "viewers": 2064 },
        { "time": 900, "retention": 82, "viewers": 1923 },
        { "time": 1200, "retention": 78, "viewers": 1829 },
        { "time": 1500, "retention": 72, "viewers": 1688 },
        { "time": 1800, "retention": 68, "viewers": 1595 }
      ],
      "drop_off_points": [
        {
          "time": 450,
          "drop_percentage": 8.2,
          "reason": "Complex concept introduction"
        },
        {
          "time": 1350,
          "drop_percentage": 6.5,
          "reason": "Mathematical derivation"
        }
      ]
    },
    "engagement_metrics": {
      "like_count": 189,
      "dislike_count": 12,
      "like_ratio": 94.0,
      "comment_count": 45,
      "share_count": 23,
      "bookmark_count": 67
    },
    "rating_analytics": {
      "average_rating": 4.7,
      "total_ratings": 67,
      "rating_distribution": {
        "5": 42,
        "4": 18,
        "3": 5,
        "2": 2,
        "1": 0
      }
    },
    "geographic_distribution": [
      {
        "state": "Maharashtra",
        "views": 456,
        "percentage": 19.4,
        "average_watch_time": 1650
      },
      {
        "state": "Delhi",
        "views": 378,
        "percentage": 16.1,
        "average_watch_time": 1720
      }
    ],
    "device_analytics": [
      {
        "device_type": "mobile",
        "views": 1407,
        "percentage": 60.0,
        "average_watch_time": 1620
      },
      {
        "device_type": "desktop",
        "views": 703,
        "percentage": 30.0,
        "average_watch_time": 2100
      },
      {
        "device_type": "tablet",
        "views": 235,
        "percentage": 10.0,
        "average_watch_time": 1890
      }
    ],
    "quality_preferences": [
      {
        "quality": "720p",
        "usage": 45.2,
        "completion_rate": 78.9
      },
      {
        "quality": "480p",
        "usage": 32.1,
        "completion_rate": 82.3
      },
      {
        "quality": "1080p",
        "usage": 22.7,
        "completion_rate": 71.5
      }
    ]
  }
}
```

#### POST /videos/{id}/reprocess

**Purpose**: Reprocess video for transcoding and thumbnails
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Video UUID

**Response**:

```json
{
  "success": true,
  "message": "Video reprocessing started",
  "data": {
    "job_id": "uuid",
    "estimated_completion": "2024-01-25T12:30:00Z",
    "processing_steps": [
      "Video analysis",
      "Transcoding to multiple qualities",
      "Thumbnail generation",
      "Chapter detection"
    ]
  }
}
```

#### GET /videos/stats

**Purpose**: Get overall video statistics
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_videos": 234,
      "ready_videos": 198,
      "processing_videos": 12,
      "failed_videos": 3,
      "archived_videos": 21,
      "total_duration": 456789,
      "total_file_size": 52428800000,
      "total_views": 123456,
      "total_watch_time": 98765432
    },
    "subject_distribution": [
      {
        "subject": "Physics",
        "video_count": 89,
        "total_views": 45678,
        "average_rating": 4.6,
        "total_duration": 160200
      },
      {
        "subject": "Chemistry",
        "video_count": 76,
        "total_views": 38901,
        "average_rating": 4.5,
        "total_duration": 136800
      },
      {
        "subject": "Mathematics",
        "video_count": 69,
        "total_views": 38877,
        "average_rating": 4.7,
        "total_duration": 159789
      }
    ],
    "video_type_analysis": [
      {
        "type": "lecture",
        "count": 156,
        "average_duration": 1800,
        "average_views": 1234,
        "completion_rate": 76.8
      },
      {
        "type": "demo",
        "count": 45,
        "average_duration": 900,
        "average_views": 987,
        "completion_rate": 82.3
      },
      {
        "type": "solution",
        "count": 23,
        "average_duration": 600,
        "average_views": 1456,
        "completion_rate": 89.1
      }
    ],
    "quality_usage": [
      {
        "quality": "720p",
        "usage_percentage": 45.2,
        "bandwidth_saved": "2.3 TB"
      },
      {
        "quality": "480p",
        "usage_percentage": 32.1,
        "bandwidth_saved": "4.1 TB"
      },
      {
        "quality": "1080p",
        "usage_percentage": 22.7,
        "bandwidth_saved": "0 TB"
      }
    ],
    "upload_trends": [
      {
        "month": "2024-01",
        "uploads": 23,
        "total_views": 12345,
        "processing_time": 15600
      }
    ],
    "top_performing_videos": [
      {
        "id": "uuid",
        "title": "JEE Physics - Laws of Motion Explained",
        "views": 2345,
        "rating": 4.7,
        "completion_rate": 76.8
      }
    ]
  }
}
```

---

## 🎯 Video Upload Interface API Summary

### **Key Features Implemented:**

1. **🎥 Complete Upload System**: Single and chunked video upload with metadata
2. **⚙️ Advanced Processing**: Multi-quality transcoding and thumbnail generation
3. **📊 Rich Analytics**: Viewing patterns, retention curves, engagement metrics
4. **🎬 Video Management**: CRUD operations with comprehensive metadata
5. **📱 Adaptive Streaming**: Multiple quality variants for different devices
6. **🔍 Content Organization**: Subject, course, module, lesson hierarchy

### **API Endpoints Summary:**

| Module         | Endpoint                            | Method | Purpose                 |
| -------------- | ----------------------------------- | ------ | ----------------------- |
| **Upload**     | `/videos/upload`                    | POST   | Single video upload     |
|                | `/videos/upload-chunked`            | POST   | Chunked upload          |
|                | `/videos/upload-complete`           | POST   | Complete chunked upload |
|                | `/videos/upload-status/{upload_id}` | GET    | Upload progress         |
| **Management** | `/videos`                           | GET    | List videos             |
|                | `/videos/{id}`                      | GET    | Detailed video info     |
|                | `/videos/{id}`                      | PUT    | Update video            |
|                | `/videos/{id}`                      | DELETE | Delete video            |
|                | `/videos/{id}/processing-status`    | GET    | Processing status       |
| **Analytics**  | `/videos/{id}/analytics`            | GET    | Video analytics         |
|                | `/videos/{id}/reprocess`            | POST   | Reprocess video         |
|                | `/videos/stats`                     | GET    | Overall statistics      |

### **Business Logic Covered:**

✅ **Upload Management**: Single and chunked upload with progress tracking
✅ **Video Processing**: Multi-quality transcoding and optimization
✅ **Content Organization**: Hierarchical content structure integration
✅ **Analytics Tracking**: Comprehensive viewing and engagement metrics
✅ **Quality Adaptation**: Adaptive streaming for different devices
✅ **Performance Monitoring**: Processing status and error handling
✅ **Content Discovery**: Search, filtering, and recommendation support

### **Technical Features:**

🔐 **Secure Upload**: File validation, size limits, format checking
📱 **Mobile Optimized**: Adaptive streaming and responsive design
🔍 **Advanced Analytics**: Retention curves, engagement tracking
📊 **Performance Metrics**: Processing time, quality usage analytics
⚡ **Async Processing**: Background transcoding and thumbnail generation
🎯 **CDN Integration**: Fast video delivery and global caching
💾 **Storage Optimization**: Multiple quality variants and compression

**🚀 The Video Upload Interface APIs are now complete and production-ready for backend implementation!**

---

## 6️⃣ TEST MANAGEMENT APIs

### 6.1 Test Creation & Management APIs

#### POST /tests

**Purpose**: Create new test/quiz with questions and configuration
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "title": "JEE Main Physics - Mechanics Mock Test",
  "description": "Comprehensive test covering all mechanics topics with JEE Main pattern questions",
  "test_type": "mock_test",
  "subject_id": "uuid",
  "course_id": "uuid",
  "module_id": "uuid",
  "exam_id": "uuid",
  "difficulty_level": "medium",
  "total_marks": 100,
  "total_questions": 30,
  "duration": 180,
  "negative_marking": true,
  "negative_marks": -1,
  "passing_marks": 40,
  "instructions": [
    "Read all questions carefully before answering",
    "Each question carries equal marks",
    "Negative marking applicable for wrong answers"
  ],
  "schedule": {
    "start_date": "2024-02-01T10:00:00Z",
    "end_date": "2024-02-07T23:59:59Z",
    "time_limit_enabled": true,
    "auto_submit": true,
    "allow_retakes": false,
    "max_attempts": 1
  },
  "access_control": {
    "is_public": false,
    "enrolled_students_only": true,
    "specific_students": [],
    "password_protected": false,
    "password": ""
  },
  "result_settings": {
    "show_results_immediately": false,
    "show_correct_answers": true,
    "show_explanations": true,
    "show_score_breakdown": true,
    "result_available_after": "2024-02-08T00:00:00Z"
  },
  "questions": [
    {
      "question_text": "A body of mass 2 kg is moving with velocity 10 m/s. What is its kinetic energy?",
      "question_type": "mcq",
      "marks": 4,
      "difficulty": "easy",
      "topic_id": "uuid",
      "options": [
        {
          "option_text": "50 J",
          "is_correct": false
        },
        {
          "option_text": "100 J",
          "is_correct": true
        },
        {
          "option_text": "200 J",
          "is_correct": false
        },
        {
          "option_text": "400 J",
          "is_correct": false
        }
      ],
      "explanation": "Kinetic energy = (1/2)mv² = (1/2) × 2 × 10² = 100 J",
      "solution_steps": [
        "Given: mass (m) = 2 kg, velocity (v) = 10 m/s",
        "Formula: KE = (1/2)mv²",
        "Substituting: KE = (1/2) × 2 × (10)²",
        "KE = 1 × 100 = 100 J"
      ]
    }
  ],
  "tags": ["jee_main", "physics", "mechanics", "mock_test"],
  "is_active": true
}
```

**Response**:

```json
{
  "success": true,
  "message": "Test created successfully",
  "data": {
    "test": {
      "id": "uuid",
      "title": "JEE Main Physics - Mechanics Mock Test",
      "test_code": "JEE_PHY_MECH_001",
      "test_type": "mock_test",
      "status": "draft",
      "total_questions": 30,
      "total_marks": 100,
      "duration": 180,
      "created_by": {
        "id": "uuid",
        "name": "Admin User"
      },
      "created_at": "2024-01-25T10:30:00Z",
      "test_url": "https://admin.utkrishta.com/tests/uuid"
    }
  }
}
```

#### GET /tests

**Purpose**: Get list of tests with comprehensive filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, description
- `test_type` (optional): `mock_test`, `quiz`, `assignment`, `practice`
- `subject` (optional): Subject ID filter
- `course` (optional): Course ID filter
- `difficulty` (optional): `easy`, `medium`, `hard`
- `status` (optional): `draft`, `published`, `active`, `completed`, `archived`
- `created_by` (optional): Creator ID filter
- `sort_by` (optional): `created_at`, `title`, `attempts`, `average_score`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "tests": [
      {
        "id": "uuid",
        "title": "JEE Main Physics - Mechanics Mock Test",
        "description": "Comprehensive test covering all mechanics topics",
        "test_code": "JEE_PHY_MECH_001",
        "test_type": "mock_test",
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete"
        },
        "module": {
          "id": "uuid",
          "title": "Mechanics"
        },
        "exam": {
          "id": "uuid",
          "name": "JEE Main"
        },
        "difficulty_level": "medium",
        "total_questions": 30,
        "total_marks": 100,
        "duration": 180,
        "negative_marking": true,
        "passing_marks": 40,
        "schedule": {
          "start_date": "2024-02-01T10:00:00Z",
          "end_date": "2024-02-07T23:59:59Z",
          "is_active": true
        },
        "statistics": {
          "total_attempts": 156,
          "completed_attempts": 142,
          "average_score": 67.8,
          "highest_score": 96,
          "lowest_score": 24,
          "pass_rate": 78.9
        },
        "status": "published",
        "created_by": {
          "id": "uuid",
          "name": "Admin User"
        },
        "created_at": "2024-01-25T10:30:00Z",
        "updated_at": "2024-01-25T11:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 89,
      "total_pages": 4,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_tests": 89,
      "published_tests": 67,
      "draft_tests": 22,
      "total_attempts": 12456,
      "average_completion_rate": 84.5
    }
  }
}
```

#### GET /tests/{id}

**Purpose**: Get detailed test information with questions and analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "test": {
      "id": "uuid",
      "title": "JEE Main Physics - Mechanics Mock Test",
      "description": "Comprehensive test covering all mechanics topics with JEE Main pattern questions",
      "test_code": "JEE_PHY_MECH_001",
      "test_type": "mock_test",
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete",
        "instructor": "Dr. Rajesh Kumar"
      },
      "module": {
        "id": "uuid",
        "title": "Mechanics"
      },
      "exam": {
        "id": "uuid",
        "name": "JEE Main",
        "pattern": "Multiple Choice Questions"
      },
      "difficulty_level": "medium",
      "total_questions": 30,
      "total_marks": 100,
      "duration": 180,
      "negative_marking": true,
      "negative_marks": -1,
      "passing_marks": 40,
      "instructions": [
        "Read all questions carefully before answering",
        "Each question carries equal marks",
        "Negative marking applicable for wrong answers",
        "Use rough sheets for calculations"
      ],
      "schedule": {
        "start_date": "2024-02-01T10:00:00Z",
        "end_date": "2024-02-07T23:59:59Z",
        "time_limit_enabled": true,
        "auto_submit": true,
        "allow_retakes": false,
        "max_attempts": 1,
        "is_active": true
      },
      "access_control": {
        "is_public": false,
        "enrolled_students_only": true,
        "specific_students": [],
        "password_protected": false,
        "allowed_student_count": 567
      },
      "result_settings": {
        "show_results_immediately": false,
        "show_correct_answers": true,
        "show_explanations": true,
        "show_score_breakdown": true,
        "show_rank": true,
        "result_available_after": "2024-02-08T00:00:00Z"
      },
      "questions": [
        {
          "id": "uuid",
          "question_number": 1,
          "question_text": "A body of mass 2 kg is moving with velocity 10 m/s. What is its kinetic energy?",
          "question_type": "mcq",
          "marks": 4,
          "difficulty": "easy",
          "topic": {
            "id": "uuid",
            "name": "Work and Energy"
          },
          "options": [
            {
              "id": "uuid",
              "option_text": "50 J",
              "option_label": "A",
              "is_correct": false
            },
            {
              "id": "uuid",
              "option_text": "100 J",
              "option_label": "B",
              "is_correct": true
            },
            {
              "id": "uuid",
              "option_text": "200 J",
              "option_label": "C",
              "is_correct": false
            },
            {
              "id": "uuid",
              "option_text": "400 J",
              "option_label": "D",
              "is_correct": false
            }
          ],
          "explanation": "Kinetic energy = (1/2)mv² = (1/2) × 2 × 10² = 100 J",
          "solution_steps": [
            "Given: mass (m) = 2 kg, velocity (v) = 10 m/s",
            "Formula: KE = (1/2)mv²",
            "Substituting: KE = (1/2) × 2 × (10)²",
            "KE = 1 × 100 = 100 J"
          ],
          "image_url": null,
          "video_solution_url": null
        }
      ],
      "question_distribution": {
        "by_difficulty": {
          "easy": 10,
          "medium": 15,
          "hard": 5
        },
        "by_topic": [
          {
            "topic": "Laws of Motion",
            "questions": 8,
            "marks": 32
          },
          {
            "topic": "Work and Energy",
            "questions": 7,
            "marks": 28
          },
          {
            "topic": "Rotational Motion",
            "questions": 6,
            "marks": 24
          }
        ]
      },
      "statistics": {
        "total_attempts": 156,
        "completed_attempts": 142,
        "in_progress_attempts": 14,
        "average_score": 67.8,
        "median_score": 72,
        "highest_score": 96,
        "lowest_score": 24,
        "pass_rate": 78.9,
        "average_time_taken": 165,
        "completion_rate": 91.0
      },
      "performance_analytics": {
        "question_wise_accuracy": [
          {
            "question_id": "uuid",
            "question_number": 1,
            "correct_attempts": 134,
            "total_attempts": 142,
            "accuracy": 94.4,
            "average_time": 45
          }
        ],
        "topic_wise_performance": [
          {
            "topic": "Laws of Motion",
            "average_score": 75.2,
            "accuracy": 78.9,
            "difficulty_rating": 6.8
          }
        ]
      },
      "tags": ["jee_main", "physics", "mechanics", "mock_test"],
      "status": "published",
      "created_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin"
      },
      "created_at": "2024-01-25T10:30:00Z",
      "updated_at": "2024-01-25T11:00:00Z",
      "published_at": "2024-01-26T09:00:00Z"
    }
  }
}
```

#### PUT /tests/{id}

**Purpose**: Update test information and configuration
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID
  **Request Body**: Same as POST but with optional fields

#### DELETE /tests/{id}

**Purpose**: Delete test (soft delete)
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "message": "Test deleted successfully",
  "data": {
    "deleted_at": "2024-01-25T12:00:00Z"
  }
}
```

#### POST /tests/{id}/publish

**Purpose**: Publish test to make it available to students
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID

**Response**:

```json
{
  "success": true,
  "message": "Test published successfully",
  "data": {
    "test": {
      "id": "uuid",
      "status": "published",
      "published_at": "2024-01-25T12:00:00Z",
      "test_url": "https://student.utkrishta.com/tests/uuid"
    }
  }
}
```

#### POST /tests/{id}/duplicate

**Purpose**: Create a duplicate copy of existing test
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID
  **Request Body**:

```json
{
  "title": "JEE Main Physics - Mechanics Mock Test 2",
  "copy_questions": true,
  "copy_settings": true,
  "reset_statistics": true
}
```

### 6.2 Test Results & Analytics APIs

#### GET /tests/{id}/results

**Purpose**: Get test results and student performance data
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID
  **Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `status` (optional): `completed`, `in_progress`, `not_started`
- `sort_by` (optional): `score`, `time_taken`, `submitted_at`, `student_name`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "test_info": {
      "id": "uuid",
      "title": "JEE Main Physics - Mechanics Mock Test",
      "total_marks": 100,
      "total_questions": 30,
      "duration": 180
    },
    "results_summary": {
      "total_attempts": 156,
      "completed_attempts": 142,
      "in_progress_attempts": 14,
      "not_started": 411,
      "average_score": 67.8,
      "median_score": 72,
      "highest_score": 96,
      "lowest_score": 24,
      "pass_rate": 78.9,
      "average_time_taken": 165
    },
    "results": [
      {
        "id": "uuid",
        "student": {
          "id": "uuid",
          "name": "Rahul Sharma",
          "email": "<EMAIL>",
          "roll_number": "**********"
        },
        "attempt_number": 1,
        "status": "completed",
        "score": 84,
        "percentage": 84.0,
        "marks_obtained": 84,
        "correct_answers": 21,
        "incorrect_answers": 6,
        "unanswered": 3,
        "time_taken": 162,
        "submitted_at": "2024-02-02T12:42:00Z",
        "rank": 12,
        "grade": "A",
        "is_passed": true,
        "subject_wise_score": [
          {
            "topic": "Laws of Motion",
            "total_questions": 8,
            "correct": 7,
            "score": 28,
            "accuracy": 87.5
          },
          {
            "topic": "Work and Energy",
            "total_questions": 7,
            "correct": 6,
            "score": 24,
            "accuracy": 85.7
          }
        ],
        "difficulty_wise_performance": {
          "easy": {
            "total": 10,
            "correct": 9,
            "accuracy": 90.0
          },
          "medium": {
            "total": 15,
            "correct": 11,
            "accuracy": 73.3
          },
          "hard": {
            "total": 5,
            "correct": 1,
            "accuracy": 20.0
          }
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 142,
      "total_pages": 6,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### GET /tests/{id}/analytics

**Purpose**: Get comprehensive test analytics and insights
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "test_performance": {
      "overall_statistics": {
        "total_attempts": 156,
        "completion_rate": 91.0,
        "average_score": 67.8,
        "standard_deviation": 18.5,
        "pass_rate": 78.9,
        "average_time_taken": 165,
        "time_efficiency": 91.7
      },
      "score_distribution": [
        { "range": "0-20", "count": 3, "percentage": 2.1 },
        { "range": "21-40", "count": 12, "percentage": 8.5 },
        { "range": "41-60", "count": 28, "percentage": 19.7 },
        { "range": "61-80", "count": 67, "percentage": 47.2 },
        { "range": "81-100", "count": 32, "percentage": 22.5 }
      ],
      "grade_distribution": [
        { "grade": "A+", "count": 15, "percentage": 10.6 },
        { "grade": "A", "count": 32, "percentage": 22.5 },
        { "grade": "B+", "count": 45, "percentage": 31.7 },
        { "grade": "B", "count": 28, "percentage": 19.7 },
        { "grade": "C", "count": 15, "percentage": 10.6 },
        { "grade": "F", "count": 7, "percentage": 4.9 }
      ]
    },
    "question_analysis": [
      {
        "question_id": "uuid",
        "question_number": 1,
        "question_text": "A body of mass 2 kg is moving with velocity 10 m/s...",
        "difficulty": "easy",
        "topic": "Work and Energy",
        "total_attempts": 142,
        "correct_attempts": 134,
        "accuracy": 94.4,
        "average_time": 45,
        "discrimination_index": 0.65,
        "difficulty_index": 0.94,
        "option_analysis": [
          { "option": "A", "selected_count": 5, "percentage": 3.5 },
          {
            "option": "B",
            "selected_count": 134,
            "percentage": 94.4,
            "is_correct": true
          },
          { "option": "C", "selected_count": 2, "percentage": 1.4 },
          { "option": "D", "selected_count": 1, "percentage": 0.7 }
        ]
      }
    ],
    "topic_wise_analysis": [
      {
        "topic": "Laws of Motion",
        "total_questions": 8,
        "total_marks": 32,
        "average_score": 24.1,
        "accuracy": 75.2,
        "difficulty_rating": 6.8,
        "time_spent": 360,
        "strongest_areas": ["Newton's First Law"],
        "weakest_areas": ["Applications of Newton's Laws"]
      }
    ],
    "time_analysis": {
      "average_time_per_question": 5.5,
      "fastest_completion": 98,
      "slowest_completion": 180,
      "time_distribution": [
        { "range": "0-120", "count": 23, "percentage": 16.2 },
        { "range": "121-150", "count": 45, "percentage": 31.7 },
        { "range": "151-180", "count": 74, "percentage": 52.1 }
      ],
      "question_time_analysis": [
        {
          "question_number": 1,
          "average_time": 45,
          "median_time": 42,
          "time_efficiency": 85.7
        }
      ]
    },
    "student_insights": {
      "top_performers": [
        {
          "student_id": "uuid",
          "name": "Priya Patel",
          "score": 96,
          "rank": 1,
          "time_taken": 145
        }
      ],
      "improvement_needed": [
        {
          "student_id": "uuid",
          "name": "Amit Kumar",
          "score": 32,
          "weak_topics": ["Rotational Motion", "Work and Energy"]
        }
      ],
      "cheating_detection": {
        "suspicious_patterns": 2,
        "flagged_students": [
          {
            "student_id": "uuid",
            "reason": "Unusually fast completion with high accuracy",
            "confidence": 0.85
          }
        ]
      }
    }
  }
}
```

#### GET /tests/{id}/export

**Purpose**: Export test results in various formats
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Test UUID
  **Query Parameters**:
- `format` (required): `csv`, `excel`, `pdf`
- `include_answers` (optional): `true`, `false` (default: false)
- `include_analytics` (optional): `true`, `false` (default: false)

**Response**: File download with appropriate headers

#### GET /tests/stats

**Purpose**: Get overall test statistics and insights
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_tests": 89,
      "published_tests": 67,
      "draft_tests": 22,
      "total_attempts": 12456,
      "completed_attempts": 11234,
      "average_completion_rate": 84.5,
      "overall_pass_rate": 76.8
    },
    "test_type_distribution": [
      {
        "type": "mock_test",
        "count": 45,
        "attempts": 6789,
        "average_score": 68.5,
        "completion_rate": 87.2
      },
      {
        "type": "quiz",
        "count": 32,
        "attempts": 4567,
        "average_score": 74.2,
        "completion_rate": 92.1
      },
      {
        "type": "assignment",
        "count": 12,
        "attempts": 1100,
        "average_score": 71.8,
        "completion_rate": 78.9
      }
    ],
    "subject_wise_performance": [
      {
        "subject": "Physics",
        "tests": 34,
        "attempts": 4567,
        "average_score": 65.8,
        "pass_rate": 74.2,
        "difficulty_rating": 7.8
      },
      {
        "subject": "Chemistry",
        "tests": 28,
        "attempts": 3890,
        "average_score": 69.4,
        "pass_rate": 78.1,
        "difficulty_rating": 7.2
      },
      {
        "subject": "Mathematics",
        "tests": 27,
        "attempts": 3999,
        "average_score": 71.2,
        "pass_rate": 79.5,
        "difficulty_rating": 7.5
      }
    ],
    "difficulty_analysis": [
      {
        "difficulty": "easy",
        "tests": 23,
        "average_score": 78.9,
        "completion_rate": 91.2,
        "student_satisfaction": 4.6
      },
      {
        "difficulty": "medium",
        "tests": 45,
        "average_score": 68.7,
        "completion_rate": 84.5,
        "student_satisfaction": 4.2
      },
      {
        "difficulty": "hard",
        "tests": 21,
        "average_score": 58.3,
        "completion_rate": 76.8,
        "student_satisfaction": 3.9
      }
    ],
    "monthly_trends": [
      {
        "month": "2024-01",
        "tests_created": 12,
        "total_attempts": 1234,
        "average_score": 67.8,
        "completion_rate": 85.2
      }
    ],
    "top_performing_tests": [
      {
        "id": "uuid",
        "title": "JEE Main Physics - Mechanics Mock Test",
        "attempts": 156,
        "average_score": 67.8,
        "completion_rate": 91.0,
        "student_rating": 4.5
      }
    ],
    "question_bank_stats": {
      "total_questions": 2456,
      "by_difficulty": {
        "easy": 789,
        "medium": 1234,
        "hard": 433
      },
      "by_subject": {
        "Physics": 856,
        "Chemistry": 789,
        "Mathematics": 811
      },
      "most_used_questions": [
        {
          "question_id": "uuid",
          "usage_count": 23,
          "average_accuracy": 78.5,
          "topic": "Laws of Motion"
        }
      ]
    }
  }
}
```

### 6.3 Question Bank Management APIs

#### GET /questions

**Purpose**: Get questions from question bank with filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in question text
- `subject` (optional): Subject ID filter
- `topic` (optional): Topic ID filter
- `difficulty` (optional): `easy`, `medium`, `hard`
- `question_type` (optional): `mcq`, `numerical`, `true_false`
- `sort_by` (optional): `created_at`, `usage_count`, `accuracy`

**Response**:

```json
{
  "success": true,
  "data": {
    "questions": [
      {
        "id": "uuid",
        "question_text": "A body of mass 2 kg is moving with velocity 10 m/s. What is its kinetic energy?",
        "question_type": "mcq",
        "difficulty": "easy",
        "subject": {
          "id": "uuid",
          "name": "Physics"
        },
        "topic": {
          "id": "uuid",
          "name": "Work and Energy"
        },
        "options": [
          { "option_text": "50 J", "is_correct": false },
          { "option_text": "100 J", "is_correct": true },
          { "option_text": "200 J", "is_correct": false },
          { "option_text": "400 J", "is_correct": false }
        ],
        "explanation": "Kinetic energy = (1/2)mv² = (1/2) × 2 × 10² = 100 J",
        "usage_count": 23,
        "average_accuracy": 94.4,
        "created_by": {
          "id": "uuid",
          "name": "Admin User"
        },
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 2456,
      "total_pages": 99
    }
  }
}
```

#### POST /questions

**Purpose**: Add new question to question bank
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "question_text": "What is the SI unit of force?",
  "question_type": "mcq",
  "difficulty": "easy",
  "subject_id": "uuid",
  "topic_id": "uuid",
  "marks": 4,
  "options": [
    { "option_text": "Joule", "is_correct": false },
    { "option_text": "Newton", "is_correct": true },
    { "option_text": "Watt", "is_correct": false },
    { "option_text": "Pascal", "is_correct": false }
  ],
  "explanation": "The SI unit of force is Newton (N), named after Sir Isaac Newton.",
  "solution_steps": [
    "Force is defined as mass × acceleration",
    "SI unit of mass = kg",
    "SI unit of acceleration = m/s²",
    "Therefore, SI unit of force = kg⋅m/s² = Newton (N)"
  ],
  "tags": ["force", "units", "physics_basics"]
}
```

---

## 🎯 Test Management API Summary

### **Key Features Implemented:**

1. **🧪 Complete Test Creation**: Comprehensive test builder with questions and settings
2. **📊 Advanced Analytics**: Detailed performance analysis and insights
3. **📈 Result Management**: Student results tracking and evaluation
4. **🎯 Question Bank**: Centralized question repository with reusability
5. **📱 Export Capabilities**: Multiple format exports for results and analytics
6. **🔍 Cheating Detection**: Suspicious pattern identification and flagging

### **API Endpoints Summary:**

| Module                  | Endpoint                | Method | Purpose                   |
| ----------------------- | ----------------------- | ------ | ------------------------- |
| **Test Management**     | `/tests`                | POST   | Create new test           |
|                         | `/tests`                | GET    | List tests with filtering |
|                         | `/tests/{id}`           | GET    | Detailed test information |
|                         | `/tests/{id}`           | PUT    | Update test               |
|                         | `/tests/{id}`           | DELETE | Delete test               |
|                         | `/tests/{id}/publish`   | POST   | Publish test              |
|                         | `/tests/{id}/duplicate` | POST   | Duplicate test            |
| **Results & Analytics** | `/tests/{id}/results`   | GET    | Test results              |
|                         | `/tests/{id}/analytics` | GET    | Test analytics            |
|                         | `/tests/{id}/export`    | GET    | Export results            |
|                         | `/tests/stats`          | GET    | Overall statistics        |
| **Question Bank**       | `/questions`            | GET    | List questions            |
|                         | `/questions`            | POST   | Add question              |

### **Business Logic Covered:**

✅ **Test Lifecycle**: Draft → Published → Completed workflow
✅ **Question Management**: Centralized question bank with reusability
✅ **Result Processing**: Automatic scoring and ranking system
✅ **Analytics Engine**: Comprehensive performance analysis
✅ **Access Control**: Student-specific and time-based access
✅ **Cheating Detection**: Pattern analysis and suspicious behavior flagging
✅ **Export System**: Multiple format support for data export

### **Technical Features:**

🔐 **Secure Testing**: Time limits, auto-submit, access control
📱 **Mobile Optimized**: Responsive test interface and data structures
🔍 **Advanced Analytics**: Statistical analysis and performance insights
📊 **Rich Reporting**: Detailed analytics and export capabilities
⚡ **Real-time Processing**: Instant result calculation and ranking
🎯 **Question Intelligence**: Difficulty analysis and usage tracking
💾 **Data Export**: CSV, Excel, PDF export capabilities

**🚀 The Test Management APIs are now complete and production-ready for backend implementation!**

---

## 7️⃣ STUDENT MANAGEMENT APIs

### 7.1 Student Profile Management APIs

#### GET /students

**Purpose**: Get list of students with comprehensive filtering and search
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in name, email, phone, roll number
- `status` (optional): `active`, `inactive`, `suspended`, `graduated`
- `course` (optional): Course ID filter
- `exam_target` (optional): Target exam filter (jee_main, neet, etc.)
- `batch` (optional): Batch ID filter
- `city` (optional): City filter
- `state` (optional): State filter
- `registration_date_from` (optional): Registration date from (YYYY-MM-DD)
- `registration_date_to` (optional): Registration date to (YYYY-MM-DD)
- `performance_level` (optional): `excellent`, `good`, `average`, `needs_improvement`
- `sort_by` (optional): `name`, `registration_date`, `last_activity`, `performance_score`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "students": [
      {
        "id": "uuid",
        "student_id": "**********",
        "roll_number": "**********",
        "name": "Rahul Sharma",
        "email": "<EMAIL>",
        "phone": "+91-**********",
        "avatar": "https://cdn.utkrishta.com/avatars/rahul-sharma.jpg",
        "date_of_birth": "2006-05-15",
        "gender": "male",
        "address": {
          "street": "123 Main Street",
          "city": "Mumbai",
          "state": "Maharashtra",
          "pincode": "400001",
          "country": "India"
        },
        "academic_info": {
          "class": "12th",
          "board": "CBSE",
          "school": "Delhi Public School",
          "target_exam": "JEE Main",
          "target_year": 2024,
          "previous_percentage": 92.5
        },
        "parent_info": {
          "father_name": "Suresh Sharma",
          "father_phone": "+91-9876543211",
          "mother_name": "Priya Sharma",
          "mother_phone": "+91-**********",
          "guardian_email": "<EMAIL>"
        },
        "enrollment_info": {
          "registration_date": "2024-01-15T10:30:00Z",
          "enrollment_status": "active",
          "courses_enrolled": 3,
          "total_fee_paid": 45000,
          "pending_fee": 15000,
          "batch": {
            "id": "uuid",
            "name": "JEE Main 2024 - Batch A",
            "start_date": "2024-02-01"
          }
        },
        "performance_summary": {
          "overall_score": 78.5,
          "performance_level": "good",
          "tests_attempted": 23,
          "average_test_score": 76.2,
          "attendance_percentage": 87.5,
          "assignments_completed": 18,
          "rank_in_batch": 12,
          "improvement_trend": "positive"
        },
        "activity_info": {
          "last_login": "2024-01-25T14:30:00Z",
          "total_study_hours": 145,
          "videos_watched": 89,
          "notes_downloaded": 34,
          "forum_posts": 12
        },
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-25T14:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 1247,
      "total_pages": 50,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_students": 1247,
      "active_students": 1089,
      "inactive_students": 158,
      "new_registrations_this_month": 67,
      "average_performance_score": 74.8,
      "total_revenue": 5623000
    }
  }
}
```

#### GET /students/{id}

**Purpose**: Get detailed student information with complete profile and analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "student": {
      "id": "uuid",
      "student_id": "**********",
      "roll_number": "**********",
      "name": "Rahul Sharma",
      "email": "<EMAIL>",
      "phone": "+91-**********",
      "avatar": "https://cdn.utkrishta.com/avatars/rahul-sharma.jpg",
      "date_of_birth": "2006-05-15",
      "age": 17,
      "gender": "male",
      "address": {
        "street": "123 Main Street",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001",
        "country": "India"
      },
      "academic_info": {
        "class": "12th",
        "board": "CBSE",
        "school": "Delhi Public School",
        "target_exam": "JEE Main",
        "target_year": 2024,
        "previous_percentage": 92.5,
        "subjects": ["Physics", "Chemistry", "Mathematics"],
        "stream": "Science"
      },
      "parent_info": {
        "father_name": "Suresh Sharma",
        "father_occupation": "Engineer",
        "father_phone": "+91-9876543211",
        "father_email": "<EMAIL>",
        "mother_name": "Priya Sharma",
        "mother_occupation": "Teacher",
        "mother_phone": "+91-**********",
        "mother_email": "<EMAIL>",
        "guardian_email": "<EMAIL>",
        "emergency_contact": "+91-9876543211"
      },
      "enrollment_info": {
        "registration_date": "2024-01-15T10:30:00Z",
        "enrollment_status": "active",
        "courses_enrolled": [
          {
            "course_id": "uuid",
            "course_title": "JEE Main Physics Complete",
            "enrollment_date": "2024-01-15T10:30:00Z",
            "progress": 67.8,
            "status": "active"
          }
        ],
        "total_fee_paid": 45000,
        "pending_fee": 15000,
        "payment_history": [
          {
            "payment_id": "uuid",
            "amount": 15000,
            "payment_date": "2024-01-15T10:30:00Z",
            "payment_method": "online",
            "status": "completed"
          }
        ],
        "batch": {
          "id": "uuid",
          "name": "JEE Main 2024 - Batch A",
          "start_date": "2024-02-01",
          "instructor": "Dr. Rajesh Kumar"
        }
      },
      "performance_analytics": {
        "overall_score": 78.5,
        "performance_level": "good",
        "rank_in_batch": 12,
        "rank_in_course": 45,
        "improvement_trend": "positive",
        "strengths": ["Physics", "Mathematics"],
        "weaknesses": ["Organic Chemistry"],
        "test_performance": {
          "tests_attempted": 23,
          "tests_completed": 21,
          "average_score": 76.2,
          "highest_score": 94,
          "lowest_score": 58,
          "improvement_rate": 12.5
        },
        "subject_wise_performance": [
          {
            "subject": "Physics",
            "average_score": 82.3,
            "tests_taken": 8,
            "rank": 8,
            "improvement": 15.2
          },
          {
            "subject": "Chemistry",
            "average_score": 71.4,
            "tests_taken": 7,
            "rank": 18,
            "improvement": 8.7
          },
          {
            "subject": "Mathematics",
            "average_score": 79.8,
            "tests_taken": 8,
            "rank": 12,
            "improvement": 13.9
          }
        ],
        "attendance": {
          "overall_percentage": 87.5,
          "classes_attended": 35,
          "total_classes": 40,
          "recent_trend": "improving"
        }
      },
      "activity_analytics": {
        "engagement_score": 8.2,
        "last_login": "2024-01-25T14:30:00Z",
        "total_study_hours": 145,
        "average_daily_hours": 2.3,
        "study_streak": 12,
        "content_consumption": {
          "videos_watched": 89,
          "total_video_time": 6780,
          "notes_downloaded": 34,
          "assignments_completed": 18,
          "forum_posts": 12,
          "questions_asked": 8
        },
        "learning_patterns": {
          "preferred_study_time": "evening",
          "most_active_day": "sunday",
          "average_session_duration": 45,
          "completion_rate": 78.9
        }
      },
      "communication_log": [
        {
          "id": "uuid",
          "type": "email",
          "subject": "Test Performance Update",
          "sent_to": "parent",
          "sent_at": "2024-01-20T10:00:00Z",
          "status": "delivered"
        }
      ],
      "notes": [
        {
          "id": "uuid",
          "note": "Student shows excellent problem-solving skills in Physics",
          "created_by": "Dr. Rajesh Kumar",
          "created_at": "2024-01-20T15:30:00Z",
          "type": "academic"
        }
      ],
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-25T14:30:00Z",
      "last_activity": "2024-01-25T14:30:00Z"
    }
  }
}
```

#### POST /students

**Purpose**: Create new student profile
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "Priya Patel",
  "email": "<EMAIL>",
  "phone": "+91-9876543213",
  "date_of_birth": "2006-08-20",
  "gender": "female",
  "address": {
    "street": "456 Park Avenue",
    "city": "Delhi",
    "state": "Delhi",
    "pincode": "110001",
    "country": "India"
  },
  "academic_info": {
    "class": "12th",
    "board": "CBSE",
    "school": "Modern School",
    "target_exam": "NEET",
    "target_year": 2024,
    "previous_percentage": 89.5,
    "subjects": ["Physics", "Chemistry", "Biology"],
    "stream": "Science"
  },
  "parent_info": {
    "father_name": "Amit Patel",
    "father_occupation": "Doctor",
    "father_phone": "+91-9876543214",
    "father_email": "<EMAIL>",
    "mother_name": "Sunita Patel",
    "mother_occupation": "Homemaker",
    "mother_phone": "+91-9876543215",
    "mother_email": "<EMAIL>",
    "guardian_email": "<EMAIL>",
    "emergency_contact": "+91-9876543214"
  },
  "enrollment_info": {
    "courses": ["uuid1", "uuid2"],
    "batch_id": "uuid",
    "fee_structure": {
      "total_fee": 60000,
      "initial_payment": 20000,
      "installments": 3
    }
  }
}
```

**Response**:

```json
{
  "success": true,
  "message": "Student created successfully",
  "data": {
    "student": {
      "id": "uuid",
      "student_id": "STU2024002",
      "roll_number": "NEET2024001",
      "name": "Priya Patel",
      "email": "<EMAIL>",
      "status": "active",
      "created_at": "2024-01-25T15:00:00Z"
    }
  }
}
```

#### PUT /students/{id}

**Purpose**: Update student profile information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID
  **Request Body**: Same as POST but with optional fields

#### DELETE /students/{id}

**Purpose**: Delete student (soft delete)
**Authentication**: Required (Admin/Super Admin)
**Response**:

```json
{
  "success": true,
  "message": "Student deleted successfully",
  "data": {
    "deleted_at": "2024-01-25T15:30:00Z"
  }
}
```

#### POST /students/{id}/status

**Purpose**: Update student status (activate, suspend, graduate)
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID
  **Request Body**:

```json
{
  "status": "suspended",
  "reason": "Fee payment overdue",
  "effective_date": "2024-01-25T15:30:00Z",
  "notes": "Student suspended due to non-payment of fees for 2 months"
}
```

### 7.2 Student Enrollment Management APIs

#### GET /students/{id}/enrollments

**Purpose**: Get student's course enrollments and progress
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "enrollments": [
      {
        "id": "uuid",
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete",
          "instructor": "Dr. Rajesh Kumar",
          "duration": "6 months",
          "total_modules": 12
        },
        "enrollment_date": "2024-01-15T10:30:00Z",
        "status": "active",
        "progress": {
          "overall_progress": 67.8,
          "modules_completed": 8,
          "total_modules": 12,
          "lessons_completed": 45,
          "total_lessons": 72,
          "assignments_completed": 18,
          "total_assignments": 24,
          "tests_completed": 8,
          "total_tests": 12
        },
        "performance": {
          "average_score": 82.3,
          "highest_score": 94,
          "lowest_score": 68,
          "rank_in_course": 8,
          "improvement_trend": "positive"
        },
        "attendance": {
          "classes_attended": 28,
          "total_classes": 32,
          "attendance_percentage": 87.5
        },
        "payment_info": {
          "total_fee": 15000,
          "amount_paid": 15000,
          "pending_amount": 0,
          "payment_status": "completed"
        },
        "certificate": {
          "eligible": false,
          "completion_required": 80,
          "current_completion": 67.8
        }
      }
    ],
    "summary": {
      "total_enrollments": 3,
      "active_enrollments": 3,
      "completed_enrollments": 0,
      "average_progress": 65.4,
      "total_fee_paid": 45000,
      "pending_fees": 15000
    }
  }
}
```

#### POST /students/{id}/enroll

**Purpose**: Enroll student in a course
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID
  **Request Body**:

```json
{
  "course_id": "uuid",
  "batch_id": "uuid",
  "enrollment_type": "regular",
  "fee_structure": {
    "total_fee": 18000,
    "discount": 2000,
    "final_fee": 16000,
    "payment_plan": "installment_3"
  },
  "start_date": "2024-02-01T00:00:00Z",
  "notes": "Student enrolled with early bird discount"
}
```

#### DELETE /students/{id}/enrollments/{enrollment_id}

**Purpose**: Unenroll student from a course
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID
- `enrollment_id`: Enrollment UUID

### 7.3 Student Performance Analytics APIs

#### GET /students/{id}/performance

**Purpose**: Get detailed student performance analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Student UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "overall_performance": {
      "performance_score": 78.5,
      "performance_level": "good",
      "rank_in_batch": 12,
      "rank_in_institute": 45,
      "improvement_trend": "positive",
      "strengths": ["Physics", "Mathematics"],
      "weaknesses": ["Organic Chemistry"],
      "recommendations": [
        "Focus more on organic chemistry concepts",
        "Practice more numerical problems in physics"
      ]
    },
    "test_analytics": {
      "total_tests": 23,
      "completed_tests": 21,
      "average_score": 76.2,
      "highest_score": 94,
      "lowest_score": 58,
      "improvement_rate": 12.5,
      "score_trend": [
        { "test_date": "2024-01-01", "score": 65 },
        { "test_date": "2024-01-15", "score": 72 },
        { "test_date": "2024-01-25", "score": 78 }
      ],
      "subject_performance": [
        {
          "subject": "Physics",
          "tests_taken": 8,
          "average_score": 82.3,
          "improvement": 15.2,
          "rank": 8,
          "strong_topics": ["Mechanics", "Thermodynamics"],
          "weak_topics": ["Modern Physics"]
        }
      ]
    },
    "attendance_analytics": {
      "overall_percentage": 87.5,
      "trend": "improving",
      "monthly_attendance": [
        { "month": "2024-01", "percentage": 85.2 },
        { "month": "2024-02", "percentage": 89.7 }
      ],
      "subject_wise_attendance": [
        { "subject": "Physics", "percentage": 90.0 },
        { "subject": "Chemistry", "percentage": 85.0 },
        { "subject": "Mathematics", "percentage": 87.5 }
      ]
    },
    "engagement_metrics": {
      "engagement_score": 8.2,
      "study_hours": 145,
      "video_completion_rate": 78.9,
      "assignment_submission_rate": 85.7,
      "forum_participation": 12,
      "doubt_sessions_attended": 8
    },
    "learning_insights": {
      "learning_style": "visual",
      "preferred_study_time": "evening",
      "most_productive_day": "sunday",
      "average_session_duration": 45,
      "retention_rate": 82.3,
      "concept_mastery": [
        { "concept": "Newton's Laws", "mastery": 92 },
        { "concept": "Organic Reactions", "mastery": 65 }
      ]
    }
  }
}
```

#### GET /students/stats

**Purpose**: Get overall student statistics and insights
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_students": 1247,
      "active_students": 1089,
      "inactive_students": 158,
      "new_registrations_this_month": 67,
      "graduation_this_month": 23,
      "average_performance_score": 74.8,
      "total_revenue": 5623000
    },
    "demographic_analysis": {
      "gender_distribution": {
        "male": 687,
        "female": 560
      },
      "age_distribution": [
        { "age_group": "16-17", "count": 456 },
        { "age_group": "17-18", "count": 567 },
        { "age_group": "18-19", "count": 224 }
      ],
      "geographic_distribution": [
        { "state": "Maharashtra", "count": 234, "percentage": 18.8 },
        { "state": "Delhi", "count": 187, "percentage": 15.0 },
        { "state": "Karnataka", "count": 156, "percentage": 12.5 }
      ]
    },
    "academic_analysis": {
      "target_exam_distribution": [
        { "exam": "JEE Main", "count": 567, "average_score": 76.2 },
        { "exam": "NEET", "count": 456, "average_score": 78.9 },
        { "exam": "JEE Advanced", "count": 224, "average_score": 74.5 }
      ],
      "board_distribution": [
        { "board": "CBSE", "count": 789 },
        { "board": "State Board", "count": 345 },
        { "board": "ICSE", "count": 113 }
      ],
      "performance_levels": [
        { "level": "excellent", "count": 187, "percentage": 15.0 },
        { "level": "good", "count": 436, "percentage": 35.0 },
        { "level": "average", "count": 498, "percentage": 39.9 },
        { "level": "needs_improvement", "count": 126, "percentage": 10.1 }
      ]
    },
    "enrollment_trends": [
      {
        "month": "2024-01",
        "new_enrollments": 67,
        "course_completions": 23,
        "revenue": 1005000
      }
    ],
    "top_performers": [
      {
        "student_id": "uuid",
        "name": "Priya Patel",
        "overall_score": 94.5,
        "rank": 1,
        "target_exam": "NEET"
      }
    ],
    "at_risk_students": [
      {
        "student_id": "uuid",
        "name": "Amit Kumar",
        "performance_score": 45.2,
        "attendance": 65.0,
        "risk_factors": ["low_attendance", "poor_test_scores"]
      }
    ]
  }
}
```

#### POST /students/bulk-import

**Purpose**: Import multiple students from CSV/Excel file
**Authentication**: Required (Admin/Super Admin)
**Request**: Multipart form data
**Form Fields**:

- `file`: CSV/Excel file with student data
- `batch_id`: Default batch ID for all students
- `course_ids`: Default course IDs (comma-separated)

**Response**:

```json
{
  "success": true,
  "message": "Bulk import completed",
  "data": {
    "total_records": 150,
    "successful_imports": 142,
    "failed_imports": 8,
    "errors": [
      {
        "row": 15,
        "error": "Invalid email format",
        "data": { "name": "John Doe", "email": "invalid-email" }
      }
    ]
  }
}
```

#### GET /students/export

**Purpose**: Export student data in various formats
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `format` (required): `csv`, `excel`, `pdf`
- `filters`: JSON string with filter criteria
- `fields`: Comma-separated list of fields to include

**Response**: File download with appropriate headers

---

## 🎯 Student Management API Summary

### **Key Features Implemented:**

1. **👥 Complete Student Profiles**: Comprehensive student information management
2. **📊 Performance Analytics**: Detailed performance tracking and insights
3. **📚 Enrollment Management**: Course enrollment and progress tracking
4. **📈 Learning Analytics**: Study patterns and engagement metrics
5. **📱 Bulk Operations**: Import/export capabilities for large datasets
6. **🎯 Predictive Insights**: At-risk student identification and recommendations

### **API Endpoints Summary:**

| Module                    | Endpoint                                     | Method | Purpose                      |
| ------------------------- | -------------------------------------------- | ------ | ---------------------------- |
| **Profile Management**    | `/students`                                  | GET    | List students with filtering |
|                           | `/students/{id}`                             | GET    | Detailed student information |
|                           | `/students`                                  | POST   | Create new student           |
|                           | `/students/{id}`                             | PUT    | Update student               |
|                           | `/students/{id}`                             | DELETE | Delete student               |
|                           | `/students/{id}/status`                      | POST   | Update student status        |
| **Enrollment Management** | `/students/{id}/enrollments`                 | GET    | Student enrollments          |
|                           | `/students/{id}/enroll`                      | POST   | Enroll in course             |
|                           | `/students/{id}/enrollments/{enrollment_id}` | DELETE | Unenroll from course         |
| **Analytics**             | `/students/{id}/performance`                 | GET    | Performance analytics        |
|                           | `/students/stats`                            | GET    | Overall statistics           |
| **Bulk Operations**       | `/students/bulk-import`                      | POST   | Bulk import                  |
|                           | `/students/export`                           | GET    | Export data                  |

### **Business Logic Covered:**

✅ **Student Lifecycle**: Registration → Active → Graduated/Suspended workflow
✅ **Performance Tracking**: Test scores, attendance, engagement analytics
✅ **Enrollment Management**: Course enrollment, progress tracking, certificates
✅ **Parent Communication**: Contact information and communication logs
✅ **Fee Management**: Payment tracking and pending fee alerts
✅ **Risk Assessment**: At-risk student identification and intervention
✅ **Bulk Operations**: Import/export for administrative efficiency

### **Technical Features:**

🔐 **Secure Data Management**: Role-based access and data privacy
📱 **Mobile Optimized**: Responsive data structures and APIs
🔍 **Advanced Search**: Multi-criteria filtering and search capabilities
📊 **Rich Analytics**: Performance insights and predictive analytics
⚡ **Efficient Queries**: Optimized database queries with pagination
🎯 **Personalized Insights**: Individual learning patterns and recommendations
💾 **Data Export**: Multiple format support for reporting

**🚀 The Student Management APIs are now complete and production-ready for backend implementation!**

---

## 8️⃣ PAYMENTS MANAGEMENT APIs

### 8.1 Payment Transaction Management APIs

#### GET /payments

**Purpose**: Get list of payment transactions with comprehensive filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in transaction ID, student name, email
- `status` (optional): `pending`, `completed`, `failed`, `refunded`, `cancelled`
- `payment_method` (optional): `online`, `cash`, `cheque`, `bank_transfer`, `upi`
- `student_id` (optional): Student ID filter
- `course_id` (optional): Course ID filter
- `date_from` (optional): Payment date from (YYYY-MM-DD)
- `date_to` (optional): Payment date to (YYYY-MM-DD)
- `amount_min` (optional): Minimum amount filter
- `amount_max` (optional): Maximum amount filter
- `sort_by` (optional): `created_at`, `amount`, `student_name`, `status`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "id": "uuid",
        "transaction_id": "TXN2024001234",
        "payment_reference": "PAY_00**********",
        "student": {
          "id": "uuid",
          "name": "Rahul Sharma",
          "email": "<EMAIL>",
          "phone": "+91-**********",
          "student_id": "**********"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete",
          "instructor": "Dr. Rajesh Kumar"
        },
        "payment_details": {
          "amount": 15000,
          "currency": "INR",
          "payment_method": "online",
          "payment_gateway": "razorpay",
          "gateway_transaction_id": "pay_123456789",
          "payment_type": "course_fee"
        },
        "fee_breakdown": {
          "base_fee": 18000,
          "discount": 3000,
          "discount_type": "early_bird",
          "tax_amount": 0,
          "final_amount": 15000
        },
        "installment_info": {
          "is_installment": true,
          "installment_number": 1,
          "total_installments": 3,
          "remaining_amount": 30000
        },
        "status": "completed",
        "payment_date": "2024-01-15T10:30:00Z",
        "due_date": "2024-01-10T23:59:59Z",
        "receipt_url": "https://cdn.utkrishta.com/receipts/TXN2024001234.pdf",
        "notes": "Payment for JEE Main Physics course - First installment",
        "processed_by": {
          "id": "uuid",
          "name": "Admin User",
          "role": "admin"
        },
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:35:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 2456,
      "total_pages": 99,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_payments": 2456,
      "completed_payments": 2234,
      "pending_payments": 156,
      "failed_payments": 66,
      "total_amount": 36840000,
      "total_revenue_this_month": 4560000
    }
  }
}
```

#### GET /payments/{id}

**Purpose**: Get detailed payment transaction information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Payment UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "payment": {
      "id": "uuid",
      "transaction_id": "TXN2024001234",
      "payment_reference": "PAY_00**********",
      "student": {
        "id": "uuid",
        "name": "Rahul Sharma",
        "email": "<EMAIL>",
        "phone": "+91-**********",
        "student_id": "**********",
        "parent_email": "<EMAIL>"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete",
        "instructor": "Dr. Rajesh Kumar",
        "duration": "6 months",
        "total_fee": 45000
      },
      "payment_details": {
        "amount": 15000,
        "currency": "INR",
        "payment_method": "online",
        "payment_gateway": "razorpay",
        "gateway_transaction_id": "pay_123456789",
        "gateway_order_id": "order_123456789",
        "payment_type": "course_fee",
        "payment_mode": "debit_card",
        "card_details": {
          "last_four_digits": "1234",
          "card_type": "visa",
          "bank_name": "HDFC Bank"
        }
      },
      "fee_breakdown": {
        "base_fee": 18000,
        "discount": 3000,
        "discount_type": "early_bird",
        "discount_percentage": 16.67,
        "tax_amount": 0,
        "tax_percentage": 0,
        "processing_fee": 0,
        "final_amount": 15000
      },
      "installment_info": {
        "is_installment": true,
        "installment_number": 1,
        "total_installments": 3,
        "installment_amount": 15000,
        "remaining_amount": 30000,
        "next_due_date": "2024-02-15T23:59:59Z",
        "installment_schedule": [
          {
            "installment": 1,
            "amount": 15000,
            "due_date": "2024-01-15T23:59:59Z",
            "status": "paid",
            "paid_date": "2024-01-15T10:30:00Z"
          },
          {
            "installment": 2,
            "amount": 15000,
            "due_date": "2024-02-15T23:59:59Z",
            "status": "pending"
          },
          {
            "installment": 3,
            "amount": 15000,
            "due_date": "2024-03-15T23:59:59Z",
            "status": "pending"
          }
        ]
      },
      "status": "completed",
      "payment_date": "2024-01-15T10:30:00Z",
      "due_date": "2024-01-10T23:59:59Z",
      "receipt_info": {
        "receipt_number": "RCP2024001234",
        "receipt_url": "https://cdn.utkrishta.com/receipts/TXN2024001234.pdf",
        "generated_at": "2024-01-15T10:35:00Z"
      },
      "refund_info": {
        "is_refundable": true,
        "refund_policy": "Full refund within 7 days",
        "refund_amount": 0,
        "refund_status": null
      },
      "notifications": {
        "email_sent": true,
        "sms_sent": true,
        "parent_notified": true,
        "receipt_emailed": true
      },
      "notes": "Payment for JEE Main Physics course - First installment",
      "processed_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin"
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:35:00Z"
    }
  }
}
```

#### POST /payments

**Purpose**: Create new payment transaction (manual payment entry)
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "student_id": "uuid",
  "course_id": "uuid",
  "amount": 15000,
  "payment_method": "cash",
  "payment_type": "course_fee",
  "fee_breakdown": {
    "base_fee": 18000,
    "discount": 3000,
    "discount_type": "scholarship",
    "tax_amount": 0,
    "final_amount": 15000
  },
  "installment_info": {
    "is_installment": true,
    "installment_number": 1,
    "total_installments": 3
  },
  "payment_date": "2024-01-15T10:30:00Z",
  "receipt_number": "RCP2024001234",
  "notes": "Cash payment received at office",
  "send_notifications": true
}
```

**Response**:

```json
{
  "success": true,
  "message": "Payment recorded successfully",
  "data": {
    "payment": {
      "id": "uuid",
      "transaction_id": "TXN2024001235",
      "amount": 15000,
      "status": "completed",
      "receipt_url": "https://cdn.utkrishta.com/receipts/TXN2024001235.pdf",
      "created_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### PUT /payments/{id}

**Purpose**: Update payment transaction details
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Payment UUID
  **Request Body**: Same as POST but with optional fields

#### POST /payments/{id}/refund

**Purpose**: Process payment refund
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Payment UUID
  **Request Body**:

```json
{
  "refund_amount": 15000,
  "refund_reason": "Course cancellation by student",
  "refund_method": "bank_transfer",
  "bank_details": {
    "account_number": "**********",
    "ifsc_code": "HDFC0001234",
    "account_holder": "Rahul Sharma"
  },
  "notes": "Full refund processed as per policy"
}
```

### 8.2 Fee Structure Management APIs

#### GET /fee-structures

**Purpose**: Get fee structures for courses and programs
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `course_id` (optional): Course ID filter
- `exam_type` (optional): Exam type filter
- `active_only` (optional): Show only active fee structures

**Response**:

```json
{
  "success": true,
  "data": {
    "fee_structures": [
      {
        "id": "uuid",
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete",
          "duration": "6 months"
        },
        "fee_details": {
          "base_fee": 45000,
          "registration_fee": 1000,
          "material_fee": 2000,
          "total_fee": 48000,
          "currency": "INR"
        },
        "payment_plans": [
          {
            "id": "uuid",
            "plan_name": "Full Payment",
            "plan_type": "full_payment",
            "amount": 45000,
            "discount": 3000,
            "final_amount": 42000,
            "description": "Pay full amount and get 3000 discount"
          },
          {
            "id": "uuid",
            "plan_name": "3 Installments",
            "plan_type": "installment_3",
            "amount": 45000,
            "installment_amount": 15000,
            "total_installments": 3,
            "processing_fee": 500,
            "final_amount": 45500,
            "schedule": [
              {
                "installment": 1,
                "amount": 15000,
                "due_after_enrollment": 0
              },
              {
                "installment": 2,
                "amount": 15000,
                "due_after_enrollment": 30
              },
              {
                "installment": 3,
                "amount": 15500,
                "due_after_enrollment": 60
              }
            ]
          }
        ],
        "discounts": [
          {
            "id": "uuid",
            "discount_name": "Early Bird",
            "discount_type": "percentage",
            "discount_value": 10,
            "max_discount": 5000,
            "valid_until": "2024-01-31T23:59:59Z",
            "conditions": "Valid for enrollments before January 31st"
          },
          {
            "id": "uuid",
            "discount_name": "Scholarship",
            "discount_type": "fixed",
            "discount_value": 10000,
            "eligibility": "Merit-based scholarship for top performers",
            "approval_required": true
          }
        ],
        "refund_policy": {
          "refund_percentage": [
            {
              "days_from_enrollment": 7,
              "refund_percentage": 100
            },
            {
              "days_from_enrollment": 30,
              "refund_percentage": 50
            },
            {
              "days_from_enrollment": 60,
              "refund_percentage": 0
            }
          ],
          "processing_fee_deduction": 500,
          "terms": "Refund processed within 7-10 working days"
        },
        "is_active": true,
        "effective_from": "2024-01-01T00:00:00Z",
        "effective_until": "2024-12-31T23:59:59Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### POST /fee-structures

**Purpose**: Create new fee structure
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "course_id": "uuid",
  "fee_details": {
    "base_fee": 60000,
    "registration_fee": 1500,
    "material_fee": 2500,
    "total_fee": 64000,
    "currency": "INR"
  },
  "payment_plans": [
    {
      "plan_name": "Full Payment",
      "plan_type": "full_payment",
      "amount": 60000,
      "discount": 5000,
      "final_amount": 55000
    }
  ],
  "discounts": [
    {
      "discount_name": "Early Bird",
      "discount_type": "percentage",
      "discount_value": 15,
      "max_discount": 8000,
      "valid_until": "2024-02-28T23:59:59Z"
    }
  ],
  "effective_from": "2024-02-01T00:00:00Z",
  "effective_until": "2024-12-31T23:59:59Z"
}
```

### 8.3 Payment Analytics & Reports APIs

#### GET /payments/analytics

**Purpose**: Get comprehensive payment analytics and financial insights
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `date_from` (optional): Start date for analytics (YYYY-MM-DD)
- `date_to` (optional): End date for analytics (YYYY-MM-DD)
- `course_id` (optional): Course-specific analytics
- `payment_method` (optional): Payment method filter

**Response**:

```json
{
  "success": true,
  "data": {
    "revenue_analytics": {
      "total_revenue": 36840000,
      "revenue_this_month": 4560000,
      "revenue_last_month": 4230000,
      "growth_percentage": 7.8,
      "average_transaction_value": 15000,
      "total_transactions": 2456
    },
    "payment_method_analysis": [
      {
        "method": "online",
        "transaction_count": 1876,
        "total_amount": ********,
        "percentage": 76.4,
        "success_rate": 94.2
      },
      {
        "method": "cash",
        "transaction_count": 345,
        "total_amount": 5175000,
        "percentage": 14.0,
        "success_rate": 100.0
      },
      {
        "method": "cheque",
        "transaction_count": 156,
        "total_amount": 2340000,
        "percentage": 6.4,
        "success_rate": 89.7
      },
      {
        "method": "bank_transfer",
        "transaction_count": 79,
        "total_amount": 1185000,
        "percentage": 3.2,
        "success_rate": 96.2
      }
    ],
    "course_wise_revenue": [
      {
        "course_id": "uuid",
        "course_title": "JEE Main Physics Complete",
        "total_revenue": 8520000,
        "student_count": 568,
        "average_fee": 15000,
        "completion_rate": 78.9
      },
      {
        "course_id": "uuid",
        "course_title": "NEET Biology Masterclass",
        "total_revenue": 7680000,
        "student_count": 512,
        "average_fee": 15000,
        "completion_rate": 82.3
      }
    ],
    "monthly_trends": [
      {
        "month": "2024-01",
        "revenue": 4560000,
        "transactions": 304,
        "new_enrollments": 234,
        "refunds": 45000
      },
      {
        "month": "2023-12",
        "revenue": 4230000,
        "transactions": 282,
        "new_enrollments": 198,
        "refunds": 67000
      }
    ],
    "payment_status_distribution": {
      "completed": {
        "count": 2234,
        "amount": 33510000,
        "percentage": 91.0
      },
      "pending": {
        "count": 156,
        "amount": 2340000,
        "percentage": 6.3
      },
      "failed": {
        "count": 66,
        "amount": 990000,
        "percentage": 2.7
      }
    },
    "installment_analysis": {
      "total_installment_plans": 1456,
      "completed_installments": 1234,
      "pending_installments": 222,
      "overdue_installments": 89,
      "total_pending_amount": 3330000
    },
    "refund_analytics": {
      "total_refunds": 234,
      "total_refund_amount": 3510000,
      "refund_rate": 9.5,
      "average_refund_amount": 15000,
      "refund_reasons": [
        {
          "reason": "Course cancellation",
          "count": 123,
          "amount": 1845000
        },
        {
          "reason": "Dissatisfaction",
          "count": 67,
          "amount": 1005000
        },
        {
          "reason": "Technical issues",
          "count": 44,
          "amount": 660000
        }
      ]
    }
  }
}
```

#### GET /payments/pending

**Purpose**: Get pending payments and overdue installments
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `overdue_only` (optional): Show only overdue payments
- `days_overdue` (optional): Filter by days overdue

**Response**:

```json
{
  "success": true,
  "data": {
    "pending_summary": {
      "total_pending": 156,
      "total_amount": 2340000,
      "overdue_payments": 89,
      "overdue_amount": 1335000
    },
    "pending_payments": [
      {
        "id": "uuid",
        "student": {
          "id": "uuid",
          "name": "Amit Kumar",
          "email": "<EMAIL>",
          "phone": "+91-9876543214"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Chemistry Complete"
        },
        "amount": 15000,
        "due_date": "2024-01-20T23:59:59Z",
        "days_overdue": 5,
        "installment_number": 2,
        "total_installments": 3,
        "payment_method": "online",
        "reminder_sent": 2,
        "last_reminder": "2024-01-23T10:00:00Z",
        "status": "overdue"
      }
    ]
  }
}
```

#### GET /payments/export

**Purpose**: Export payment data in various formats
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `format` (required): `csv`, `excel`, `pdf`
- `date_from` (optional): Start date filter
- `date_to` (optional): End date filter
- `include_analytics` (optional): Include analytics data

**Response**: File download with appropriate headers

#### POST /payments/send-reminders

**Purpose**: Send payment reminders to students with pending payments
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "filter_criteria": {
    "days_overdue_min": 1,
    "days_overdue_max": 30,
    "amount_min": 1000
  },
  "reminder_type": "email_sms",
  "custom_message": "Your payment is overdue. Please make the payment to continue accessing the course.",
  "include_parents": true
}
```

---

## 🎯 Payments Management API Summary

### **Key Features Implemented:**

1. **💳 Complete Transaction Management**: Comprehensive payment tracking and processing
2. **📊 Advanced Analytics**: Revenue insights and financial reporting
3. **💰 Fee Structure Management**: Flexible pricing and payment plans
4. **📈 Installment System**: Multi-installment payment tracking
5. **🔄 Refund Processing**: Automated refund management and tracking
6. **📱 Payment Reminders**: Automated reminder system for pending payments

### **API Endpoints Summary:**

| Module                     | Endpoint                   | Method | Purpose                      |
| -------------------------- | -------------------------- | ------ | ---------------------------- |
| **Transaction Management** | `/payments`                | GET    | List payments with filtering |
|                            | `/payments/{id}`           | GET    | Detailed payment information |
|                            | `/payments`                | POST   | Create payment transaction   |
|                            | `/payments/{id}`           | PUT    | Update payment               |
|                            | `/payments/{id}/refund`    | POST   | Process refund               |
| **Fee Structure**          | `/fee-structures`          | GET    | List fee structures          |
|                            | `/fee-structures`          | POST   | Create fee structure         |
| **Analytics & Reports**    | `/payments/analytics`      | GET    | Payment analytics            |
|                            | `/payments/pending`        | GET    | Pending payments             |
|                            | `/payments/export`         | GET    | Export payment data          |
|                            | `/payments/send-reminders` | POST   | Send payment reminders       |

### **Business Logic Covered:**

✅ **Payment Lifecycle**: Pending → Completed → Refunded workflow
✅ **Installment Management**: Multi-installment tracking and reminders
✅ **Fee Structures**: Flexible pricing with discounts and payment plans
✅ **Refund Processing**: Automated refund calculations and processing
✅ **Revenue Analytics**: Comprehensive financial insights and reporting
✅ **Payment Reminders**: Automated notification system for overdue payments
✅ **Gateway Integration**: Support for multiple payment gateways

### **Technical Features:**

🔐 **Secure Transactions**: Payment gateway integration with security measures
📱 **Mobile Optimized**: Responsive payment interfaces and data structures
🔍 **Advanced Analytics**: Revenue tracking and financial insights
📊 **Rich Reporting**: Detailed financial reports and export capabilities
⚡ **Real-time Processing**: Instant payment status updates and notifications
🎯 **Automated Reminders**: Smart reminder system for pending payments
💾 **Data Export**: Multiple format support for financial reporting

**🚀 The Payments Management APIs are now complete and production-ready for backend implementation!**

---

## 9️⃣ LIVE CLASS SCHEDULER APIs

### 9.1 Class Scheduling Management APIs

#### POST /live-classes

**Purpose**: Schedule new live class with comprehensive configuration
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "title": "JEE Main Physics - Laws of Motion Live Session",
  "description": "Interactive live session covering Newton's laws with problem-solving",
  "course_id": "uuid",
  "subject_id": "uuid",
  "module_id": "uuid",
  "instructor_id": "uuid",
  "class_type": "live_lecture",
  "schedule": {
    "start_time": "2024-02-01T16:00:00Z",
    "duration": 90,
    "timezone": "Asia/Kolkata",
    "is_recurring": true,
    "recurrence_pattern": {
      "type": "weekly",
      "days": ["monday", "wednesday", "friday"],
      "end_date": "2024-06-30T23:59:59Z",
      "total_sessions": 36
    }
  },
  "meeting_config": {
    "platform": "zoom",
    "auto_generate_meeting": true,
    "waiting_room_enabled": true,
    "recording_enabled": true,
    "auto_recording": true,
    "allow_screen_sharing": false,
    "mute_participants_on_join": true,
    "max_participants": 100
  },
  "enrollment_settings": {
    "enrollment_required": true,
    "max_capacity": 100,
    "allow_waitlist": true,
    "registration_deadline_hours": 2,
    "send_reminders": true,
    "reminder_schedule": [1440, 60, 15]
  },
  "content_outline": {
    "topics": ["Newton's First Law", "Newton's Second Law", "Applications"],
    "learning_objectives": [
      "Understand concept of inertia",
      "Apply F=ma in problem solving",
      "Solve complex motion problems"
    ],
    "prerequisites": ["Basic kinematics", "Vector concepts"],
    "materials": [
      {
        "title": "Laws of Motion Notes",
        "type": "pdf",
        "url": "https://cdn.utkrishta.com/materials/laws-of-motion.pdf"
      }
    ]
  },
  "assessment": {
    "has_quiz": true,
    "quiz_duration": 15,
    "quiz_questions": 5,
    "has_assignment": true,
    "assignment_due_hours": 48
  },
  "notifications": {
    "notify_students": true,
    "notify_parents": false,
    "email_template": "live_class_scheduled",
    "sms_enabled": true,
    "custom_message": "Join us for an interactive physics session!"
  },
  "tags": ["jee_main", "physics", "mechanics", "live_session"],
  "is_public": false,
  "status": "scheduled"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Live class scheduled successfully",
  "data": {
    "live_class": {
      "id": "uuid",
      "class_code": "LC2024001",
      "title": "JEE Main Physics - Laws of Motion Live Session",
      "schedule": {
        "start_time": "2024-02-01T16:00:00Z",
        "duration": 90,
        "timezone": "Asia/Kolkata"
      },
      "meeting_details": {
        "platform": "zoom",
        "meeting_id": "123-456-789",
        "meeting_url": "https://zoom.us/j/123456789",
        "instructor_url": "https://zoom.us/j/123456789?role=1",
        "passcode": "physics123"
      },
      "enrollment_info": {
        "enrolled_students": 0,
        "max_capacity": 100,
        "waitlist_count": 0
      },
      "status": "scheduled",
      "created_at": "2024-01-25T10:30:00Z",
      "recurring_sessions": 36
    }
  }
}
```

#### GET /live-classes

**Purpose**: Get list of live classes with comprehensive filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, description, instructor name
- `status` (optional): `scheduled`, `live`, `completed`, `cancelled`
- `course_id` (optional): Course ID filter
- `subject_id` (optional): Subject ID filter
- `instructor_id` (optional): Instructor ID filter
- `date_from` (optional): Start date filter (YYYY-MM-DD)
- `date_to` (optional): End date filter (YYYY-MM-DD)
- `class_type` (optional): `live_lecture`, `doubt_session`, `test_discussion`
- `sort_by` (optional): `start_time`, `created_at`, `title`, `enrollment_count`
- `sort_order` (optional): `asc`, `desc` (default: `asc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "live_classes": [
      {
        "id": "uuid",
        "class_code": "LC2024001",
        "title": "JEE Main Physics - Laws of Motion Live Session",
        "description": "Interactive live session covering Newton's laws",
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete"
        },
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "instructor": {
          "id": "uuid",
          "name": "Dr. Rajesh Kumar",
          "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
          "rating": 4.8
        },
        "schedule": {
          "start_time": "2024-02-01T16:00:00Z",
          "end_time": "2024-02-01T17:30:00Z",
          "duration": 90,
          "timezone": "Asia/Kolkata",
          "is_recurring": true,
          "next_session": "2024-02-03T16:00:00Z"
        },
        "meeting_info": {
          "platform": "zoom",
          "meeting_id": "123-456-789",
          "meeting_url": "https://zoom.us/j/123456789",
          "recording_enabled": true
        },
        "enrollment": {
          "enrolled_students": 67,
          "max_capacity": 100,
          "waitlist_count": 5,
          "enrollment_rate": 67.0
        },
        "class_type": "live_lecture",
        "status": "scheduled",
        "tags": ["jee_main", "physics", "mechanics"],
        "created_at": "2024-01-25T10:30:00Z",
        "updated_at": "2024-01-25T11:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 156,
      "total_pages": 7,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_classes": 156,
      "scheduled_classes": 89,
      "live_classes": 3,
      "completed_classes": 64,
      "total_enrolled_students": 4567,
      "average_attendance": 84.5
    }
  }
}
```

#### GET /live-classes/{id}

**Purpose**: Get detailed live class information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "live_class": {
      "id": "uuid",
      "class_code": "LC2024001",
      "title": "JEE Main Physics - Laws of Motion Live Session",
      "description": "Interactive live session covering Newton's laws with problem-solving techniques",
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete",
        "total_students": 567
      },
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY"
      },
      "instructor": {
        "id": "uuid",
        "name": "Dr. Rajesh Kumar",
        "email": "<EMAIL>",
        "phone": "+91-**********",
        "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
        "rating": 4.8,
        "experience": 15
      },
      "schedule": {
        "start_time": "2024-02-01T16:00:00Z",
        "end_time": "2024-02-01T17:30:00Z",
        "duration": 90,
        "timezone": "Asia/Kolkata",
        "is_recurring": true,
        "recurrence_pattern": {
          "type": "weekly",
          "days": ["monday", "wednesday", "friday"],
          "end_date": "2024-06-30T23:59:59Z",
          "total_sessions": 36,
          "completed_sessions": 8,
          "remaining_sessions": 28
        },
        "next_session": "2024-02-03T16:00:00Z"
      },
      "meeting_details": {
        "platform": "zoom",
        "meeting_id": "123-456-789",
        "meeting_url": "https://zoom.us/j/123456789",
        "instructor_url": "https://zoom.us/j/123456789?role=1",
        "passcode": "physics123",
        "waiting_room_enabled": true,
        "recording_enabled": true,
        "auto_recording": true
      },
      "enrollment_info": {
        "enrolled_students": 67,
        "max_capacity": 100,
        "waitlist_count": 5,
        "enrollment_rate": 67.0,
        "registration_deadline": "2024-02-01T14:00:00Z",
        "allow_late_registration": false
      },
      "content_outline": {
        "topics": ["Newton's First Law", "Newton's Second Law", "Applications"],
        "learning_objectives": [
          "Understand concept of inertia",
          "Apply F=ma in problem solving",
          "Solve complex motion problems"
        ],
        "prerequisites": ["Basic kinematics", "Vector concepts"],
        "materials": [
          {
            "id": "uuid",
            "title": "Laws of Motion Notes",
            "type": "pdf",
            "url": "https://cdn.utkrishta.com/materials/laws-of-motion.pdf",
            "size": 2048000
          }
        ]
      },
      "assessment": {
        "has_quiz": true,
        "quiz_id": "uuid",
        "quiz_duration": 15,
        "quiz_questions": 5,
        "has_assignment": true,
        "assignment_id": "uuid",
        "assignment_due_hours": 48
      },
      "attendance_summary": {
        "total_sessions_completed": 8,
        "average_attendance": 87.5,
        "highest_attendance": 95.2,
        "lowest_attendance": 78.9,
        "attendance_trend": "stable"
      },
      "class_type": "live_lecture",
      "status": "scheduled",
      "tags": ["jee_main", "physics", "mechanics", "live_session"],
      "is_public": false,
      "created_at": "2024-01-25T10:30:00Z",
      "updated_at": "2024-01-25T11:00:00Z"
    }
  }
}
```

#### PUT /live-classes/{id}

**Purpose**: Update live class details
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID
  **Request Body**: Same as POST but with optional fields

#### DELETE /live-classes/{id}

**Purpose**: Cancel/delete live class
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID

### 9.2 Live Class Control APIs

#### POST /live-classes/{id}/start

**Purpose**: Start live class session
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID

**Response**:

```json
{
  "success": true,
  "message": "Live class started successfully",
  "data": {
    "session": {
      "id": "uuid",
      "live_class_id": "uuid",
      "meeting_url": "https://zoom.us/j/123456789",
      "instructor_url": "https://zoom.us/j/123456789?role=1",
      "started_at": "2024-02-01T16:00:00Z",
      "status": "live",
      "participants_joined": 0,
      "recording_started": true
    }
  }
}
```

#### POST /live-classes/{id}/end

**Purpose**: End live class session
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID

**Response**:

```json
{
  "success": true,
  "message": "Live class ended successfully",
  "data": {
    "session": {
      "id": "uuid",
      "ended_at": "2024-02-01T17:30:00Z",
      "actual_duration": 88,
      "participants_count": 58,
      "recording_url": "https://cdn.utkrishta.com/recordings/LC2024001-session1.mp4",
      "recording_duration": 88,
      "status": "completed"
    }
  }
}
```

#### GET /live-classes/{id}/status

**Purpose**: Get real-time live class status
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "status": "live",
    "session_info": {
      "started_at": "2024-02-01T16:02:00Z",
      "current_duration": 45,
      "participants_count": 58,
      "max_participants_reached": 62,
      "recording_status": "active",
      "instructor_present": true
    },
    "real_time_metrics": {
      "active_participants": 58,
      "participants_with_video": 12,
      "participants_with_audio": 45,
      "chat_messages": 23,
      "questions_asked": 8,
      "polls_conducted": 2
    },
    "technical_status": {
      "connection_quality": "good",
      "audio_quality": "excellent",
      "video_quality": "good",
      "server_load": "normal"
    }
  }
}
```

### 9.3 Attendance & Analytics APIs

#### GET /live-classes/{id}/attendance

**Purpose**: Get attendance details for live class session
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID
  **Query Parameters**:
- `session_date` (optional): Specific session date (YYYY-MM-DD)

**Response**:

```json
{
  "success": true,
  "data": {
    "attendance_summary": {
      "session_date": "2024-02-01",
      "total_enrolled": 67,
      "students_attended": 58,
      "attendance_rate": 86.6,
      "on_time_attendance": 52,
      "late_joiners": 6,
      "early_leavers": 8,
      "average_session_duration": 82
    },
    "attendance_list": [
      {
        "student_id": "uuid",
        "student_name": "Rahul Sharma",
        "student_email": "<EMAIL>",
        "join_time": "2024-02-01T16:02:00Z",
        "leave_time": "2024-02-01T17:28:00Z",
        "duration_attended": 86,
        "status": "present",
        "participation_score": 8.5,
        "questions_asked": 3,
        "chat_messages": 8,
        "polls_participated": 2,
        "device_type": "desktop",
        "connection_quality": "good"
      }
    ],
    "session_analytics": {
      "peak_attendance": 62,
      "peak_time": "2024-02-01T16:15:00Z",
      "average_engagement": 8.2,
      "total_questions": 23,
      "total_chat_messages": 156,
      "polls_conducted": 3,
      "breakout_rooms_used": 0
    }
  }
}
```

#### POST /live-classes/{id}/attendance

**Purpose**: Mark attendance manually or update attendance records
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Live class UUID
  **Request Body**:

```json
{
  "session_date": "2024-02-01",
  "attendance_records": [
    {
      "student_id": "uuid",
      "status": "present",
      "join_time": "2024-02-01T16:02:00Z",
      "leave_time": "2024-02-01T17:28:00Z",
      "participation_score": 8.5,
      "notes": "Active participation in discussions"
    },
    {
      "student_id": "uuid",
      "status": "absent",
      "reason": "sick",
      "excuse_provided": true
    }
  ],
  "auto_calculate_duration": true,
  "send_notifications": true
}
```

#### GET /live-classes/analytics

**Purpose**: Get comprehensive live class analytics
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `date_from` (optional): Start date for analytics
- `date_to` (optional): End date for analytics
- `instructor_id` (optional): Instructor-specific analytics
- `course_id` (optional): Course-specific analytics

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_classes": 156,
      "scheduled_classes": 89,
      "completed_classes": 64,
      "cancelled_classes": 3,
      "live_classes": 2,
      "total_hours_conducted": 9600,
      "average_attendance": 84.5
    },
    "attendance_analytics": {
      "overall_attendance_rate": 84.5,
      "punctuality_rate": 78.9,
      "completion_rate": 86.7,
      "engagement_rate": 82.3,
      "monthly_trends": [
        {
          "month": "2024-01",
          "classes": 23,
          "attendance_rate": 85.2,
          "engagement_score": 8.1
        }
      ]
    },
    "instructor_performance": [
      {
        "instructor_id": "uuid",
        "name": "Dr. Rajesh Kumar",
        "classes_conducted": 45,
        "average_attendance": 87.3,
        "student_rating": 4.8,
        "engagement_score": 8.9,
        "technical_issues": 2
      }
    ],
    "subject_wise_analytics": [
      {
        "subject": "Physics",
        "classes_count": 45,
        "attendance_rate": 87.3,
        "student_satisfaction": 4.6,
        "average_duration": 88,
        "popular_topics": ["Mechanics", "Thermodynamics"]
      }
    ],
    "technical_metrics": {
      "platform_uptime": 99.8,
      "average_connection_quality": "good",
      "recording_success_rate": 98.5,
      "audio_quality_score": 9.2,
      "video_quality_score": 8.7
    },
    "engagement_metrics": {
      "average_questions_per_session": 18,
      "average_chat_messages": 145,
      "poll_participation_rate": 76.8,
      "breakout_room_usage": 23.4,
      "screen_sharing_frequency": 12.3
    }
  }
}
```

#### GET /live-classes/schedule

**Purpose**: Get upcoming live class schedule
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `date_from` (optional): Start date (default: today)
- `date_to` (optional): End date (default: +7 days)
- `instructor_id` (optional): Instructor filter

**Response**:

```json
{
  "success": true,
  "data": {
    "schedule": [
      {
        "date": "2024-02-01",
        "classes": [
          {
            "id": "uuid",
            "title": "JEE Main Physics - Laws of Motion",
            "time": "16:00-17:30",
            "instructor": "Dr. Rajesh Kumar",
            "enrolled_students": 67,
            "status": "scheduled",
            "meeting_url": "https://zoom.us/j/123456789"
          }
        ]
      }
    ],
    "summary": {
      "total_upcoming_classes": 23,
      "classes_today": 3,
      "classes_this_week": 18,
      "total_enrolled_students": 1456
    }
  }
}
```

---

## 🎯 Live Class Scheduler API Summary

### **Key Features Implemented:**

1. **🔴 Complete Class Scheduling**: Comprehensive live class creation and management
2. **📊 Real-time Control**: Live class start/stop and status monitoring
3. **👥 Attendance Management**: Detailed attendance tracking and analytics
4. **📈 Advanced Analytics**: Performance insights and engagement metrics
5. **🔄 Recurring Classes**: Flexible recurring class patterns and management
6. **📱 Multi-platform Support**: Zoom, Teams, and other platform integration

### **API Endpoints Summary:**

| Module                     | Endpoint                        | Method | Purpose                    |
| -------------------------- | ------------------------------- | ------ | -------------------------- |
| **Class Scheduling**       | `/live-classes`                 | POST   | Schedule new live class    |
|                            | `/live-classes`                 | GET    | List live classes          |
|                            | `/live-classes/{id}`            | GET    | Detailed class information |
|                            | `/live-classes/{id}`            | PUT    | Update class details       |
|                            | `/live-classes/{id}`            | DELETE | Cancel class               |
| **Class Control**          | `/live-classes/{id}/start`      | POST   | Start live session         |
|                            | `/live-classes/{id}/end`        | POST   | End live session           |
|                            | `/live-classes/{id}/status`     | GET    | Real-time status           |
| **Attendance & Analytics** | `/live-classes/{id}/attendance` | GET    | Class attendance           |
|                            | `/live-classes/{id}/attendance` | POST   | Mark attendance            |
|                            | `/live-classes/analytics`       | GET    | Comprehensive analytics    |
|                            | `/live-classes/schedule`        | GET    | Upcoming schedule          |

### **Business Logic Covered:**

✅ **Class Lifecycle**: Scheduled → Live → Completed workflow
✅ **Recurring Classes**: Flexible patterns with session tracking
✅ **Attendance Management**: Real-time attendance with participation scoring
✅ **Meeting Integration**: Zoom/Teams platform integration
✅ **Recording Management**: Automatic recording and processing
✅ **Notification System**: Automated reminders and notifications
✅ **Analytics Engine**: Performance tracking and insights

### **Technical Features:**

🔐 **Secure Sessions**: Meeting security with waiting rooms and passcodes
📱 **Mobile Optimized**: Responsive interfaces and real-time updates
🔍 **Real-time Monitoring**: Live session status and participant tracking
📊 **Rich Analytics**: Attendance patterns and engagement metrics
⚡ **Platform Integration**: Seamless video conferencing platform support
🎯 **Automated Workflows**: Smart scheduling and reminder systems
💾 **Recording Management**: Automatic recording and storage

**🚀 The Live Class Scheduler APIs are now complete and production-ready for backend implementation!**

---

## 🔟 NOTIFICATION CENTER APIs

### 10.1 Notification Management APIs

#### GET /notifications

**Purpose**: Get list of notifications with comprehensive filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, message content
- `type` (optional): `system`, `course`, `payment`, `test`, `live_class`, `announcement`
- `priority` (optional): `low`, `medium`, `high`, `urgent`
- `status` (optional): `draft`, `scheduled`, `sent`, `failed`, `cancelled`
- `recipient_type` (optional): `all_students`, `course_students`, `specific_students`, `instructors`, `parents`
- `channel` (optional): `email`, `sms`, `push`, `in_app`, `whatsapp`
- `date_from` (optional): Creation date from (YYYY-MM-DD)
- `date_to` (optional): Creation date to (YYYY-MM-DD)
- `created_by` (optional): Creator ID filter
- `sort_by` (optional): `created_at`, `scheduled_at`, `title`, `priority`, `delivery_rate`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "title": "JEE Main Mock Test Reminder",
        "message": "Your JEE Main Physics mock test is scheduled for tomorrow at 10:00 AM. Please be prepared with your study materials.",
        "type": "test",
        "priority": "high",
        "status": "sent",
        "channels": ["email", "sms", "push"],
        "recipient_info": {
          "recipient_type": "course_students",
          "total_recipients": 567,
          "course_id": "uuid",
          "course_title": "JEE Main Physics Complete"
        },
        "delivery_stats": {
          "total_sent": 567,
          "delivered": 542,
          "failed": 25,
          "opened": 387,
          "clicked": 234,
          "delivery_rate": 95.6,
          "open_rate": 71.4,
          "click_rate": 43.2
        },
        "schedule_info": {
          "is_scheduled": true,
          "scheduled_at": "2024-02-01T09:00:00Z",
          "sent_at": "2024-02-01T09:00:15Z",
          "timezone": "Asia/Kolkata"
        },
        "template_info": {
          "template_id": "uuid",
          "template_name": "Test Reminder",
          "is_custom": false
        },
        "created_by": {
          "id": "uuid",
          "name": "Admin User",
          "role": "admin"
        },
        "created_at": "2024-01-31T15:30:00Z",
        "updated_at": "2024-02-01T09:00:15Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 456,
      "total_pages": 19,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_notifications": 456,
      "sent_notifications": 389,
      "scheduled_notifications": 45,
      "draft_notifications": 22,
      "average_delivery_rate": 94.2,
      "average_open_rate": 68.7
    }
  }
}
```

#### GET /notifications/{id}

**Purpose**: Get detailed notification information with delivery analytics
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Notification UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "notification": {
      "id": "uuid",
      "title": "JEE Main Mock Test Reminder",
      "message": "Your JEE Main Physics mock test is scheduled for tomorrow at 10:00 AM. Please be prepared with your study materials and ensure stable internet connection.",
      "type": "test",
      "priority": "high",
      "status": "sent",
      "channels": ["email", "sms", "push"],
      "content": {
        "email_subject": "Important: JEE Main Mock Test Tomorrow",
        "email_body": "Dear Student,\n\nThis is a reminder that your JEE Main Physics mock test is scheduled for tomorrow...",
        "sms_message": "JEE Main Physics mock test tomorrow 10 AM. Be ready with materials. Good luck!",
        "push_title": "Mock Test Reminder",
        "push_body": "JEE Main Physics test tomorrow at 10 AM",
        "custom_variables": {
          "student_name": "{{student_name}}",
          "test_name": "{{test_name}}",
          "test_time": "{{test_time}}"
        }
      },
      "recipient_info": {
        "recipient_type": "course_students",
        "total_recipients": 567,
        "course_id": "uuid",
        "course_title": "JEE Main Physics Complete",
        "filters": {
          "enrollment_status": "active",
          "attendance_min": 75,
          "exclude_completed": true
        }
      },
      "delivery_analytics": {
        "overall_stats": {
          "total_sent": 567,
          "delivered": 542,
          "failed": 25,
          "bounced": 8,
          "opened": 387,
          "clicked": 234,
          "unsubscribed": 3
        },
        "channel_wise_stats": [
          {
            "channel": "email",
            "sent": 567,
            "delivered": 542,
            "opened": 387,
            "clicked": 234,
            "delivery_rate": 95.6,
            "open_rate": 71.4,
            "click_rate": 43.2
          },
          {
            "channel": "sms",
            "sent": 567,
            "delivered": 534,
            "delivery_rate": 94.2,
            "click_rate": 12.8
          },
          {
            "channel": "push",
            "sent": 567,
            "delivered": 489,
            "opened": 234,
            "delivery_rate": 86.2,
            "open_rate": 47.9
          }
        ],
        "delivery_timeline": [
          {
            "timestamp": "2024-02-01T09:00:00Z",
            "event": "sending_started",
            "count": 567
          },
          {
            "timestamp": "2024-02-01T09:05:00Z",
            "event": "delivery_completed",
            "count": 542
          }
        ],
        "failure_analysis": [
          {
            "reason": "invalid_email",
            "count": 15,
            "percentage": 2.6
          },
          {
            "reason": "blocked_number",
            "count": 10,
            "percentage": 1.8
          }
        ]
      },
      "schedule_info": {
        "is_scheduled": true,
        "scheduled_at": "2024-02-01T09:00:00Z",
        "sent_at": "2024-02-01T09:00:15Z",
        "timezone": "Asia/Kolkata",
        "delay_reason": null
      },
      "template_info": {
        "template_id": "uuid",
        "template_name": "Test Reminder",
        "template_category": "academic",
        "is_custom": false,
        "variables_used": ["student_name", "test_name", "test_time"]
      },
      "automation_info": {
        "is_automated": true,
        "trigger_event": "test_scheduled",
        "trigger_time": "24_hours_before",
        "automation_rule_id": "uuid"
      },
      "created_by": {
        "id": "uuid",
        "name": "Admin User",
        "role": "admin",
        "email": "<EMAIL>"
      },
      "created_at": "2024-01-31T15:30:00Z",
      "updated_at": "2024-02-01T09:00:15Z"
    }
  }
}
```

#### POST /notifications

**Purpose**: Create and send new notification
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "title": "Important: Course Schedule Update",
  "message": "There has been a change in your course schedule. Please check the updated timetable.",
  "type": "course",
  "priority": "high",
  "channels": ["email", "sms", "push"],
  "content": {
    "email_subject": "Course Schedule Update - Action Required",
    "email_body": "Dear {{student_name}},\n\nWe hope this message finds you well. We are writing to inform you about an important update to your course schedule...",
    "sms_message": "Course schedule updated for {{course_name}}. Check app for details. -Utkrishta Team",
    "push_title": "Schedule Update",
    "push_body": "Your {{course_name}} schedule has been updated",
    "custom_variables": {
      "student_name": "{{student_name}}",
      "course_name": "{{course_name}}",
      "update_date": "{{update_date}}"
    }
  },
  "recipients": {
    "recipient_type": "course_students",
    "course_id": "uuid",
    "filters": {
      "enrollment_status": "active",
      "attendance_min": 50,
      "exclude_completed": false
    },
    "include_parents": true,
    "exclude_unsubscribed": true
  },
  "schedule": {
    "send_immediately": false,
    "scheduled_at": "2024-02-02T10:00:00Z",
    "timezone": "Asia/Kolkata",
    "repeat_settings": {
      "is_recurring": false,
      "frequency": null,
      "end_date": null
    }
  },
  "template_id": "uuid",
  "automation_settings": {
    "is_automated": false,
    "trigger_event": null,
    "trigger_conditions": null
  },
  "tracking_settings": {
    "track_opens": true,
    "track_clicks": true,
    "track_unsubscribes": true,
    "custom_tracking_params": {
      "campaign": "schedule_update",
      "source": "admin_panel"
    }
  },
  "approval_required": false,
  "save_as_draft": false
}
```

**Response**:

```json
{
  "success": true,
  "message": "Notification created and scheduled successfully",
  "data": {
    "notification": {
      "id": "uuid",
      "title": "Important: Course Schedule Update",
      "status": "scheduled",
      "total_recipients": 456,
      "scheduled_at": "2024-02-02T10:00:00Z",
      "estimated_delivery_time": "2024-02-02T10:05:00Z",
      "channels": ["email", "sms", "push"],
      "created_at": "2024-02-01T15:30:00Z"
    }
  }
}
```

#### PUT /notifications/{id}

**Purpose**: Update notification (only if not sent)
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Notification UUID
  **Request Body**: Same as POST but with optional fields

#### DELETE /notifications/{id}

**Purpose**: Cancel/delete notification
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Notification UUID

#### POST /notifications/{id}/send

**Purpose**: Send scheduled notification immediately
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Notification UUID

**Response**:

```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "notification": {
      "id": "uuid",
      "status": "sending",
      "sent_at": "2024-02-01T15:45:00Z",
      "total_recipients": 456,
      "estimated_completion": "2024-02-01T15:50:00Z"
    }
  }
}
```

### 10.2 Template Management APIs

#### GET /notification-templates

**Purpose**: Get notification templates
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `category` (optional): `academic`, `payment`, `system`, `marketing`
- `type` (optional): Template type filter
- `is_active` (optional): Active templates only

**Response**:

```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "uuid",
        "name": "Test Reminder",
        "description": "Reminder template for upcoming tests and exams",
        "category": "academic",
        "type": "test",
        "is_active": true,
        "channels_supported": ["email", "sms", "push"],
        "variables": [
          {
            "name": "student_name",
            "description": "Student's full name",
            "required": true,
            "default_value": null
          },
          {
            "name": "test_name",
            "description": "Name of the test",
            "required": true,
            "default_value": null
          },
          {
            "name": "test_date",
            "description": "Test date and time",
            "required": true,
            "default_value": null
          }
        ],
        "content": {
          "email_subject": "Reminder: {{test_name}} scheduled for {{test_date}}",
          "email_body": "Dear {{student_name}},\n\nThis is a friendly reminder that your {{test_name}} is scheduled for {{test_date}}...",
          "sms_message": "Hi {{student_name}}, {{test_name}} is on {{test_date}}. Good luck!",
          "push_title": "Test Reminder",
          "push_body": "{{test_name}} is scheduled for {{test_date}}"
        },
        "usage_stats": {
          "times_used": 45,
          "last_used": "2024-02-01T09:00:00Z",
          "average_delivery_rate": 95.2,
          "average_open_rate": 72.8
        },
        "created_by": {
          "id": "uuid",
          "name": "System",
          "role": "system"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### POST /notification-templates

**Purpose**: Create new notification template
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "Payment Due Reminder",
  "description": "Template for payment due reminders",
  "category": "payment",
  "type": "payment",
  "channels_supported": ["email", "sms"],
  "variables": [
    {
      "name": "student_name",
      "description": "Student's full name",
      "required": true
    },
    {
      "name": "amount_due",
      "description": "Amount due for payment",
      "required": true
    },
    {
      "name": "due_date",
      "description": "Payment due date",
      "required": true
    }
  ],
  "content": {
    "email_subject": "Payment Reminder: ₹{{amount_due}} due on {{due_date}}",
    "email_body": "Dear {{student_name}},\n\nThis is a reminder that your payment of ₹{{amount_due}} is due on {{due_date}}...",
    "sms_message": "Payment reminder: ₹{{amount_due}} due on {{due_date}}. Pay now to avoid late fees."
  },
  "is_active": true
}
```

### 10.3 Automation & Analytics APIs

#### GET /notification-automations

**Purpose**: Get automated notification rules
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "automations": [
      {
        "id": "uuid",
        "name": "Test Reminder Automation",
        "description": "Automatically send test reminders 24 hours before test",
        "trigger_event": "test_scheduled",
        "trigger_conditions": {
          "time_before": "24_hours",
          "test_types": ["mock_test", "quiz"],
          "course_filters": ["uuid1", "uuid2"]
        },
        "template_id": "uuid",
        "template_name": "Test Reminder",
        "channels": ["email", "sms", "push"],
        "recipient_settings": {
          "recipient_type": "enrolled_students",
          "include_parents": false,
          "filters": {
            "enrollment_status": "active",
            "attendance_min": 75
          }
        },
        "is_active": true,
        "statistics": {
          "total_triggered": 156,
          "successful_sends": 148,
          "failed_sends": 8,
          "average_delivery_rate": 94.9
        },
        "created_at": "2024-01-01T00:00:00Z",
        "last_triggered": "2024-02-01T09:00:00Z"
      }
    ]
  }
}
```

#### POST /notification-automations

**Purpose**: Create new automation rule
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "Payment Due Automation",
  "description": "Send payment reminders 3 days before due date",
  "trigger_event": "payment_due_approaching",
  "trigger_conditions": {
    "time_before": "72_hours",
    "amount_min": 1000,
    "payment_types": ["course_fee", "installment"]
  },
  "template_id": "uuid",
  "channels": ["email", "sms"],
  "recipient_settings": {
    "recipient_type": "students_with_pending_payments",
    "include_parents": true,
    "filters": {
      "exclude_paid": true,
      "exclude_cancelled": true
    }
  },
  "is_active": true
}
```

#### GET /notifications/analytics

**Purpose**: Get comprehensive notification analytics
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `date_from` (optional): Start date for analytics
- `date_to` (optional): End date for analytics
- `type` (optional): Notification type filter
- `channel` (optional): Channel filter

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_notifications": 456,
      "sent_notifications": 389,
      "scheduled_notifications": 45,
      "failed_notifications": 22,
      "total_recipients": 125670,
      "overall_delivery_rate": 94.2,
      "overall_open_rate": 68.7,
      "overall_click_rate": 23.4
    },
    "channel_performance": [
      {
        "channel": "email",
        "notifications_sent": 389,
        "total_recipients": 125670,
        "delivered": 118386,
        "opened": 86342,
        "clicked": 29456,
        "delivery_rate": 94.2,
        "open_rate": 72.9,
        "click_rate": 34.1,
        "unsubscribe_rate": 0.8
      },
      {
        "channel": "sms",
        "notifications_sent": 234,
        "total_recipients": 78450,
        "delivered": 76234,
        "clicked": 9876,
        "delivery_rate": 97.2,
        "click_rate": 12.9
      },
      {
        "channel": "push",
        "notifications_sent": 345,
        "total_recipients": 89234,
        "delivered": 76543,
        "opened": 45678,
        "delivery_rate": 85.8,
        "open_rate": 59.7
      }
    ],
    "type_wise_analytics": [
      {
        "type": "test",
        "notifications": 89,
        "delivery_rate": 96.2,
        "open_rate": 78.9,
        "engagement_score": 8.4
      },
      {
        "type": "payment",
        "notifications": 67,
        "delivery_rate": 94.8,
        "open_rate": 82.3,
        "engagement_score": 7.9
      },
      {
        "type": "course",
        "notifications": 123,
        "delivery_rate": 92.1,
        "open_rate": 65.4,
        "engagement_score": 7.2
      }
    ],
    "automation_performance": [
      {
        "automation_id": "uuid",
        "name": "Test Reminder Automation",
        "triggered_count": 156,
        "success_rate": 94.9,
        "average_delivery_rate": 95.2,
        "average_open_rate": 76.8
      }
    ],
    "monthly_trends": [
      {
        "month": "2024-01",
        "notifications_sent": 89,
        "delivery_rate": 94.5,
        "open_rate": 69.2,
        "click_rate": 24.1
      }
    ],
    "recipient_insights": {
      "most_engaged_segments": [
        {
          "segment": "JEE Main Students",
          "open_rate": 82.3,
          "click_rate": 34.5,
          "engagement_score": 8.9
        }
      ],
      "least_engaged_segments": [
        {
          "segment": "Inactive Students",
          "open_rate": 23.4,
          "click_rate": 5.6,
          "engagement_score": 3.2
        }
      ]
    },
    "best_performing_templates": [
      {
        "template_id": "uuid",
        "name": "Test Reminder",
        "usage_count": 45,
        "average_delivery_rate": 96.8,
        "average_open_rate": 78.9,
        "average_click_rate": 32.1
      }
    ]
  }
}
```

#### GET /notifications/delivery-status

**Purpose**: Get real-time delivery status of recent notifications
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "active_deliveries": [
      {
        "notification_id": "uuid",
        "title": "Course Schedule Update",
        "status": "sending",
        "progress": 75,
        "total_recipients": 456,
        "sent": 342,
        "delivered": 298,
        "failed": 12,
        "estimated_completion": "2024-02-01T16:05:00Z"
      }
    ],
    "recent_completions": [
      {
        "notification_id": "uuid",
        "title": "Test Reminder",
        "completed_at": "2024-02-01T15:30:00Z",
        "total_recipients": 567,
        "delivery_rate": 95.6,
        "completion_time": 300
      }
    ],
    "failed_deliveries": [
      {
        "notification_id": "uuid",
        "title": "Payment Reminder",
        "failed_at": "2024-02-01T14:20:00Z",
        "failure_reason": "Template processing error",
        "affected_recipients": 234,
        "retry_scheduled": true,
        "retry_at": "2024-02-01T16:00:00Z"
      }
    ]
  }
}
```

---

## 🎯 Notification Center API Summary

### **Key Features Implemented:**

1. **📣 Complete Notification System**: Comprehensive notification creation and management
2. **📊 Multi-channel Delivery**: Email, SMS, push, in-app, and WhatsApp support
3. **🎨 Template Management**: Reusable templates with variable substitution
4. **🤖 Automation Engine**: Event-triggered automated notifications
5. **📈 Advanced Analytics**: Delivery tracking and engagement metrics
6. **⏰ Smart Scheduling**: Flexible scheduling with timezone support

### **API Endpoints Summary:**

| Module                      | Endpoint                         | Method | Purpose                    |
| --------------------------- | -------------------------------- | ------ | -------------------------- |
| **Notification Management** | `/notifications`                 | GET    | List notifications         |
|                             | `/notifications/{id}`            | GET    | Detailed notification info |
|                             | `/notifications`                 | POST   | Create notification        |
|                             | `/notifications/{id}`            | PUT    | Update notification        |
|                             | `/notifications/{id}`            | DELETE | Cancel notification        |
|                             | `/notifications/{id}/send`       | POST   | Send immediately           |
| **Template Management**     | `/notification-templates`        | GET    | List templates             |
|                             | `/notification-templates`        | POST   | Create template            |
| **Automation & Analytics**  | `/notification-automations`      | GET    | List automations           |
|                             | `/notification-automations`      | POST   | Create automation          |
|                             | `/notifications/analytics`       | GET    | Comprehensive analytics    |
|                             | `/notifications/delivery-status` | GET    | Real-time delivery status  |

### **Business Logic Covered:**

✅ **Multi-channel Communication**: Email, SMS, push, in-app notifications
✅ **Template System**: Reusable templates with dynamic variables
✅ **Automation Engine**: Event-triggered notifications with conditions
✅ **Recipient Management**: Flexible targeting with filters and segments
✅ **Delivery Tracking**: Real-time delivery status and analytics
✅ **Engagement Analytics**: Open rates, click rates, engagement scoring
✅ **Scheduling System**: Immediate and scheduled delivery with timezone support

### **Technical Features:**

🔐 **Secure Delivery**: Multi-provider support with failover mechanisms
📱 **Mobile Optimized**: Push notifications and responsive email templates
🔍 **Advanced Analytics**: Comprehensive tracking and performance metrics
📊 **Rich Reporting**: Detailed analytics with trend analysis
⚡ **Real-time Processing**: Instant delivery status and progress tracking
🎯 **Smart Targeting**: Advanced recipient filtering and segmentation
💾 **Template Engine**: Dynamic content with variable substitution

**🚀 The Notification Center APIs are now complete and production-ready for backend implementation!**

---

## 1️⃣1️⃣ CONTENT REVIEW & MODERATION APIs

### 11.1 Content Review Management APIs

#### GET /content-reviews

**Purpose**: Get list of content items pending review with comprehensive filtering
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in title, description, author name
- `content_type` (optional): `course`, `video`, `document`, `test`, `assignment`, `forum_post`, `comment`
- `status` (optional): `pending`, `under_review`, `approved`, `rejected`, `revision_required`
- `priority` (optional): `low`, `medium`, `high`, `urgent`
- `reviewer_id` (optional): Assigned reviewer filter
- `author_id` (optional): Content author filter
- `subject_id` (optional): Subject filter
- `date_from` (optional): Submission date from (YYYY-MM-DD)
- `date_to` (optional): Submission date to (YYYY-MM-DD)
- `review_deadline` (optional): Items with review deadline approaching
- `sort_by` (optional): `created_at`, `priority`, `deadline`, `title`, `author_name`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "content_reviews": [
      {
        "id": "uuid",
        "content_id": "uuid",
        "title": "JEE Main Physics - Thermodynamics Chapter 5",
        "content_type": "video",
        "description": "Comprehensive video lecture covering laws of thermodynamics with problem-solving techniques",
        "author": {
          "id": "uuid",
          "name": "Dr. Rajesh Kumar",
          "role": "instructor",
          "email": "<EMAIL>",
          "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg"
        },
        "subject": {
          "id": "uuid",
          "name": "Physics",
          "code": "PHY"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete"
        },
        "content_details": {
          "file_url": "https://cdn.utkrishta.com/videos/thermodynamics-ch5.mp4",
          "file_size": 524288000,
          "duration": 3600,
          "format": "mp4",
          "thumbnail": "https://cdn.utkrishta.com/thumbnails/thermodynamics-ch5.jpg"
        },
        "review_info": {
          "status": "under_review",
          "priority": "high",
          "assigned_reviewer": {
            "id": "uuid",
            "name": "Content Review Team",
            "email": "<EMAIL>"
          },
          "submission_date": "2024-02-01T10:30:00Z",
          "review_deadline": "2024-02-03T23:59:59Z",
          "review_started_at": "2024-02-01T14:00:00Z",
          "estimated_completion": "2024-02-02T16:00:00Z"
        },
        "quality_metrics": {
          "content_quality_score": 8.5,
          "technical_quality_score": 9.2,
          "educational_value_score": 8.8,
          "overall_score": 8.8,
          "automated_checks": {
            "audio_quality": "excellent",
            "video_quality": "good",
            "content_accuracy": "pending_review",
            "plagiarism_check": "passed"
          }
        },
        "review_history": [
          {
            "action": "submitted",
            "timestamp": "2024-02-01T10:30:00Z",
            "user": "Dr. Rajesh Kumar",
            "notes": "Initial submission for review"
          },
          {
            "action": "assigned",
            "timestamp": "2024-02-01T11:00:00Z",
            "user": "System",
            "notes": "Auto-assigned to Content Review Team"
          }
        ],
        "tags": ["thermodynamics", "jee_main", "physics", "video_lecture"],
        "created_at": "2024-02-01T10:30:00Z",
        "updated_at": "2024-02-01T14:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 89,
      "total_pages": 4,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_pending": 89,
      "under_review": 23,
      "approved_today": 15,
      "rejected_today": 3,
      "overdue_reviews": 8,
      "average_review_time": 48,
      "review_backlog": 34
    }
  }
}
```

#### GET /content-reviews/{id}

**Purpose**: Get detailed content review information
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Content review UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "content_review": {
      "id": "uuid",
      "content_id": "uuid",
      "title": "JEE Main Physics - Thermodynamics Chapter 5",
      "content_type": "video",
      "description": "Comprehensive video lecture covering laws of thermodynamics with detailed problem-solving techniques and real-world applications",
      "author": {
        "id": "uuid",
        "name": "Dr. Rajesh Kumar",
        "role": "instructor",
        "email": "<EMAIL>",
        "phone": "+91-**********",
        "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
        "experience": 15,
        "rating": 4.8
      },
      "subject": {
        "id": "uuid",
        "name": "Physics",
        "code": "PHY",
        "category": "Science"
      },
      "course": {
        "id": "uuid",
        "title": "JEE Main Physics Complete",
        "level": "intermediate",
        "target_exam": "JEE Main"
      },
      "content_details": {
        "file_url": "https://cdn.utkrishta.com/videos/thermodynamics-ch5.mp4",
        "file_size": 524288000,
        "duration": 3600,
        "format": "mp4",
        "resolution": "1080p",
        "bitrate": "2000kbps",
        "thumbnail": "https://cdn.utkrishta.com/thumbnails/thermodynamics-ch5.jpg",
        "captions_available": true,
        "transcript_available": true,
        "chapters": [
          {
            "title": "Introduction to Thermodynamics",
            "start_time": 0,
            "duration": 600
          },
          {
            "title": "First Law of Thermodynamics",
            "start_time": 600,
            "duration": 900
          },
          {
            "title": "Problem Solving Session",
            "start_time": 1500,
            "duration": 2100
          }
        ]
      },
      "review_info": {
        "status": "under_review",
        "priority": "high",
        "assigned_reviewer": {
          "id": "uuid",
          "name": "Content Review Team",
          "email": "<EMAIL>",
          "specialization": ["Physics", "Engineering"]
        },
        "submission_date": "2024-02-01T10:30:00Z",
        "review_deadline": "2024-02-03T23:59:59Z",
        "review_started_at": "2024-02-01T14:00:00Z",
        "estimated_completion": "2024-02-02T16:00:00Z",
        "review_checklist": {
          "content_accuracy": "in_progress",
          "educational_value": "pending",
          "technical_quality": "completed",
          "compliance_check": "completed",
          "plagiarism_check": "completed"
        }
      },
      "quality_assessment": {
        "content_quality": {
          "score": 8.5,
          "criteria": {
            "accuracy": 9.0,
            "clarity": 8.5,
            "completeness": 8.0,
            "relevance": 9.0
          },
          "feedback": "Content is accurate and well-structured. Minor improvements needed in explanation clarity."
        },
        "technical_quality": {
          "score": 9.2,
          "criteria": {
            "audio_quality": 9.5,
            "video_quality": 9.0,
            "editing": 9.0,
            "presentation": 9.2
          },
          "feedback": "Excellent technical quality with clear audio and professional presentation."
        },
        "educational_value": {
          "score": 8.8,
          "criteria": {
            "learning_objectives": 9.0,
            "engagement": 8.5,
            "practical_application": 9.0,
            "difficulty_level": 8.5
          },
          "feedback": "Strong educational value with good practical examples."
        },
        "overall_score": 8.8,
        "recommendation": "approve_with_minor_changes"
      },
      "automated_checks": {
        "plagiarism_check": {
          "status": "passed",
          "similarity_score": 12.5,
          "sources_found": 2,
          "report_url": "https://cdn.utkrishta.com/reports/plagiarism-check-uuid.pdf"
        },
        "content_moderation": {
          "status": "passed",
          "inappropriate_content": false,
          "language_appropriateness": true,
          "copyright_issues": false
        },
        "technical_validation": {
          "file_integrity": "passed",
          "format_compliance": "passed",
          "accessibility_check": "passed",
          "mobile_compatibility": "passed"
        }
      },
      "review_comments": [
        {
          "id": "uuid",
          "reviewer": {
            "id": "uuid",
            "name": "Dr. Priya Sharma",
            "role": "content_reviewer"
          },
          "comment": "The thermodynamics concepts are explained clearly. However, the problem-solving section could benefit from more step-by-step explanations.",
          "category": "content_quality",
          "timestamp": "2024-02-01T15:30:00Z",
          "is_resolved": false
        }
      ],
      "revision_requests": [
        {
          "id": "uuid",
          "description": "Add more detailed explanations in the problem-solving section",
          "category": "content_improvement",
          "priority": "medium",
          "requested_by": "Dr. Priya Sharma",
          "requested_at": "2024-02-01T15:45:00Z",
          "status": "pending"
        }
      ],
      "compliance_info": {
        "copyright_cleared": true,
        "content_guidelines_met": true,
        "accessibility_compliant": true,
        "age_appropriate": true,
        "language_appropriate": true
      },
      "review_history": [
        {
          "action": "submitted",
          "timestamp": "2024-02-01T10:30:00Z",
          "user": "Dr. Rajesh Kumar",
          "notes": "Initial submission for review"
        },
        {
          "action": "assigned",
          "timestamp": "2024-02-01T11:00:00Z",
          "user": "System",
          "notes": "Auto-assigned to Content Review Team"
        },
        {
          "action": "review_started",
          "timestamp": "2024-02-01T14:00:00Z",
          "user": "Dr. Priya Sharma",
          "notes": "Started detailed content review"
        }
      ],
      "tags": [
        "thermodynamics",
        "jee_main",
        "physics",
        "video_lecture",
        "problem_solving"
      ],
      "created_at": "2024-02-01T10:30:00Z",
      "updated_at": "2024-02-01T15:45:00Z"
    }
  }
}
```

#### POST /content-reviews/{id}/review

**Purpose**: Submit content review decision and feedback
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Content review UUID
  **Request Body**:

```json
{
  "decision": "approve_with_changes",
  "quality_assessment": {
    "content_quality": {
      "score": 8.5,
      "criteria": {
        "accuracy": 9.0,
        "clarity": 8.5,
        "completeness": 8.0,
        "relevance": 9.0
      },
      "feedback": "Content is accurate and well-structured. Minor improvements needed in explanation clarity."
    },
    "technical_quality": {
      "score": 9.2,
      "criteria": {
        "audio_quality": 9.5,
        "video_quality": 9.0,
        "editing": 9.0,
        "presentation": 9.2
      },
      "feedback": "Excellent technical quality with clear audio and professional presentation."
    },
    "educational_value": {
      "score": 8.8,
      "criteria": {
        "learning_objectives": 9.0,
        "engagement": 8.5,
        "practical_application": 9.0,
        "difficulty_level": 8.5
      },
      "feedback": "Strong educational value with good practical examples."
    },
    "overall_score": 8.8
  },
  "review_comments": [
    {
      "comment": "The thermodynamics concepts are explained clearly. However, the problem-solving section could benefit from more step-by-step explanations.",
      "category": "content_quality",
      "priority": "medium"
    }
  ],
  "revision_requests": [
    {
      "description": "Add more detailed explanations in the problem-solving section",
      "category": "content_improvement",
      "priority": "medium",
      "deadline": "2024-02-05T23:59:59Z"
    }
  ],
  "compliance_checklist": {
    "copyright_cleared": true,
    "content_guidelines_met": true,
    "accessibility_compliant": true,
    "age_appropriate": true,
    "language_appropriate": true
  },
  "next_steps": "revision_required",
  "estimated_revision_time": 24,
  "notify_author": true,
  "internal_notes": "Good content overall, minor revisions needed for clarity"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Content review completed successfully",
  "data": {
    "review": {
      "id": "uuid",
      "status": "revision_required",
      "overall_score": 8.8,
      "decision": "approve_with_changes",
      "revision_deadline": "2024-02-05T23:59:59Z",
      "reviewed_at": "2024-02-02T16:30:00Z",
      "reviewed_by": "Dr. Priya Sharma"
    }
  }
}
```

#### POST /content-reviews/{id}/assign

**Purpose**: Assign content review to specific reviewer
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `id`: Content review UUID
  **Request Body**:

```json
{
  "reviewer_id": "uuid",
  "priority": "high",
  "deadline": "2024-02-03T23:59:59Z",
  "notes": "Urgent review required for course launch",
  "notify_reviewer": true
}
```

### 11.2 Moderation Workflow APIs

#### GET /moderation-rules

**Purpose**: Get content moderation rules and policies
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "moderation_rules": [
      {
        "id": "uuid",
        "name": "Video Content Quality Standards",
        "category": "video",
        "description": "Quality standards for video content including technical and educational criteria",
        "criteria": [
          {
            "name": "Audio Quality",
            "weight": 20,
            "min_score": 7.0,
            "description": "Clear audio without background noise"
          },
          {
            "name": "Video Quality",
            "weight": 20,
            "description": "HD resolution with stable footage"
          },
          {
            "name": "Content Accuracy",
            "weight": 30,
            "min_score": 8.0,
            "description": "Factually correct and up-to-date information"
          },
          {
            "name": "Educational Value",
            "weight": 30,
            "min_score": 7.5,
            "description": "Clear learning objectives and practical application"
          }
        ],
        "automated_checks": [
          "plagiarism_detection",
          "audio_quality_analysis",
          "video_quality_analysis",
          "content_appropriateness"
        ],
        "approval_threshold": 8.0,
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### POST /moderation-rules

**Purpose**: Create new moderation rule
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "name": "Document Content Standards",
  "category": "document",
  "description": "Quality standards for document content",
  "criteria": [
    {
      "name": "Content Accuracy",
      "weight": 40,
      "min_score": 8.5,
      "description": "Factually correct information with proper citations"
    },
    {
      "name": "Readability",
      "weight": 30,
      "min_score": 7.0,
      "description": "Clear and understandable language"
    },
    {
      "name": "Structure",
      "weight": 30,
      "min_score": 7.5,
      "description": "Well-organized content with proper formatting"
    }
  ],
  "automated_checks": [
    "plagiarism_detection",
    "readability_analysis",
    "grammar_check"
  ],
  "approval_threshold": 8.0,
  "is_active": true
}
```

#### GET /content-reviews/analytics

**Purpose**: Get content review analytics and performance metrics
**Authentication**: Required (Admin/Super Admin)
**Query Parameters**:

- `date_from` (optional): Start date for analytics
- `date_to` (optional): End date for analytics
- `content_type` (optional): Content type filter
- `reviewer_id` (optional): Reviewer-specific analytics

**Response**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "total_reviews": 456,
      "pending_reviews": 89,
      "completed_reviews": 367,
      "approved_content": 298,
      "rejected_content": 45,
      "revision_required": 24,
      "average_review_time": 48,
      "approval_rate": 81.2
    },
    "content_type_breakdown": [
      {
        "content_type": "video",
        "total_reviews": 234,
        "approved": 189,
        "rejected": 28,
        "revision_required": 17,
        "approval_rate": 80.8,
        "average_review_time": 52
      },
      {
        "content_type": "document",
        "total_reviews": 123,
        "approved": 109,
        "rejected": 8,
        "revision_required": 6,
        "approval_rate": 88.6,
        "average_review_time": 36
      }
    ],
    "reviewer_performance": [
      {
        "reviewer_id": "uuid",
        "name": "Dr. Priya Sharma",
        "reviews_completed": 45,
        "average_review_time": 42,
        "approval_rate": 84.4,
        "quality_consistency": 9.2,
        "feedback_quality": 8.8
      }
    ],
    "quality_trends": [
      {
        "month": "2024-01",
        "average_quality_score": 8.4,
        "approval_rate": 82.1,
        "revision_rate": 15.3,
        "rejection_rate": 2.6
      }
    ],
    "common_issues": [
      {
        "issue": "Audio quality problems",
        "frequency": 23,
        "content_type": "video",
        "impact": "medium"
      },
      {
        "issue": "Incomplete explanations",
        "frequency": 18,
        "content_type": "video",
        "impact": "high"
      }
    ],
    "automated_check_performance": {
      "plagiarism_detection": {
        "checks_performed": 456,
        "issues_found": 12,
        "accuracy_rate": 97.4
      },
      "quality_analysis": {
        "checks_performed": 234,
        "correlation_with_manual": 89.2
      }
    }
  }
}
```

### 11.3 Automated Moderation APIs

#### POST /content-reviews/auto-moderate

**Purpose**: Run automated moderation checks on content
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "content_id": "uuid",
  "content_type": "video",
  "checks_to_run": [
    "plagiarism_detection",
    "audio_quality_analysis",
    "video_quality_analysis",
    "content_appropriateness",
    "accessibility_check"
  ],
  "priority": "high"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Automated moderation initiated",
  "data": {
    "moderation_job": {
      "id": "uuid",
      "content_id": "uuid",
      "status": "processing",
      "checks_running": 5,
      "estimated_completion": "2024-02-01T16:15:00Z",
      "started_at": "2024-02-01T16:00:00Z"
    }
  }
}
```

#### GET /content-reviews/auto-moderate/{job_id}

**Purpose**: Get automated moderation results
**Authentication**: Required (Admin/Super Admin)
**Path Parameters**:

- `job_id`: Moderation job UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "moderation_results": {
      "job_id": "uuid",
      "content_id": "uuid",
      "status": "completed",
      "overall_score": 8.6,
      "recommendation": "approve",
      "completed_at": "2024-02-01T16:12:00Z",
      "processing_time": 720,
      "check_results": {
        "plagiarism_detection": {
          "status": "passed",
          "score": 9.5,
          "similarity_percentage": 8.2,
          "sources_found": 1,
          "details": "Low similarity detected, within acceptable limits"
        },
        "audio_quality_analysis": {
          "status": "passed",
          "score": 9.2,
          "metrics": {
            "clarity": 9.5,
            "volume_consistency": 9.0,
            "background_noise": 9.0,
            "speech_quality": 9.2
          },
          "details": "Excellent audio quality with minimal background noise"
        },
        "video_quality_analysis": {
          "status": "passed",
          "score": 8.8,
          "metrics": {
            "resolution": 9.0,
            "stability": 8.5,
            "lighting": 9.0,
            "focus": 8.8
          },
          "details": "Good video quality with stable footage"
        },
        "content_appropriateness": {
          "status": "passed",
          "score": 9.8,
          "checks": {
            "inappropriate_language": false,
            "violent_content": false,
            "adult_content": false,
            "discriminatory_content": false
          },
          "details": "Content is appropriate for educational use"
        },
        "accessibility_check": {
          "status": "warning",
          "score": 7.5,
          "issues": [
            {
              "type": "missing_captions",
              "severity": "medium",
              "description": "Video lacks closed captions"
            }
          ],
          "recommendations": ["Add closed captions for better accessibility"]
        }
      },
      "flags_raised": [
        {
          "type": "accessibility",
          "severity": "medium",
          "description": "Missing closed captions",
          "requires_manual_review": false
        }
      ],
      "next_steps": "manual_review_recommended"
    }
  }
}
```

#### GET /content-reviews/queue

**Purpose**: Get content review queue status and management
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "queue_status": {
      "total_in_queue": 89,
      "high_priority": 12,
      "medium_priority": 45,
      "low_priority": 32,
      "overdue_items": 8,
      "estimated_processing_time": 168,
      "active_reviewers": 5
    },
    "queue_distribution": [
      {
        "content_type": "video",
        "count": 45,
        "average_wait_time": 52
      },
      {
        "content_type": "document",
        "count": 28,
        "average_wait_time": 36
      },
      {
        "content_type": "test",
        "count": 16,
        "average_wait_time": 24
      }
    ],
    "reviewer_workload": [
      {
        "reviewer_id": "uuid",
        "name": "Dr. Priya Sharma",
        "assigned_reviews": 8,
        "capacity": 12,
        "availability": "available",
        "specialization": ["Physics", "Chemistry"]
      }
    ],
    "bottlenecks": [
      {
        "type": "reviewer_capacity",
        "description": "Physics content reviews are backlogged",
        "impact": "high",
        "suggested_action": "Assign additional physics reviewers"
      }
    ]
  }
}
```

---

## 🎯 Content Review & Moderation API Summary

### **Key Features Implemented:**

1. **🧾 Complete Review System**: Comprehensive content review workflow management
2. **🤖 Automated Moderation**: AI-powered content analysis and quality checks
3. **📊 Quality Assessment**: Multi-criteria scoring and detailed feedback system
4. **🔄 Workflow Management**: Reviewer assignment and deadline tracking
5. **📈 Analytics Dashboard**: Performance metrics and trend analysis
6. **⚡ Queue Management**: Efficient review queue and workload distribution

### **API Endpoints Summary:**

| Module                   | Endpoint                                  | Method | Purpose                |
| ------------------------ | ----------------------------------------- | ------ | ---------------------- |
| **Review Management**    | `/content-reviews`                        | GET    | List content reviews   |
|                          | `/content-reviews/{id}`                   | GET    | Detailed review info   |
|                          | `/content-reviews/{id}/review`            | POST   | Submit review decision |
|                          | `/content-reviews/{id}/assign`            | POST   | Assign reviewer        |
| **Moderation Workflow**  | `/moderation-rules`                       | GET    | List moderation rules  |
|                          | `/moderation-rules`                       | POST   | Create moderation rule |
|                          | `/content-reviews/analytics`              | GET    | Review analytics       |
| **Automated Moderation** | `/content-reviews/auto-moderate`          | POST   | Run automated checks   |
|                          | `/content-reviews/auto-moderate/{job_id}` | GET    | Get automation results |
|                          | `/content-reviews/queue`                  | GET    | Queue management       |

### **Business Logic Covered:**

✅ **Multi-stage Review Process**: Submission → Review → Approval/Revision workflow
✅ **Quality Scoring**: Multi-criteria assessment with weighted scoring
✅ **Automated Checks**: Plagiarism, quality analysis, and compliance validation
✅ **Reviewer Management**: Assignment, workload tracking, and performance metrics
✅ **Compliance Monitoring**: Copyright, accessibility, and content guidelines
✅ **Revision Tracking**: Change requests and improvement workflows
✅ **Analytics Engine**: Performance insights and optimization recommendations

### **Technical Features:**

🔐 **Secure Review Process**: Role-based access and audit trail maintenance
📱 **Mobile Optimized**: Responsive review interfaces and mobile-friendly workflows
🔍 **Advanced Analytics**: Comprehensive performance tracking and insights
📊 **Rich Reporting**: Detailed analytics with trend analysis and bottleneck identification
⚡ **Real-time Processing**: Instant status updates and automated workflow triggers
🎯 **Smart Assignment**: Intelligent reviewer matching based on expertise and workload
💾 **Comprehensive Tracking**: Complete audit trail and revision history

**🚀 The Content Review & Moderation APIs are now complete and production-ready for backend implementation!**

---

## 1️⃣2️⃣ ADMIN PROFILE & ROLES APIs

### 12.1 Admin Profile Management APIs

#### GET /admin/profile

**Purpose**: Get current admin user profile information
**Authentication**: Required (Admin/Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "admin": {
      "id": "uuid",
      "username": "admin_user",
      "email": "<EMAIL>",
      "first_name": "Admin",
      "last_name": "User",
      "full_name": "Admin User",
      "phone": "+91-**********",
      "avatar": "https://cdn.utkrishta.com/avatars/admin-user.jpg",
      "role": {
        "id": "uuid",
        "name": "Super Admin",
        "code": "super_admin",
        "level": 1,
        "description": "Full system access with all permissions"
      },
      "permissions": [
        "users.create",
        "users.read",
        "users.update",
        "users.delete",
        "courses.manage",
        "payments.manage",
        "reports.access",
        "system.configure"
      ],
      "department": "Administration",
      "position": "System Administrator",
      "employee_id": "EMP001",
      "hire_date": "2023-01-15",
      "status": "active",
      "last_login": "2024-02-01T15:30:00Z",
      "login_count": 1247,
      "preferences": {
        "theme": "light",
        "language": "en",
        "timezone": "Asia/Kolkata",
        "notifications": {
          "email": true,
          "push": true,
          "sms": false
        },
        "dashboard_layout": "default",
        "items_per_page": 25
      },
      "security": {
        "two_factor_enabled": true,
        "last_password_change": "2024-01-01T00:00:00Z",
        "password_expires_at": "2024-04-01T00:00:00Z",
        "failed_login_attempts": 0,
        "account_locked": false
      },
      "activity_summary": {
        "sessions_this_month": 45,
        "actions_performed": 1234,
        "last_activity": "2024-02-01T15:30:00Z",
        "most_used_features": [
          "Student Management",
          "Course Management",
          "Reports"
        ]
      },
      "created_at": "2023-01-15T10:00:00Z",
      "updated_at": "2024-02-01T15:30:00Z"
    }
  }
}
```

#### PUT /admin/profile

**Purpose**: Update current admin user profile
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "first_name": "Admin",
  "last_name": "User",
  "phone": "+91-**********",
  "department": "Administration",
  "position": "System Administrator",
  "preferences": {
    "theme": "dark",
    "language": "en",
    "timezone": "Asia/Kolkata",
    "notifications": {
      "email": true,
      "push": true,
      "sms": false
    },
    "dashboard_layout": "compact",
    "items_per_page": 50
  }
}
```

#### POST /admin/change-password

**Purpose**: Change admin user password
**Authentication**: Required (Admin/Super Admin)
**Request Body**:

```json
{
  "current_password": "current_password_here",
  "new_password": "new_secure_password",
  "confirm_password": "new_secure_password"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Password changed successfully",
  "data": {
    "password_changed_at": "2024-02-01T16:00:00Z",
    "password_expires_at": "2024-05-01T16:00:00Z"
  }
}
```

#### POST /admin/upload-avatar

**Purpose**: Upload admin user avatar
**Authentication**: Required (Admin/Super Admin)
**Request**: Multipart form data with image file

**Response**:

```json
{
  "success": true,
  "message": "Avatar uploaded successfully",
  "data": {
    "avatar_url": "https://cdn.utkrishta.com/avatars/admin-user-new.jpg",
    "uploaded_at": "2024-02-01T16:15:00Z"
  }
}
```

### 12.2 Admin User Management APIs

#### GET /admin/users

**Purpose**: Get list of admin users with comprehensive filtering
**Authentication**: Required (Super Admin)
**Query Parameters**:

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 25, max: 100)
- `search` (optional): Search in name, email, username
- `role_id` (optional): Role filter
- `status` (optional): `active`, `inactive`, `suspended`, `pending`
- `department` (optional): Department filter
- `sort_by` (optional): `created_at`, `last_login`, `name`, `role`
- `sort_order` (optional): `asc`, `desc` (default: `desc`)

**Response**:

```json
{
  "success": true,
  "data": {
    "admin_users": [
      {
        "id": "uuid",
        "username": "content_admin",
        "email": "<EMAIL>",
        "full_name": "Content Administrator",
        "phone": "+91-9876543211",
        "avatar": "https://cdn.utkrishta.com/avatars/content-admin.jpg",
        "role": {
          "id": "uuid",
          "name": "Content Admin",
          "code": "content_admin",
          "level": 3
        },
        "department": "Content Management",
        "position": "Content Manager",
        "status": "active",
        "last_login": "2024-02-01T14:20:00Z",
        "login_count": 456,
        "created_at": "2023-03-01T10:00:00Z",
        "created_by": {
          "id": "uuid",
          "name": "Super Admin"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 12,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    },
    "summary": {
      "total_admins": 12,
      "active_admins": 10,
      "inactive_admins": 2,
      "super_admins": 2,
      "regular_admins": 10
    }
  }
}
```

#### GET /admin/users/{id}

**Purpose**: Get detailed admin user information
**Authentication**: Required (Super Admin)
**Path Parameters**:

- `id`: Admin user UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "admin_user": {
      "id": "uuid",
      "username": "content_admin",
      "email": "<EMAIL>",
      "first_name": "Content",
      "last_name": "Administrator",
      "full_name": "Content Administrator",
      "phone": "+91-9876543211",
      "avatar": "https://cdn.utkrishta.com/avatars/content-admin.jpg",
      "role": {
        "id": "uuid",
        "name": "Content Admin",
        "code": "content_admin",
        "level": 3,
        "description": "Manage content creation and review processes"
      },
      "permissions": [
        "content.create",
        "content.read",
        "content.update",
        "content.review",
        "courses.read",
        "students.read"
      ],
      "department": "Content Management",
      "position": "Content Manager",
      "employee_id": "EMP003",
      "hire_date": "2023-03-01",
      "status": "active",
      "last_login": "2024-02-01T14:20:00Z",
      "login_count": 456,
      "security": {
        "two_factor_enabled": false,
        "last_password_change": "2023-12-01T00:00:00Z",
        "password_expires_at": "2024-03-01T00:00:00Z",
        "failed_login_attempts": 0,
        "account_locked": false
      },
      "activity_log": [
        {
          "action": "login",
          "timestamp": "2024-02-01T14:20:00Z",
          "ip_address": "*************",
          "user_agent": "Mozilla/5.0..."
        },
        {
          "action": "content_approved",
          "timestamp": "2024-02-01T14:25:00Z",
          "details": "Approved video: JEE Physics Chapter 5"
        }
      ],
      "created_at": "2023-03-01T10:00:00Z",
      "created_by": {
        "id": "uuid",
        "name": "Super Admin",
        "email": "<EMAIL>"
      },
      "updated_at": "2024-02-01T14:20:00Z"
    }
  }
}
```

#### POST /admin/users

**Purpose**: Create new admin user
**Authentication**: Required (Super Admin)
**Request Body**:

```json
{
  "username": "finance_admin",
  "email": "<EMAIL>",
  "first_name": "Finance",
  "last_name": "Administrator",
  "phone": "+91-**********",
  "role_id": "uuid",
  "department": "Finance",
  "position": "Finance Manager",
  "employee_id": "EMP004",
  "hire_date": "2024-02-01",
  "password": "temporary_password",
  "send_welcome_email": true
}
```

#### PUT /admin/users/{id}

**Purpose**: Update admin user information
**Authentication**: Required (Super Admin)
**Path Parameters**:

- `id`: Admin user UUID
  **Request Body**: Same as POST but with optional fields

#### DELETE /admin/users/{id}

**Purpose**: Deactivate admin user
**Authentication**: Required (Super Admin)
**Path Parameters**:

- `id`: Admin user UUID

### 12.3 Role & Permission Management APIs

#### GET /admin/roles

**Purpose**: Get list of admin roles and permissions
**Authentication**: Required (Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "uuid",
        "name": "Super Admin",
        "code": "super_admin",
        "level": 1,
        "description": "Full system access with all permissions",
        "permissions": [
          {
            "id": "uuid",
            "name": "User Management",
            "code": "users.manage",
            "category": "user_management",
            "description": "Create, read, update, delete users"
          },
          {
            "id": "uuid",
            "name": "System Configuration",
            "code": "system.configure",
            "category": "system",
            "description": "Configure system settings and parameters"
          }
        ],
        "user_count": 2,
        "is_system_role": true,
        "is_active": true,
        "created_at": "2023-01-01T00:00:00Z"
      },
      {
        "id": "uuid",
        "name": "Content Admin",
        "code": "content_admin",
        "level": 3,
        "description": "Manage content creation and review processes",
        "permissions": [
          {
            "id": "uuid",
            "name": "Content Management",
            "code": "content.manage",
            "category": "content",
            "description": "Create, review, and publish content"
          },
          {
            "id": "uuid",
            "name": "Course Read Access",
            "code": "courses.read",
            "category": "courses",
            "description": "View course information"
          }
        ],
        "user_count": 3,
        "is_system_role": false,
        "is_active": true,
        "created_at": "2023-02-01T00:00:00Z"
      }
    ],
    "permission_categories": [
      {
        "category": "user_management",
        "name": "User Management",
        "description": "Permissions related to user management"
      },
      {
        "category": "content",
        "name": "Content Management",
        "description": "Permissions related to content creation and management"
      },
      {
        "category": "courses",
        "name": "Course Management",
        "description": "Permissions related to course management"
      },
      {
        "category": "payments",
        "name": "Payment Management",
        "description": "Permissions related to payment processing"
      },
      {
        "category": "reports",
        "name": "Reports & Analytics",
        "description": "Permissions related to reports and analytics"
      },
      {
        "category": "system",
        "name": "System Administration",
        "description": "Permissions related to system configuration"
      }
    ]
  }
}
```

#### POST /admin/roles

**Purpose**: Create new admin role
**Authentication**: Required (Super Admin)
**Request Body**:

```json
{
  "name": "Marketing Admin",
  "code": "marketing_admin",
  "level": 4,
  "description": "Manage marketing campaigns and student outreach",
  "permission_ids": ["uuid1", "uuid2", "uuid3"],
  "is_active": true
}
```

#### GET /admin/permissions

**Purpose**: Get all available permissions
**Authentication**: Required (Super Admin)

**Response**:

```json
{
  "success": true,
  "data": {
    "permissions": [
      {
        "id": "uuid",
        "name": "Create Users",
        "code": "users.create",
        "category": "user_management",
        "description": "Create new user accounts",
        "is_system_permission": true
      },
      {
        "id": "uuid",
        "name": "Manage Courses",
        "code": "courses.manage",
        "category": "courses",
        "description": "Full course management access",
        "is_system_permission": false
      }
    ]
  }
}
```

#### GET /admin/activity-logs

**Purpose**: Get admin activity logs and audit trail
**Authentication**: Required (Super Admin)
**Query Parameters**:

- `user_id` (optional): Filter by specific admin user
- `action` (optional): Filter by action type
- `date_from` (optional): Start date filter
- `date_to` (optional): End date filter

**Response**:

```json
{
  "success": true,
  "data": {
    "activity_logs": [
      {
        "id": "uuid",
        "user": {
          "id": "uuid",
          "name": "Content Administrator",
          "email": "<EMAIL>"
        },
        "action": "content_approved",
        "resource_type": "video",
        "resource_id": "uuid",
        "details": "Approved video: JEE Physics Chapter 5",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0...",
        "timestamp": "2024-02-01T14:25:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 1234,
      "total_pages": 50
    }
  }
}
```

---

## 🎯 Admin Profile & Roles API Summary

### **Key Features Implemented:**

1. **🔐 Complete Profile Management**: Comprehensive admin user profile and preferences
2. **👥 User Administration**: Full admin user lifecycle management
3. **🛡️ Role-Based Access Control**: Flexible role and permission system
4. **📊 Activity Tracking**: Complete audit trail and activity monitoring
5. **🔒 Security Features**: Password management and two-factor authentication
6. **⚙️ Preference Management**: Customizable user preferences and settings

### **API Endpoints Summary:**

| Module                 | Endpoint                 | Method | Purpose                   |
| ---------------------- | ------------------------ | ------ | ------------------------- |
| **Profile Management** | `/admin/profile`         | GET    | Get current admin profile |
|                        | `/admin/profile`         | PUT    | Update admin profile      |
|                        | `/admin/change-password` | POST   | Change password           |
|                        | `/admin/upload-avatar`   | POST   | Upload avatar             |
| **User Management**    | `/admin/users`           | GET    | List admin users          |
|                        | `/admin/users/{id}`      | GET    | Get admin user details    |
|                        | `/admin/users`           | POST   | Create admin user         |
|                        | `/admin/users/{id}`      | PUT    | Update admin user         |
|                        | `/admin/users/{id}`      | DELETE | Deactivate admin user     |
| **Role & Permission**  | `/admin/roles`           | GET    | List roles                |
|                        | `/admin/roles`           | POST   | Create role               |
|                        | `/admin/permissions`     | GET    | List permissions          |
|                        | `/admin/activity-logs`   | GET    | Activity audit trail      |

### **Business Logic Covered:**

✅ **Profile Management**: Complete admin profile with preferences and security
✅ **User Administration**: Full admin user lifecycle with role assignment
✅ **Role-Based Access**: Hierarchical role system with granular permissions
✅ **Security Features**: Password policies, 2FA, and account security
✅ **Activity Tracking**: Complete audit trail and activity monitoring
✅ **Permission System**: Granular permission control with categories
✅ **Preference Management**: Customizable UI and notification preferences

### **Technical Features:**

🔐 **Advanced Security**: Multi-factor authentication and password policies
📱 **Mobile Optimized**: Responsive admin interfaces and mobile access
🔍 **Comprehensive Auditing**: Complete activity tracking and audit trails
📊 **Rich Analytics**: Admin usage patterns and system insights
⚡ **Real-time Updates**: Instant permission changes and profile updates
🎯 **Granular Control**: Fine-grained permission system with role hierarchy
💾 **Secure Storage**: Encrypted password storage and secure session management

**🚀 The Admin Profile & Roles APIs are now complete and production-ready for backend implementation!**

---

## 🎉 **COMPLETE API SPECIFICATION SUMMARY**

### **📋 Total API Coverage:**

| **Section**                   | **APIs**     | **Key Features**                                        |
| ----------------------------- | ------------ | ------------------------------------------------------- |
| **1. Dashboard Analytics**    | 8 APIs       | Real-time metrics, performance insights, trend analysis |
| **2. User Authentication**    | 7 APIs       | Secure login, JWT tokens, password management           |
| **3. Course Management**      | 13 APIs      | Complete course lifecycle, content management           |
| **4. Instructor Management**  | 11 APIs      | Instructor profiles, performance tracking               |
| **5. Test Management**        | 12 APIs      | Test creation, analytics, result processing             |
| **6. Assignment Management**  | 10 APIs      | Assignment lifecycle, submission tracking               |
| **7. Student Management**     | 13 APIs      | Student profiles, performance analytics                 |
| **8. Payments Management**    | 11 APIs      | Transaction processing, financial analytics             |
| **9. Live Class Scheduler**   | 12 APIs      | Class scheduling, attendance tracking                   |
| **10. Notification Center**   | 12 APIs      | Multi-channel notifications, automation                 |
| **11. Content Review**        | 10 APIs      | Quality assurance, moderation workflows                 |
| **12. Admin Profile & Roles** | 13 APIs      | User management, role-based access control              |
| **TOTAL**                     | **132 APIs** | **Complete coaching business ecosystem**                |

### **🚀 Production-Ready Features:**

✅ **Complete Business Logic**: Every aspect of coaching business operations covered
✅ **Advanced Analytics**: Comprehensive reporting and insights across all modules
✅ **Security & Compliance**: Role-based access, audit trails, data protection
✅ **Scalable Architecture**: Designed for high-volume operations and growth
✅ **Mobile-First Design**: Responsive APIs for all device types
✅ **Real-time Capabilities**: Live updates, notifications, and monitoring
✅ **Integration Ready**: Seamless third-party service integration points

**🎯 Your complete coaching business admin panel API specification is now ready for world-class backend implementation!**
