/**
 * Error Handler Utilities
 * Custom error classes and error handling middleware
 */

/**
 * Custom Application Error class
 */
class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message);
    
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation Error class
 */
class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 422);
    this.errors = errors;
  }
}

/**
 * Authentication Error class
 */
class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401);
  }
}

/**
 * Authorization Error class
 */
class AuthorizationError extends AppError {
  constructor(message = 'Access forbidden') {
    super(message, 403);
  }
}

/**
 * Not Found Error class
 */
class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404);
  }
}

/**
 * Conflict Error class
 */
class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409);
  }
}

/**
 * Rate Limit Error class
 */
class RateLimitError extends AppError {
  constructor(message = 'Too many requests', retryAfter = 60) {
    super(message, 429);
    this.retryAfter = retryAfter;
  }
}

/**
 * Database Error class
 */
class DatabaseError extends AppError {
  constructor(message = 'Database operation failed', originalError = null) {
    super(message, 500);
    this.originalError = originalError;
  }
}

/**
 * External Service Error class
 */
class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error', statusCode = 502) {
    super(`${service}: ${message}`, statusCode);
    this.service = service;
  }
}

/**
 * Handle Cast Error (Invalid ObjectId)
 */
function handleCastError(err) {
  const message = `Invalid ${err.path}: ${err.value}`;
  return new AppError(message, 400);
}

/**
 * Handle Duplicate Field Error
 */
function handleDuplicateFieldError(err) {
  const field = Object.keys(err.keyValue)[0];
  const value = err.keyValue[field];
  const message = `${field} '${value}' already exists`;
  return new ConflictError(message);
}

/**
 * Handle Validation Error
 */
function handleValidationError(err) {
  const errors = Object.values(err.errors).map(el => ({
    field: el.path,
    message: el.message,
    value: el.value
  }));
  
  return new ValidationError('Validation failed', errors);
}

/**
 * Handle JWT Error
 */
function handleJWTError() {
  return new AuthenticationError('Invalid token. Please log in again');
}

/**
 * Handle JWT Expired Error
 */
function handleJWTExpiredError() {
  return new AuthenticationError('Your token has expired. Please log in again');
}

/**
 * Handle MySQL Error
 */
function handleMySQLError(err) {
  switch (err.code) {
    case 'ER_DUP_ENTRY':
      const field = err.sqlMessage.match(/for key '(.+?)'/)?.[1] || 'field';
      return new ConflictError(`Duplicate entry for ${field}`);
    
    case 'ER_NO_REFERENCED_ROW_2':
      return new AppError('Referenced record does not exist', 400);
    
    case 'ER_ROW_IS_REFERENCED_2':
      return new AppError('Cannot delete record as it is referenced by other records', 400);
    
    case 'ER_DATA_TOO_LONG':
      return new AppError('Data too long for field', 400);
    
    case 'ER_BAD_NULL_ERROR':
      const nullField = err.sqlMessage.match(/Column '(.+?)'/)?.[1] || 'field';
      return new AppError(`${nullField} cannot be null`, 400);
    
    case 'ECONNREFUSED':
      return new DatabaseError('Database connection refused');
    
    case 'ETIMEDOUT':
      return new DatabaseError('Database connection timeout');
    
    default:
      return new DatabaseError('Database operation failed', err);
  }
}

/**
 * Send error response in development
 */
function sendErrorDev(err, res) {
  const { errorResponse } = require('./responseHelper');
  
  return errorResponse(res, err.message, err.statusCode, {
    status: err.status,
    error: err,
    stack: err.stack,
    originalError: err.originalError
  });
}

/**
 * Send error response in production
 */
function sendErrorProd(err, res) {
  const { errorResponse, internalServerErrorResponse } = require('./responseHelper');
  
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    const errors = err.errors || null;
    const meta = {};
    
    if (err.retryAfter) {
      meta.retry_after = err.retryAfter;
      res.set('Retry-After', err.retryAfter);
    }
    
    return errorResponse(res, err.message, err.statusCode, errors, meta);
  }
  
  // Programming or other unknown error: don't leak error details
  console.error('ERROR 💥', err);
  return internalServerErrorResponse(res);
}

/**
 * Global error handling middleware
 */
function globalErrorHandler(err, req, res, next) {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, res);
  } else {
    let error = { ...err };
    error.message = err.message;
    
    // Handle specific error types
    if (error.name === 'CastError') error = handleCastError(error);
    if (error.code === 11000) error = handleDuplicateFieldError(error);
    if (error.name === 'ValidationError') error = handleValidationError(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();
    if (error.code && error.code.startsWith('ER_')) error = handleMySQLError(error);
    
    sendErrorProd(error, res);
  }
}

/**
 * Handle unhandled promise rejections
 */
function handleUnhandledRejection() {
  process.on('unhandledRejection', (err, promise) => {
    console.log('UNHANDLED PROMISE REJECTION! 💥 Shutting down...');
    console.log(err.name, err.message);
    
    // Close server gracefully
    if (global.server) {
      global.server.close(() => {
        process.exit(1);
      });
    } else {
      process.exit(1);
    }
  });
}

/**
 * Handle uncaught exceptions
 */
function handleUncaughtException() {
  process.on('uncaughtException', (err) => {
    console.log('UNCAUGHT EXCEPTION! 💥 Shutting down...');
    console.log(err.name, err.message);
    process.exit(1);
  });
}

/**
 * Async error wrapper
 */
function catchAsync(fn) {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
}

/**
 * Log error for monitoring
 */
function logError(error, req = null) {
  const { formatErrorForLogging } = require('./responseHelper');
  const errorLog = formatErrorForLogging(error, req);
  
  // In production, you might want to send this to a logging service
  console.error('Error logged:', JSON.stringify(errorLog, null, 2));
  
  // TODO: Integrate with logging service (e.g., Winston, Sentry)
}

module.exports = {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  globalErrorHandler,
  handleUnhandledRejection,
  handleUncaughtException,
  catchAsync,
  logError
};
