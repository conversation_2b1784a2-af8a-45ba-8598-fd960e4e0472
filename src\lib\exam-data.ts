import { Exam, CreateExamRequest, UpdateExamRequest } from '@/types/exam'

// Mock data - In real app, this would come from your API
const mockExams: Exam[] = [
  {
    id: '1',
    name: 'JEE Main',
    description: 'Joint Entrance Examination (Main) for engineering admissions',
    category: 'JEE',
    level: 'INTERMEDIATE',
    duration: 180,
    totalMarks: 300,
    passingMarks: 90,
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    coursesCount: 8
  },
  {
    id: '2',
    name: 'JEE Advanced',
    description: 'Joint Entrance Examination (Advanced) for IIT admissions',
    category: 'JEE',
    level: 'ADVANCED',
    duration: 180,
    totalMarks: 372,
    passingMarks: 120,
    isActive: true,
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    coursesCount: 5
  },
  {
    id: '3',
    name: 'NEET UG',
    description: 'National Eligibility cum Entrance Test for medical admissions',
    category: 'NEET',
    level: 'INTERMEDIATE',
    duration: 200,
    totalMarks: 720,
    passingMarks: 400,
    isActive: true,
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    coursesCount: 6
  },
  {
    id: '4',
    name: 'Class 12 CBSE',
    description: 'Central Board of Secondary Education Class 12',
    category: 'CBSE',
    level: 'INTERMEDIATE',
    isActive: true,
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
    coursesCount: 12
  },
  {
    id: '5',
    name: 'Class 11 CBSE',
    description: 'Central Board of Secondary Education Class 11',
    category: 'CBSE',
    level: 'BEGINNER',
    isActive: true,
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    coursesCount: 10
  }
]

export const getExams = (): Promise<Exam[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve([...mockExams]), 100)
  })
}

export const getExamById = (id: string): Promise<Exam | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const exam = mockExams.find(e => e.id === id) || null
      resolve(exam)
    }, 100)
  })
}

export const createExam = (data: CreateExamRequest): Promise<Exam> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newExam: Exam = {
        id: Date.now().toString(),
        ...data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        coursesCount: 0
      }
      mockExams.push(newExam)
      resolve(newExam)
    }, 200)
  })
}

export const updateExam = (data: UpdateExamRequest): Promise<Exam> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockExams.findIndex(e => e.id === data.id)
      if (index === -1) {
        reject(new Error('Exam not found'))
        return
      }
      
      mockExams[index] = {
        ...mockExams[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockExams[index])
    }, 200)
  })
}

export const deleteExam = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockExams.findIndex(e => e.id === id)
      if (index === -1) {
        reject(new Error('Exam not found'))
        return
      }
      
      mockExams.splice(index, 1)
      resolve()
    }, 200)
  })
}
