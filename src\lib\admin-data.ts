import { 
  AdminUser, 
  AdminRole, 
  Permission, 
  PermissionCategory,
  CreateAdminRequest, 
  UpdateAdminRequest, 
  AdminFilters, 
  AdminStats,
  AdminSession,
  AdminActivity,
  RolePermissionMatrix
} from '@/types/admin'

// Mock permission categories
const mockPermissionCategories: PermissionCategory[] = [
  {
    id: '1',
    name: 'dashboard',
    displayName: 'Dashboard',
    description: 'Dashboard and analytics access',
    icon: 'BarChart3',
    color: 'bg-blue-100 text-blue-800',
    order: 1
  },
  {
    id: '2',
    name: 'students',
    displayName: 'Student Management',
    description: 'Student data and enrollment management',
    icon: 'Users',
    color: 'bg-green-100 text-green-800',
    order: 2
  },
  {
    id: '3',
    name: 'courses',
    displayName: 'Course Management',
    description: 'Course creation and management',
    icon: 'BookOpen',
    color: 'bg-purple-100 text-purple-800',
    order: 3
  },
  {
    id: '4',
    name: 'payments',
    displayName: 'Payment Management',
    description: 'Financial transactions and billing',
    icon: 'CreditCard',
    color: 'bg-yellow-100 text-yellow-800',
    order: 4
  },
  {
    id: '5',
    name: 'system',
    displayName: 'System Administration',
    description: 'System settings and configuration',
    icon: 'Settings',
    color: 'bg-red-100 text-red-800',
    order: 5
  }
]

// Mock permissions
const mockPermissions: Permission[] = [
  // Dashboard permissions
  {
    id: '1',
    name: 'dashboard.view',
    displayName: 'View Dashboard',
    description: 'Access to main dashboard and analytics',
    category: mockPermissionCategories[0],
    resource: 'dashboard',
    actions: [{ id: '1', name: 'read', displayName: 'Read', description: 'View dashboard data', level: 'READ' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'dashboard.analytics',
    displayName: 'Advanced Analytics',
    description: 'Access to detailed analytics and reports',
    category: mockPermissionCategories[0],
    resource: 'dashboard',
    actions: [{ id: '2', name: 'analytics', displayName: 'Analytics', description: 'View advanced analytics', level: 'READ' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  // Student permissions
  {
    id: '3',
    name: 'students.read',
    displayName: 'View Students',
    description: 'View student profiles and data',
    category: mockPermissionCategories[1],
    resource: 'students',
    actions: [{ id: '3', name: 'read', displayName: 'Read', description: 'View student data', level: 'READ' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '4',
    name: 'students.write',
    displayName: 'Manage Students',
    description: 'Create and edit student profiles',
    category: mockPermissionCategories[1],
    resource: 'students',
    actions: [
      { id: '4', name: 'write', displayName: 'Write', description: 'Create and edit students', level: 'WRITE' },
      { id: '5', name: 'enroll', displayName: 'Enroll', description: 'Enroll students in courses', level: 'WRITE' }
    ],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '5',
    name: 'students.delete',
    displayName: 'Delete Students',
    description: 'Delete student profiles and data',
    category: mockPermissionCategories[1],
    resource: 'students',
    actions: [{ id: '6', name: 'delete', displayName: 'Delete', description: 'Delete student data', level: 'DELETE' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  // Course permissions
  {
    id: '6',
    name: 'courses.read',
    displayName: 'View Courses',
    description: 'View course information and content',
    category: mockPermissionCategories[2],
    resource: 'courses',
    actions: [{ id: '7', name: 'read', displayName: 'Read', description: 'View course data', level: 'READ' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '7',
    name: 'courses.write',
    displayName: 'Manage Courses',
    description: 'Create and edit courses',
    category: mockPermissionCategories[2],
    resource: 'courses',
    actions: [{ id: '8', name: 'write', displayName: 'Write', description: 'Create and edit courses', level: 'WRITE' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  // Payment permissions
  {
    id: '8',
    name: 'payments.read',
    displayName: 'View Payments',
    description: 'View payment transactions and reports',
    category: mockPermissionCategories[3],
    resource: 'payments',
    actions: [{ id: '9', name: 'read', displayName: 'Read', description: 'View payment data', level: 'READ' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '9',
    name: 'payments.process',
    displayName: 'Process Payments',
    description: 'Process refunds and manual payments',
    category: mockPermissionCategories[3],
    resource: 'payments',
    actions: [{ id: '10', name: 'process', displayName: 'Process', description: 'Process payments', level: 'WRITE' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  // System permissions
  {
    id: '10',
    name: 'system.admin',
    displayName: 'System Administration',
    description: 'Full system administration access',
    category: mockPermissionCategories[4],
    resource: 'system',
    actions: [{ id: '11', name: 'admin', displayName: 'Admin', description: 'Full system access', level: 'ADMIN' }],
    scope: 'GLOBAL',
    isActive: true,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

// Mock roles
const mockRoles: AdminRole[] = [
  {
    id: '1',
    name: 'super_admin',
    displayName: 'Super Administrator',
    description: 'Full system access with all permissions',
    level: 1,
    color: 'bg-red-100 text-red-800',
    permissions: mockPermissions.map(p => p.id),
    restrictions: [],
    isActive: true,
    isSystem: true,
    userCount: 1,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'system'
  },
  {
    id: '2',
    name: 'admin',
    displayName: 'Administrator',
    description: 'Administrative access with most permissions',
    level: 2,
    color: 'bg-orange-100 text-orange-800',
    permissions: ['1', '2', '3', '4', '6', '7', '8', '9'],
    restrictions: [],
    isActive: true,
    isSystem: true,
    userCount: 3,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'system'
  },
  {
    id: '3',
    name: 'manager',
    displayName: 'Manager',
    description: 'Management access with limited permissions',
    level: 3,
    color: 'bg-blue-100 text-blue-800',
    permissions: ['1', '3', '4', '6', '7', '8'],
    restrictions: [],
    isActive: true,
    isSystem: false,
    userCount: 5,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'admin'
  },
  {
    id: '4',
    name: 'staff',
    displayName: 'Staff',
    description: 'Basic staff access with read permissions',
    level: 4,
    color: 'bg-green-100 text-green-800',
    permissions: ['1', '3', '6', '8'],
    restrictions: [],
    isActive: true,
    isSystem: false,
    userCount: 8,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'admin'
  }
]

// Mock admin users
const mockAdminUsers: AdminUser[] = [
  {
    id: '1',
    username: 'superadmin',
    email: '<EMAIL>',
    firstName: 'Super',
    lastName: 'Admin',
    fullName: 'Super Admin',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    phone: '+91 9876543210',
    designation: 'System Administrator',
    department: 'IT',
    employeeId: 'EMP001',
    joiningDate: '2024-01-01',
    experience: 10,
    specializations: ['System Administration', 'Database Management', 'Security'],
    bio: 'Experienced system administrator with expertise in educational technology platforms.',
    role: mockRoles[0],
    permissions: mockPermissions,
    customPermissions: [],
    status: 'ACTIVE',
    isEmailVerified: true,
    isPhoneVerified: true,
    isTwoFactorEnabled: true,
    lastLogin: '2024-01-25T10:30:00Z',
    lastPasswordChange: '2024-01-01T00:00:00Z',
    loginAttempts: 0,
    totalLogins: 245,
    totalSessions: 189,
    averageSessionDuration: 120,
    lastActivity: '2024-01-25T14:45:00Z',
    preferences: {
      theme: 'LIGHT',
      language: 'en',
      timezone: 'Asia/Kolkata',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24H',
      dashboardLayout: 'DEFAULT',
      defaultPage: '/admin/dashboard',
      sidebarCollapsed: false,
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      notificationSound: true,
      sessionTimeout: 60,
      requirePasswordChange: false,
      allowMultipleSessions: true,
      itemsPerPage: 25,
      defaultSortOrder: 'DESC',
      showAdvancedFilters: true,
      showOnlineStatus: true,
      allowProfileView: true,
      shareAnalytics: true
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-25T10:30:00Z',
    createdBy: 'system',
    updatedBy: 'superadmin',
    analytics: {
      totalLogins: 245,
      uniqueLoginDays: 25,
      averageSessionDuration: 120,
      lastLoginStreak: 25,
      longestLoginStreak: 25,
      totalActions: 1250,
      actionsToday: 45,
      actionsThisWeek: 234,
      actionsThisMonth: 1250,
      mostUsedFeatures: [
        { feature: 'Dashboard', count: 245, lastUsed: '2024-01-25T14:45:00Z' },
        { feature: 'Students', count: 189, lastUsed: '2024-01-25T12:30:00Z' },
        { feature: 'Courses', count: 156, lastUsed: '2024-01-24T16:20:00Z' }
      ],
      averageResponseTime: 1.2,
      errorRate: 0.5,
      successRate: 99.5,
      activeHours: [
        { hour: 9, count: 45 },
        { hour: 10, count: 67 },
        { hour: 11, count: 89 },
        { hour: 14, count: 78 },
        { hour: 15, count: 56 }
      ],
      activeDays: [
        { day: 'Monday', count: 234 },
        { day: 'Tuesday', count: 189 },
        { day: 'Wednesday', count: 267 },
        { day: 'Thursday', count: 198 },
        { day: 'Friday', count: 156 }
      ]
    }
  },
  {
    id: '2',
    username: 'john.doe',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    fullName: 'John Doe',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    phone: '+91 9876543211',
    designation: 'Academic Manager',
    department: 'Academic',
    employeeId: 'EMP002',
    joiningDate: '2024-01-15',
    experience: 5,
    specializations: ['Course Management', 'Student Relations', 'Academic Planning'],
    bio: 'Academic manager with focus on course development and student success.',
    role: mockRoles[2],
    permissions: mockPermissions.filter(p => ['1', '3', '4', '6', '7', '8'].includes(p.id)),
    customPermissions: [],
    status: 'ACTIVE',
    isEmailVerified: true,
    isPhoneVerified: true,
    isTwoFactorEnabled: false,
    lastLogin: '2024-01-25T09:15:00Z',
    lastPasswordChange: '2024-01-15T00:00:00Z',
    loginAttempts: 0,
    totalLogins: 89,
    totalSessions: 67,
    averageSessionDuration: 95,
    lastActivity: '2024-01-25T13:20:00Z',
    preferences: {
      theme: 'LIGHT',
      language: 'en',
      timezone: 'Asia/Kolkata',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12H',
      dashboardLayout: 'COMPACT',
      defaultPage: '/admin/courses',
      sidebarCollapsed: false,
      emailNotifications: true,
      pushNotifications: false,
      smsNotifications: false,
      notificationSound: false,
      sessionTimeout: 45,
      requirePasswordChange: false,
      allowMultipleSessions: false,
      itemsPerPage: 20,
      defaultSortOrder: 'ASC',
      showAdvancedFilters: false,
      showOnlineStatus: true,
      allowProfileView: true,
      shareAnalytics: false
    },
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-25T09:15:00Z',
    createdBy: 'superadmin',
    updatedBy: 'john.doe',
    analytics: {
      totalLogins: 89,
      uniqueLoginDays: 10,
      averageSessionDuration: 95,
      lastLoginStreak: 10,
      longestLoginStreak: 10,
      totalActions: 456,
      actionsToday: 23,
      actionsThisWeek: 89,
      actionsThisMonth: 456,
      mostUsedFeatures: [
        { feature: 'Courses', count: 156, lastUsed: '2024-01-25T13:20:00Z' },
        { feature: 'Students', count: 89, lastUsed: '2024-01-25T11:45:00Z' },
        { feature: 'Dashboard', count: 67, lastUsed: '2024-01-25T09:15:00Z' }
      ],
      averageResponseTime: 1.8,
      errorRate: 1.2,
      successRate: 98.8,
      activeHours: [
        { hour: 9, count: 23 },
        { hour: 10, count: 34 },
        { hour: 11, count: 45 },
        { hour: 14, count: 32 },
        { hour: 15, count: 28 }
      ],
      activeDays: [
        { day: 'Monday', count: 89 },
        { day: 'Tuesday', count: 67 },
        { day: 'Wednesday', count: 78 },
        { day: 'Thursday', count: 56 },
        { day: 'Friday', count: 45 }
      ]
    }
  },
  {
    id: '3',
    username: 'jane.smith',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    fullName: 'Jane Smith',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=150&h=150&fit=crop&crop=face',
    phone: '+91 9876543212',
    designation: 'Finance Manager',
    department: 'Finance',
    employeeId: 'EMP003',
    joiningDate: '2024-01-10',
    experience: 7,
    specializations: ['Financial Management', 'Payment Processing', 'Reporting'],
    bio: 'Finance manager specializing in payment systems and financial reporting.',
    role: mockRoles[3],
    permissions: mockPermissions.filter(p => ['1', '3', '6', '8'].includes(p.id)),
    customPermissions: [
      {
        id: 'custom1',
        adminId: '3',
        permissionId: '9',
        granted: true,
        grantedBy: 'superadmin',
        grantedAt: '2024-01-20T10:00:00Z',
        reason: 'Special access for payment processing'
      }
    ],
    status: 'ACTIVE',
    isEmailVerified: true,
    isPhoneVerified: false,
    isTwoFactorEnabled: true,
    lastLogin: '2024-01-24T16:45:00Z',
    lastPasswordChange: '2024-01-10T00:00:00Z',
    loginAttempts: 0,
    totalLogins: 67,
    totalSessions: 45,
    averageSessionDuration: 110,
    lastActivity: '2024-01-24T18:30:00Z',
    preferences: {
      theme: 'DARK',
      language: 'en',
      timezone: 'Asia/Kolkata',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12H',
      dashboardLayout: 'DETAILED',
      defaultPage: '/admin/payments',
      sidebarCollapsed: true,
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: true,
      notificationSound: true,
      sessionTimeout: 30,
      requirePasswordChange: true,
      allowMultipleSessions: false,
      itemsPerPage: 50,
      defaultSortOrder: 'DESC',
      showAdvancedFilters: true,
      showOnlineStatus: false,
      allowProfileView: false,
      shareAnalytics: true
    },
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-24T16:45:00Z',
    createdBy: 'superadmin',
    updatedBy: 'jane.smith',
    analytics: {
      totalLogins: 67,
      uniqueLoginDays: 15,
      averageSessionDuration: 110,
      lastLoginStreak: 15,
      longestLoginStreak: 15,
      totalActions: 334,
      actionsToday: 0,
      actionsThisWeek: 67,
      actionsThisMonth: 334,
      mostUsedFeatures: [
        { feature: 'Payments', count: 189, lastUsed: '2024-01-24T18:30:00Z' },
        { feature: 'Dashboard', count: 67, lastUsed: '2024-01-24T16:45:00Z' },
        { feature: 'Students', count: 45, lastUsed: '2024-01-23T14:20:00Z' }
      ],
      averageResponseTime: 2.1,
      errorRate: 0.8,
      successRate: 99.2,
      activeHours: [
        { hour: 10, count: 15 },
        { hour: 11, count: 23 },
        { hour: 14, count: 18 },
        { hour: 15, count: 21 },
        { hour: 16, count: 19 }
      ],
      activeDays: [
        { day: 'Monday', count: 67 },
        { day: 'Tuesday', count: 45 },
        { day: 'Wednesday', count: 56 },
        { day: 'Thursday', count: 34 },
        { day: 'Friday', count: 23 }
      ]
    }
  }
]

export const getAdminUsers = (filters?: AdminFilters): Promise<AdminUser[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredUsers = [...mockAdminUsers]
      
      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredUsers = filteredUsers.filter(u => 
            u.firstName.toLowerCase().includes(searchLower) ||
            u.lastName.toLowerCase().includes(searchLower) ||
            u.email.toLowerCase().includes(searchLower) ||
            u.username.toLowerCase().includes(searchLower) ||
            u.designation.toLowerCase().includes(searchLower) ||
            u.department.toLowerCase().includes(searchLower)
          )
        }
        
        if (filters.role) {
          filteredUsers = filteredUsers.filter(u => u.role.id === filters.role)
        }
        
        if (filters.department) {
          filteredUsers = filteredUsers.filter(u => u.department === filters.department)
        }
        
        if (filters.status) {
          filteredUsers = filteredUsers.filter(u => u.status === filters.status)
        }
        
        if (filters.joiningDateFrom) {
          filteredUsers = filteredUsers.filter(u => 
            new Date(u.joiningDate) >= new Date(filters.joiningDateFrom!)
          )
        }
        
        if (filters.joiningDateTo) {
          filteredUsers = filteredUsers.filter(u => 
            new Date(u.joiningDate) <= new Date(filters.joiningDateTo!)
          )
        }
        
        if (filters.specializations && filters.specializations.length > 0) {
          filteredUsers = filteredUsers.filter(u => 
            filters.specializations!.some(spec => u.specializations.includes(spec))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredUsers.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredUsers)
    }, 100)
  })
}

export const getAdminUserById = (id: string): Promise<AdminUser | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const user = mockAdminUsers.find(u => u.id === id) || null
      resolve(user)
    }, 100)
  })
}

export const getAdminStats = (): Promise<AdminStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: AdminStats = {
        totalAdmins: mockAdminUsers.length,
        activeAdmins: mockAdminUsers.filter(u => u.status === 'ACTIVE').length,
        inactiveAdmins: mockAdminUsers.filter(u => u.status === 'INACTIVE').length,
        suspendedAdmins: mockAdminUsers.filter(u => u.status === 'SUSPENDED').length,
        pendingAdmins: mockAdminUsers.filter(u => u.status === 'PENDING').length,
        roleDistribution: mockRoles.map(role => ({
          roleId: role.id,
          roleName: role.displayName,
          count: role.userCount,
          percentage: (role.userCount / mockAdminUsers.length) * 100
        })),
        departmentDistribution: [
          { department: 'IT', count: 1, percentage: 33.3 },
          { department: 'Academic', count: 1, percentage: 33.3 },
          { department: 'Finance', count: 1, percentage: 33.3 }
        ],
        totalSessions: 301,
        activeSessions: 2,
        averageSessionDuration: 108,
        totalActions: 2040,
        actionsToday: 68,
        loginsToday: 2,
        loginsThisWeek: 15,
        loginsThisMonth: 401,
        uniqueLoginsToday: 2,
        failedLoginAttempts: 0,
        lockedAccounts: 0,
        twoFactorEnabled: 2,
        passwordResetRequests: 0,
        newAdminsThisMonth: 3,
        monthlyGrowthRate: 100,
        recentLogins: [
          { adminId: '1', adminName: 'Super Admin', loginTime: '2024-01-25T10:30:00Z', ipAddress: '*************', location: 'Mumbai, India' },
          { adminId: '2', adminName: 'John Doe', loginTime: '2024-01-25T09:15:00Z', ipAddress: '*************', location: 'Delhi, India' }
        ],
        mostActiveAdmins: [
          { adminId: '1', adminName: 'Super Admin', totalActions: 1250, lastActivity: '2024-01-25T14:45:00Z' },
          { adminId: '2', adminName: 'John Doe', totalActions: 456, lastActivity: '2024-01-25T13:20:00Z' },
          { adminId: '3', adminName: 'Jane Smith', totalActions: 334, lastActivity: '2024-01-24T18:30:00Z' }
        ]
      }
      resolve(stats)
    }, 100)
  })
}

export const getAdminRoles = (): Promise<AdminRole[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockRoles)
    }, 100)
  })
}

export const getPermissions = (): Promise<Permission[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockPermissions)
    }, 100)
  })
}

export const getPermissionCategories = (): Promise<PermissionCategory[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockPermissionCategories)
    }, 100)
  })
}

export const createAdminUser = (data: CreateAdminRequest): Promise<AdminUser> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const role = mockRoles.find(r => r.id === data.roleId) || mockRoles[3]
      const permissions = mockPermissions.filter(p => role.permissions.includes(p.id))
      
      const newUser: AdminUser = {
        id: Date.now().toString(),
        username: data.username,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        fullName: `${data.firstName} ${data.lastName}`,
        phone: data.phone,
        designation: data.designation,
        department: data.department,
        employeeId: data.employeeId,
        joiningDate: data.joiningDate,
        experience: 0,
        specializations: data.specializations || [],
        bio: data.bio,
        role,
        permissions,
        customPermissions: [],
        status: 'ACTIVE',
        isEmailVerified: false,
        isPhoneVerified: false,
        isTwoFactorEnabled: false,
        lastPasswordChange: new Date().toISOString(),
        loginAttempts: 0,
        totalLogins: 0,
        totalSessions: 0,
        averageSessionDuration: 0,
        preferences: {
          theme: 'LIGHT',
          language: 'en',
          timezone: 'Asia/Kolkata',
          dateFormat: 'DD/MM/YYYY',
          timeFormat: '24H',
          dashboardLayout: 'DEFAULT',
          defaultPage: '/admin/dashboard',
          sidebarCollapsed: false,
          emailNotifications: true,
          pushNotifications: false,
          smsNotifications: false,
          notificationSound: true,
          sessionTimeout: 60,
          requirePasswordChange: true,
          allowMultipleSessions: false,
          itemsPerPage: 25,
          defaultSortOrder: 'DESC',
          showAdvancedFilters: false,
          showOnlineStatus: true,
          allowProfileView: true,
          shareAnalytics: false
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin',
        updatedBy: 'admin',
        analytics: {
          totalLogins: 0,
          uniqueLoginDays: 0,
          averageSessionDuration: 0,
          lastLoginStreak: 0,
          longestLoginStreak: 0,
          totalActions: 0,
          actionsToday: 0,
          actionsThisWeek: 0,
          actionsThisMonth: 0,
          mostUsedFeatures: [],
          averageResponseTime: 0,
          errorRate: 0,
          successRate: 100,
          activeHours: [],
          activeDays: []
        }
      }
      mockAdminUsers.push(newUser)
      resolve(newUser)
    }, 200)
  })
}

export const updateAdminUser = (data: UpdateAdminRequest): Promise<AdminUser> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockAdminUsers.findIndex(u => u.id === data.id)
      if (index === -1) {
        reject(new Error('Admin user not found'))
        return
      }
      
      mockAdminUsers[index] = {
        ...mockAdminUsers[index],
        ...data,
        fullName: data.firstName && data.lastName ? `${data.firstName} ${data.lastName}` : mockAdminUsers[index].fullName,
        updatedAt: new Date().toISOString(),
        updatedBy: 'admin'
      }
      resolve(mockAdminUsers[index])
    }, 200)
  })
}

export const deleteAdminUser = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockAdminUsers.findIndex(u => u.id === id)
      if (index === -1) {
        reject(new Error('Admin user not found'))
        return
      }
      
      mockAdminUsers.splice(index, 1)
      resolve()
    }, 200)
  })
}
