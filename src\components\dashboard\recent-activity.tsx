'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { UserPlus, CreditCard, CheckCircle, FileText } from 'lucide-react'
import { RecentActivity } from '@/types/dashboard'

interface RecentActivityProps {
  activities: RecentActivity[]
}

const getActivityIcon = (type: RecentActivity['type']) => {
  switch (type) {
    case 'enrollment':
      return UserPlus
    case 'payment':
      return CreditCard
    case 'completion':
      return CheckCircle
    case 'test':
      return FileText
    default:
      return FileText
  }
}

const getActivityColor = (type: RecentActivity['type']) => {
  switch (type) {
    case 'enrollment':
      return 'text-blue-600 bg-blue-50'
    case 'payment':
      return 'text-green-600 bg-green-50'
    case 'completion':
      return 'text-purple-600 bg-purple-50'
    case 'test':
      return 'text-orange-600 bg-orange-50'
    default:
      return 'text-gray-600 bg-gray-50'
  }
}

export function RecentActivity({ activities }: RecentActivityProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">Recent Activity</CardTitle>
        <p className="text-sm text-gray-500">Latest student activities and system events</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = getActivityIcon(activity.type)
            const colorClasses = getActivityColor(activity.type)
            
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${colorClasses}`}>
                  <Icon className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs text-gray-500">{activity.user}</span>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-500">{activity.timestamp}</span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
