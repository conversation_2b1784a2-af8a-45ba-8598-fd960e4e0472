export interface LiveClass {
  id: string
  title: string
  description?: string
  
  // Academic Information
  examId?: string
  examName?: string
  courseId?: string
  courseName?: string
  subjectId?: string
  subjectName?: string
  
  // Instructor Information
  instructorId: string
  instructorName: string
  instructorEmail: string
  instructorPhone?: string
  instructorBio?: string
  instructorImage?: string
  
  // Schedule Information
  scheduledDate: string
  startTime: string
  endTime: string
  duration: number // in minutes
  timezone: string
  
  // Class Configuration
  type: 'LIVE' | 'RECORDED' | 'HYBRID'
  mode: 'ONLINE' | 'OFFLINE' | 'HYBRID'
  platform: 'ZOOM' | 'GOOGLE_MEET' | 'MICROSOFT_TEAMS' | 'CUSTOM' | 'OFFLINE'
  maxParticipants?: number
  currentParticipants: number
  
  // Content Information
  topics: string[]
  prerequisites?: string[]
  materials?: string[]
  agenda?: string
  
  // Access & Pricing
  isPublic: boolean
  isFree: boolean
  price?: number
  currency?: 'INR' | 'USD' | 'EUR'
  accessLevel: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE'
  
  // Meeting Details
  meetingId?: string
  meetingPassword?: string
  meetingUrl?: string
  recordingUrl?: string
  
  // Status & State
  status: 'SCHEDULED' | 'LIVE' | 'COMPLETED' | 'CANCELLED' | 'POSTPONED'
  isRecurring: boolean
  recurrencePattern?: RecurrencePattern
  
  // Engagement
  registrations: LiveClassRegistration[]
  totalRegistrations: number
  attendanceCount: number
  averageRating?: number
  totalRatings: number
  
  // Notifications
  reminderSent: boolean
  followUpSent: boolean
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  tags: string[]
  notes?: string
}

export interface RecurrencePattern {
  type: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM'
  interval: number // every N days/weeks/months
  daysOfWeek?: number[] // 0-6, Sunday = 0
  endDate?: string
  occurrences?: number
}

export interface LiveClassRegistration {
  id: string
  classId: string
  studentId: string
  studentName: string
  studentEmail: string
  studentPhone?: string
  
  // Registration Details
  registeredAt: string
  registrationSource: 'WEBSITE' | 'MOBILE_APP' | 'ADMIN' | 'BULK_IMPORT'
  
  // Payment Information
  paymentStatus: 'PAID' | 'PENDING' | 'FAILED' | 'FREE' | 'REFUNDED'
  paymentId?: string
  amountPaid?: number
  
  // Attendance
  attended: boolean
  joinedAt?: string
  leftAt?: string
  attendanceDuration?: number // in minutes
  
  // Engagement
  rating?: number
  feedback?: string
  
  // Status
  status: 'REGISTERED' | 'CONFIRMED' | 'CANCELLED' | 'NO_SHOW' | 'ATTENDED'
  cancellationReason?: string
  
  // Notifications
  reminderSent: boolean
  confirmationSent: boolean
}

export interface LiveClassSession {
  id: string
  classId: string
  
  // Session Details
  actualStartTime?: string
  actualEndTime?: string
  actualDuration?: number
  
  // Technical Details
  meetingId: string
  recordingUrl?: string
  recordingDuration?: number
  recordingSize?: number // in MB
  
  // Attendance
  attendees: SessionAttendee[]
  maxConcurrentAttendees: number
  averageAttendanceDuration: number
  
  // Engagement Metrics
  totalQuestions: number
  totalMessages: number
  pollsCreated: number
  
  // Technical Metrics
  connectionIssues: number
  averageConnectionQuality: number
  
  // Content
  materials: SessionMaterial[]
  
  // Status
  status: 'SCHEDULED' | 'STARTED' | 'ENDED' | 'CANCELLED'
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface SessionAttendee {
  studentId: string
  studentName: string
  joinedAt: string
  leftAt?: string
  duration: number
  connectionQuality: 'POOR' | 'FAIR' | 'GOOD' | 'EXCELLENT'
  deviceType: 'DESKTOP' | 'MOBILE' | 'TABLET'
  questionsAsked: number
  messagesPosted: number
}

export interface SessionMaterial {
  id: string
  type: 'PDF' | 'VIDEO' | 'IMAGE' | 'LINK' | 'DOCUMENT'
  name: string
  url: string
  size?: number
  uploadedAt: string
  downloadCount: number
}

export interface CreateLiveClassRequest {
  title: string
  description?: string
  examId?: string
  courseId?: string
  subjectId?: string
  instructorId: string
  scheduledDate: string
  startTime: string
  endTime: string
  timezone?: string
  type: LiveClass['type']
  mode: LiveClass['mode']
  platform: LiveClass['platform']
  maxParticipants?: number
  topics: string[]
  prerequisites?: string[]
  materials?: string[]
  agenda?: string
  isPublic: boolean
  isFree: boolean
  price?: number
  currency?: LiveClass['currency']
  accessLevel: LiveClass['accessLevel']
  isRecurring: boolean
  recurrencePattern?: RecurrencePattern
  tags?: string[]
  notes?: string
}

export interface UpdateLiveClassRequest extends Partial<CreateLiveClassRequest> {
  id: string
  status?: LiveClass['status']
  meetingId?: string
  meetingPassword?: string
  meetingUrl?: string
  recordingUrl?: string
}

export interface LiveClassFilters {
  search?: string
  instructorId?: string
  examId?: string
  courseId?: string
  subjectId?: string
  status?: LiveClass['status']
  type?: LiveClass['type']
  mode?: LiveClass['mode']
  platform?: LiveClass['platform']
  accessLevel?: LiveClass['accessLevel']
  isPublic?: boolean
  isFree?: boolean
  dateFrom?: string
  dateTo?: string
  tags?: string[]
  sortBy?: 'scheduledDate' | 'title' | 'instructorName' | 'totalRegistrations' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface LiveClassStats {
  totalClasses: number
  scheduledClasses: number
  liveClasses: number
  completedClasses: number
  cancelledClasses: number
  
  totalRegistrations: number
  totalAttendance: number
  averageAttendanceRate: number
  
  totalRevenue: number
  averageClassRevenue: number
  
  totalInstructors: number
  activeInstructors: number
  
  platformDistribution: {
    platform: string
    count: number
    percentage: number
  }[]
  
  popularTimeSlots: {
    timeSlot: string
    count: number
    attendanceRate: number
  }[]
  
  subjectDistribution: {
    subjectId: string
    subjectName: string
    classCount: number
    registrations: number
    attendanceRate: number
  }[]
  
  monthlyTrends: {
    month: string
    classes: number
    registrations: number
    attendance: number
    revenue: number
  }[]
  
  instructorPerformance: {
    instructorId: string
    instructorName: string
    classesCount: number
    totalRegistrations: number
    averageRating: number
    attendanceRate: number
  }[]
}

export interface LiveClassCalendar {
  date: string
  classes: LiveClass[]
  totalClasses: number
  totalRegistrations: number
}

export interface Instructor {
  id: string
  name: string
  email: string
  phone?: string
  bio?: string
  image?: string
  specializations: string[]
  experience: number // in years
  rating: number
  totalClasses: number
  totalStudents: number
  isActive: boolean
  availability: InstructorAvailability[]
  createdAt: string
  updatedAt: string
}

export interface InstructorAvailability {
  dayOfWeek: number // 0-6, Sunday = 0
  startTime: string // HH:MM format
  endTime: string // HH:MM format
  timezone: string
}

export interface LiveClassNotification {
  id: string
  classId: string
  type: 'REMINDER' | 'CONFIRMATION' | 'CANCELLATION' | 'RESCHEDULE' | 'FOLLOW_UP'
  recipientType: 'STUDENT' | 'INSTRUCTOR' | 'ALL'
  recipients: string[] // student/instructor IDs
  subject: string
  message: string
  scheduledAt: string
  sentAt?: string
  status: 'SCHEDULED' | 'SENT' | 'FAILED' | 'CANCELLED'
  createdAt: string
}

export interface LiveClassAnalytics {
  classId: string
  
  // Registration Analytics
  registrationTrends: {
    date: string
    registrations: number
    cancellations: number
  }[]
  
  // Attendance Analytics
  attendanceByTimeSlot: {
    timeSlot: string
    attendees: number
  }[]
  
  // Engagement Analytics
  engagementMetrics: {
    averageAttendanceDuration: number
    questionsAsked: number
    messagesPosted: number
    pollParticipation: number
  }
  
  // Geographic Analytics
  attendeesByLocation: {
    city: string
    state: string
    count: number
  }[]
  
  // Device Analytics
  deviceDistribution: {
    device: string
    count: number
    percentage: number
  }[]
  
  // Feedback Analytics
  feedbackSummary: {
    averageRating: number
    totalFeedbacks: number
    ratingDistribution: {
      rating: number
      count: number
    }[]
  }
}
