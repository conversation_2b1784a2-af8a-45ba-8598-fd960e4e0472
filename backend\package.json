{"name": "utk<PERSON>hta-admin-backend", "version": "1.0.0", "description": "Backend API for Utkrishta Coaching Business Admin Panel", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "build": "echo 'No build step required for Node.js'", "docker:build": "docker build -t utkrishta-admin-backend .", "docker:run": "docker run -p 5000:5000 utkrishta-admin-backend"}, "keywords": ["coaching", "education", "admin-panel", "api", "dashboard", "analytics", "nodejs", "express"], "author": "Utkrishta Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-validator": "^7.0.1", "express-rate-limit": "^6.10.0", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "mysql2": "^3.6.0", "redis": "^4.6.7", "ws": "^8.13.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "moment-timezone": "^0.5.43", "uuid": "^9.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.4", "nodemailer": "^6.9.4", "twilio": "^4.14.0", "axios": "^1.5.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "exceljs": "^4.3.0", "pdfkit": "^0.13.0", "joi": "^17.9.2", "lodash": "^4.17.21", "cron": "^2.4.4", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.46.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.0", "prettier": "^3.0.1", "husky": "^8.0.3", "lint-staged": "^13.2.3", "@types/node": "^20.4.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/utkrishta/admin-backend.git"}, "bugs": {"url": "https://github.com/utkrishta/admin-backend/issues"}, "homepage": "https://github.com/utkrishta/admin-backend#readme", "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/**", "!src/migrations/**", "!src/seeds/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "lint-staged": {"src/**/*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}}