const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const WebSocket = require('ws');
const http = require('http');
const url = require('url');

// Import utilities and middleware
const { globalErrorHandler, handleUnhandledRejection, handleUncaughtException } = require('./utils/errorHandler');
const cacheService = require('./services/cacheService');
const realTimeService = require('./services/realTimeService');

// Import routes
const dashboardRoutes = require('./routes/dashboardRoutes');

// Handle uncaught exceptions and unhandled rejections
handleUncaughtException();
handleUnhandledRejection();

/**
 * Express Application Setup
 */
const app = express();
const server = http.createServer(app);

// Make server globally available for graceful shutdown
global.server = server;

/**
 * Security Middleware
 */
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

/**
 * CORS Configuration
 */
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = (process.env.ALLOWED_ORIGINS || 'http://localhost:3000').split(',');
    
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

/**
 * Rate Limiting
 */
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 100 : 1000, // Limit each IP to 100 requests per windowMs in production
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.',
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

/**
 * General Middleware
 */
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * Logging Middleware
 */
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

/**
 * Health Check Endpoint
 */
app.get('/health', async (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: new Date().toISOString(),
    services: {
      database: 'connected', // TODO: Add actual database health check
      cache: cacheService.isAvailable() ? 'connected' : 'disconnected',
      realtime: realTimeService.isInitialized ? 'active' : 'inactive'
    }
  };

  try {
    // Add cache statistics if available
    if (cacheService.isAvailable()) {
      healthCheck.services.cache_stats = await cacheService.getStats();
    }

    // Add real-time service statistics
    if (realTimeService.isInitialized) {
      healthCheck.services.realtime_stats = realTimeService.getStats();
    }

    res.status(200).json(healthCheck);
  } catch (error) {
    healthCheck.message = 'Service Degraded';
    healthCheck.error = error.message;
    res.status(503).json(healthCheck);
  }
});

/**
 * API Routes
 */
app.use('/api/dashboard', dashboardRoutes);

/**
 * 404 Handler for API routes
 */
app.all('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  });
});

/**
 * Root endpoint
 */
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Utkrishta Admin Panel API',
    version: process.env.API_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      dashboard: '/api/dashboard',
      websocket: '/ws'
    }
  });
});

/**
 * Global Error Handler
 */
app.use(globalErrorHandler);

/**
 * WebSocket Server Setup
 */
const wss = new WebSocket.Server({
  server,
  path: '/ws',
  verifyClient: (info) => {
    // Basic verification - you might want to add more sophisticated auth
    const query = url.parse(info.req.url, true).query;
    return query.token ? true : false;
  }
});

/**
 * WebSocket Connection Handler
 */
wss.on('connection', async (ws, req) => {
  const query = url.parse(req.url, true).query;
  
  try {
    // Import dashboard controller for WebSocket handling
    const dashboardController = require('./controllers/dashboardController');
    await dashboardController.handleRealTimeConnection(ws, req);
  } catch (error) {
    console.error('WebSocket connection error:', error);
    ws.close(1011, 'Internal server error');
  }
});

/**
 * Initialize Services
 */
async function initializeServices() {
  try {
    // Initialize cache service
    await cacheService.initialize();
    console.log('Cache service initialized');

    // Initialize real-time service
    realTimeService.initialize();
    console.log('Real-time service initialized');

  } catch (error) {
    console.error('Error initializing services:', error);
    // Don't exit the process, allow the app to run with degraded functionality
  }
}

/**
 * Graceful Shutdown Handler
 */
function gracefulShutdown(signal) {
  console.log(`Received ${signal}. Starting graceful shutdown...`);
  
  server.close(async () => {
    console.log('HTTP server closed');
    
    try {
      // Close WebSocket server
      wss.close(() => {
        console.log('WebSocket server closed');
      });

      // Shutdown real-time service
      realTimeService.shutdown();

      // Close cache service
      await cacheService.close();
      console.log('Cache service closed');

      console.log('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      console.error('Error during graceful shutdown:', error);
      process.exit(1);
    }
  });

  // Force close after 30 seconds
  setTimeout(() => {
    console.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
}

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

/**
 * Start Server
 */
const PORT = process.env.PORT || 5000;

server.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
  console.log(`📊 Dashboard API: http://localhost:${PORT}/api/dashboard`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}/ws`);
  console.log(`❤️  Health Check: http://localhost:${PORT}/health`);
  
  // Initialize services after server starts
  await initializeServices();
});

module.exports = app;
