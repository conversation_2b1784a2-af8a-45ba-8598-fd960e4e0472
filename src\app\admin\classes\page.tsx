'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Play, 
  Clock, 
  Eye, 
  FileText, 
  Video, 
  TestTube,
  TrendingUp,
  BookOpen
} from 'lucide-react'
import { Class, ClassFilters, ClassStats, CreateClassRequest } from '@/types/class'
import { Subject } from '@/types/subject'
import { Course } from '@/types/course'
import { Exam } from '@/types/exam'
import { getClasses, deleteClass, getClassStats, createClass } from '@/lib/class-data'
import { getSubjects } from '@/lib/subject-data'
import { getCourses } from '@/lib/course-data'
import { getExams } from '@/lib/exam-data'
import { AddClassModal } from '@/components/classes/add-class-modal'

export default function ClassesPage() {
  const [classes, setClasses] = useState<Class[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [stats, setStats] = useState<ClassStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [filters, setFilters] = useState<ClassFilters>({
    search: '',
    sortBy: 'order',
    sortOrder: 'asc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadClasses()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [classesData, subjectsData, coursesData, examsData, statsData] = await Promise.all([
        getClasses(),
        getSubjects(),
        getCourses(),
        getExams(),
        getClassStats()
      ])
      setClasses(classesData)
      setSubjects(subjectsData)
      setCourses(coursesData)
      setExams(examsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadClasses = async () => {
    try {
      const data = await getClasses(filters)
      setClasses(data)
    } catch (error) {
      console.error('Failed to load classes:', error)
    }
  }

  const handleDeleteClass = async (id: string) => {
    if (confirm('Are you sure you want to delete this class?')) {
      try {
        await deleteClass(id)
        await loadClasses()
        const statsData = await getClassStats()
        setStats(statsData)
      } catch (error) {
        console.error('Failed to delete class:', error)
      }
    }
  }

  const handleAddClass = async (data: CreateClassRequest) => {
    try {
      await createClass(data)
      await loadClasses()
      const statsData = await getClassStats()
      setStats(statsData)
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add class:', error)
      throw error
    }
  }

  const handleFilterChange = (key: keyof ClassFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading classes...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Class Management</h1>
          <p className="text-gray-600 mt-1">Manage video classes, PDFs, tests, and content visibility</p>
        </div>
        <Button
          className="flex items-center space-x-2"
          onClick={() => setShowAddModal(true)}
        >
          <Plus className="w-4 h-4" />
          <span>Add New Class</span>
        </Button>
      </div>

      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalClasses}</div>
              <p className="text-xs text-muted-foreground">All classes</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.publishedClasses}</div>
              <p className="text-xs text-muted-foreground">Live classes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Duration</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatDuration(stats.totalDuration)}</div>
              <p className="text-xs text-muted-foreground">Content time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">All time views</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Completion</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(stats.averageCompletion)}%</div>
              <p className="text-xs text-muted-foreground">Completion rate</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search classes by title, description, or subject..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.subjectId || 'all'} onValueChange={(value) => handleFilterChange('subjectId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by Subject" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Subjects</SelectItem>
                {subjects.map((subject) => (
                  <SelectItem key={subject.id} value={subject.id}>
                    {subject.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.isPublished?.toString() || 'all'} onValueChange={(value) => handleFilterChange('isPublished', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Published</SelectItem>
                <SelectItem value="false">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Class Details</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Content</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Views</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {classes.map((classItem) => (
                <TableRow key={classItem.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">#{classItem.order}</Badge>
                        <div className="font-medium">{classItem.title}</div>
                      </div>
                      <div className="text-sm text-gray-500">{classItem.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium text-sm">{classItem.subjectName}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {classItem.videoUrl && (
                        <div className="flex items-center space-x-1 text-blue-600">
                          <Video className="w-3 h-3" />
                          <span className="text-xs">Video</span>
                        </div>
                      )}
                      {classItem.pdfUrl && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <FileText className="w-3 h-3" />
                          <span className="text-xs">PDF</span>
                        </div>
                      )}
                      {classItem.testId && (
                        <div className="flex items-center space-x-1 text-green-600">
                          <TestTube className="w-3 h-3" />
                          <span className="text-xs">Test</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <span className="text-sm">{formatDuration(classItem.duration)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3 text-gray-400" />
                      <span className="text-sm">{classItem.viewCount.toLocaleString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={classItem.isPublished ? 'success' : 'warning'}>
                      {classItem.isPublished ? 'Published' : 'Draft'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClass(classItem.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {classes.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No classes found matching your filters.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Class Modal */}
      <AddClassModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddClass}
        courses={courses}
        subjects={subjects}
        exams={exams}
      />
    </div>
  )
}
