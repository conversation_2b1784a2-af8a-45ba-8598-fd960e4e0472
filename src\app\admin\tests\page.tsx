'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Play, 
  Eye, 
  TestTube, 
  Clock,
  Users,
  Target,
  TrendingUp,
  BookOpen,
  Award,
  BarChart3,
  Settings,
  Copy,
  Download
} from 'lucide-react'
import { Test, TestFilters, TestStats, CreateTestRequest } from '@/types/test'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'
import { Exam } from '@/types/exam'
import { getTests, deleteTest, getTestStats, createTest } from '@/lib/test-data'
import { getCourses } from '@/lib/course-data'
import { getSubjects } from '@/lib/subject-data'
import { getExams } from '@/lib/exam-data'

export default function TestsPage() {
  const [tests, setTests] = useState<Test[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [stats, setStats] = useState<TestStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [filters, setFilters] = useState<TestFilters>({
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadTests()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [testsData, coursesData, subjectsData, examsData, statsData] = await Promise.all([
        getTests(),
        getCourses(),
        getSubjects(),
        getExams(),
        getTestStats()
      ])
      setTests(testsData)
      setCourses(coursesData)
      setSubjects(subjectsData)
      setExams(examsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTests = async () => {
    try {
      const data = await getTests(filters)
      setTests(data)
    } catch (error) {
      console.error('Failed to load tests:', error)
    }
  }

  const handleDeleteTest = async (id: string) => {
    if (confirm('Are you sure you want to delete this test? This action cannot be undone.')) {
      try {
        await deleteTest(id)
        await loadTests()
        // Reload stats
        const statsData = await getTestStats()
        setStats(statsData)
      } catch (error) {
        console.error('Failed to delete test:', error)
      }
    }
  }

  const handleFilterChange = (key: keyof TestFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleAddTest = async (data: CreateTestRequest) => {
    try {
      await createTest(data)
      await loadTests()
      // Reload stats
      const statsData = await getTestStats()
      setStats(statsData)
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add test:', error)
      throw error
    }
  }

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PRACTICE': return 'bg-blue-100 text-blue-800'
      case 'MOCK': return 'bg-red-100 text-red-800'
      case 'CHAPTER': return 'bg-green-100 text-green-800'
      case 'FULL_LENGTH': return 'bg-purple-100 text-purple-800'
      case 'QUICK': return 'bg-yellow-100 text-yellow-800'
      case 'REVISION': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'HARD': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading tests...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Test Management</h1>
          <p className="text-gray-600 mt-1">Create, manage, and analyze tests and assessments</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center space-x-2">
            <Copy className="w-4 h-4" />
            <span>Duplicate Test</span>
          </Button>
          <Button 
            className="flex items-center space-x-2"
            onClick={() => setShowAddModal(true)}
          >
            <Plus className="w-4 h-4" />
            <span>Create Test</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-7 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
              <TestTube className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTests}</div>
              <p className="text-xs text-muted-foreground">All tests</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.publishedTests}</div>
              <p className="text-xs text-muted-foreground">Live tests</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeTests}</div>
              <p className="text-xs text-muted-foreground">Available now</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Questions</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalQuestions}</div>
              <p className="text-xs text-muted-foreground">Total questions</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attempts</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalAttempts.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Total attempts</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Score</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageScore.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Average score</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageCompletionRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Completion rate</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search tests by title, description, or creator..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.examId || 'all'} onValueChange={(value) => handleFilterChange('examId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by Exam" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Exams</SelectItem>
                {exams.map((exam) => (
                  <SelectItem key={exam.id} value={exam.id}>
                    {exam.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.type || 'all'} onValueChange={(value) => handleFilterChange('type', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Test Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="PRACTICE">Practice</SelectItem>
                <SelectItem value="MOCK">Mock Test</SelectItem>
                <SelectItem value="CHAPTER">Chapter Test</SelectItem>
                <SelectItem value="FULL_LENGTH">Full Length</SelectItem>
                <SelectItem value="QUICK">Quick Test</SelectItem>
                <SelectItem value="REVISION">Revision</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.difficulty || 'all'} onValueChange={(value) => handleFilterChange('difficulty', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="EASY">Easy</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="HARD">Hard</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.isPublished?.toString() || 'all'} onValueChange={(value) => handleFilterChange('isPublished', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Published</SelectItem>
                <SelectItem value="false">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Test Details</TableHead>
                <TableHead>Type & Difficulty</TableHead>
                <TableHead>Course/Subject</TableHead>
                <TableHead>Configuration</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tests.map((test) => (
                <TableRow key={test.id}>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <TestTube className="w-4 h-4 text-blue-600" />
                        <div className="font-medium">{test.title}</div>
                      </div>
                      <div className="text-sm text-gray-500 line-clamp-2">{test.description}</div>
                      <div className="flex items-center space-x-2">
                        {test.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {test.tags.length > 3 && (
                          <span className="text-xs text-gray-400">+{test.tags.length - 3} more</span>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">
                        By {test.createdBy} • {new Date(test.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getTypeColor(test.type)}>
                        {test.type.replace('_', ' ')}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <Badge className={getDifficultyColor(test.difficulty)} variant="outline">
                          {test.difficulty}
                        </Badge>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {test.examName && (
                        <div className="text-sm font-medium">{test.examName}</div>
                      )}
                      {test.courseName && (
                        <div className="text-xs text-gray-500 line-clamp-1">{test.courseName}</div>
                      )}
                      {test.subjectName && (
                        <div className="text-xs text-blue-600">{test.subjectName}</div>
                      )}
                      {test.className && (
                        <div className="text-xs text-purple-600">{test.className}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{formatDuration(test.duration)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BookOpen className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{test.totalQuestions} questions</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Target className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{test.totalMarks} marks</span>
                      </div>
                      {test.negativeMarking && (
                        <div className="text-xs text-red-600">
                          Negative: -{test.negativeMarkingRatio}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Users className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{test.totalAttempts}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Award className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{test.averageScore.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{test.completionRate.toFixed(1)}%</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Badge variant={test.isPublished ? 'success' : 'secondary'}>
                          {test.isPublished ? 'Published' : 'Draft'}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Badge variant={test.isActive ? 'success' : 'secondary'}>
                          {test.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      {test.allowRetake && (
                        <Badge variant="outline" className="text-xs">
                          Retake: {test.maxAttempts || '∞'}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" title="Preview Test">
                        <Play className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Analytics">
                        <BarChart3 className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Edit Test">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Settings">
                        <Settings className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        title="Delete Test"
                        onClick={() => handleDeleteTest(test.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {tests.length === 0 && (
            <div className="text-center py-8">
              <TestTube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No tests found matching your filters.</p>
              <Button onClick={() => setShowAddModal(true)}>
                Create your first test
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Copy className="w-4 h-4" />
              <span>Duplicate Test</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Download className="w-4 h-4" />
              <span>Export Results</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <BarChart3 className="w-4 h-4" />
              <span>Analytics Report</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <BookOpen className="w-4 h-4" />
              <span>Question Bank</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
