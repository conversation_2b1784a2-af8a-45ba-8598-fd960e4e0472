'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X, Plus, CheckCircle, AlertCircle, FileText, Palette, Hash } from 'lucide-react'
import { CreateSubjectRequest } from '@/types/subject'
import { Course } from '@/types/course'
import { Exam } from '@/types/exam'

interface AddSubjectModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (data: CreateSubjectRequest) => Promise<void>
  courses: Course[]
  exams: Exam[]
}

export function AddSubjectModal({ isOpen, onClose, onAdd, courses, exams }: AddSubjectModalProps) {
  const [formData, setFormData] = useState<Partial<CreateSubjectRequest>>({
    color: '#3B82F6',
    icon: '📚',
    order: 1,
    isActive: true
  })
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [error, setError] = useState<string>()

  const predefinedColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ]

  const predefinedIcons = [
    '📚', '🔬', '🧮', '🌍', '📖', '⚗️', '🎨', '🏛️', '💻', '🎵',
    '🏃‍♂️', '🌱', '🔭', '📊', '🧬', '⚡', '🌟', '🔥', '💡', '🎯'
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.description || !formData.courseId || !formData.examId) {
      setError('Please fill in all required fields')
      return
    }

    setLoading(true)
    setError(undefined)

    try {
      const subjectData: CreateSubjectRequest = {
        name: formData.name!,
        description: formData.description!,
        courseId: formData.courseId!,
        examId: formData.examId!,
        color: formData.color!,
        icon: formData.icon!,
        order: formData.order!,
        isActive: formData.isActive!
      }

      await onAdd(subjectData)
      setCompleted(true)
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add subject')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      color: '#3B82F6',
      icon: '📚',
      order: 1,
      isActive: true
    })
    setLoading(false)
    setCompleted(false)
    setError(undefined)
  }

  // Filter courses based on selected exam
  const filteredCourses = formData.examId 
    ? courses.filter(course => course.examId === formData.examId)
    : courses

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold flex items-center space-x-2">
            <FileText className="w-5 h-5 text-blue-600" />
            <span>Add New Subject</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Subject Added Successfully!</h3>
              <p className="text-gray-600">The subject has been created and is now available in the system.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Subject Name *
                    </label>
                    <Input
                      value={formData.name || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Mechanics"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Order *
                    </label>
                    <div className="relative">
                      <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        type="number"
                        value={formData.order || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, order: parseInt(e.target.value) || 1 }))}
                        placeholder="1"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.description || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter subject description..."
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exam *
                    </label>
                    <Select 
                      value={formData.examId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ 
                        ...prev, 
                        examId: value,
                        courseId: '' // Reset course when exam changes
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select exam" />
                      </SelectTrigger>
                      <SelectContent>
                        {exams.map((exam) => (
                          <SelectItem key={exam.id} value={exam.id}>
                            {exam.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Course *
                    </label>
                    <Select 
                      value={formData.courseId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, courseId: value }))}
                      disabled={!formData.examId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select course" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredCourses.map((course) => (
                          <SelectItem key={course.id} value={course.id}>
                            {course.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Visual Customization */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Visual Customization</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Color Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject Color *
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Palette className="w-4 h-4 text-gray-400" />
                        <Input
                          type="color"
                          value={formData.color}
                          onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                          className="w-16 h-8 p-1 border rounded"
                        />
                        <span className="text-sm text-gray-600">{formData.color}</span>
                      </div>
                      
                      <div className="grid grid-cols-5 gap-2">
                        {predefinedColors.map((color) => (
                          <button
                            key={color}
                            type="button"
                            className={`w-8 h-8 rounded border-2 ${
                              formData.color === color ? 'border-gray-800' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => setFormData(prev => ({ ...prev, color }))}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Icon Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject Icon *
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <div className="text-2xl">{formData.icon}</div>
                        <Input
                          value={formData.icon}
                          onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                          placeholder="📚"
                          className="w-20"
                        />
                      </div>
                      
                      <div className="grid grid-cols-10 gap-2">
                        {predefinedIcons.map((icon) => (
                          <button
                            key={icon}
                            type="button"
                            className={`w-8 h-8 text-lg flex items-center justify-center rounded border ${
                              formData.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                            }`}
                            onClick={() => setFormData(prev => ({ ...prev, icon }))}
                          >
                            {icon}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Preview */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preview
                  </label>
                  <div className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50">
                    <div 
                      className="w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold"
                      style={{ backgroundColor: formData.color }}
                    >
                      {formData.icon}
                    </div>
                    <div>
                      <div className="font-medium">{formData.name || 'Subject Name'}</div>
                      <div className="text-sm text-gray-500">{formData.description || 'Subject description'}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <label htmlFor="isActive" className="text-sm text-gray-700">
                  Make this subject active
                </label>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>{loading ? 'Adding...' : 'Add Subject'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
