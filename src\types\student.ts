export interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  dateOfBirth?: string
  gender?: 'MALE' | 'FEMALE' | 'OTHER'
  profileImage?: string
  
  // Academic Information
  targetExam?: string
  targetExamName?: string
  currentClass?: string
  school?: string
  city?: string
  state?: string
  country?: string
  
  // Account Information
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING'
  registrationDate: string
  lastLoginDate?: string
  emailVerified: boolean
  phoneVerified: boolean
  
  // Enrollment Information
  enrolledCourses: StudentEnrollment[]
  totalCoursesEnrolled: number
  activeCourses: number
  completedCourses: number
  
  // Performance Metrics
  overallProgress: number // percentage
  averageScore: number // percentage
  totalTestsAttempted: number
  totalTestsPassed: number
  totalStudyTime: number // in minutes
  streakDays: number
  lastActivityDate?: string
  
  // Subscription Information
  subscriptionType: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE'
  subscriptionStatus: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'TRIAL'
  subscriptionStartDate?: string
  subscriptionEndDate?: string
  
  // Communication Preferences
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  marketingEmails: boolean
  
  // Parent/Guardian Information (for minors)
  parentName?: string
  parentEmail?: string
  parentPhone?: string
  
  // Additional Information
  notes?: string
  tags: string[]
  createdAt: string
  updatedAt: string
  createdBy?: string
}

export interface StudentEnrollment {
  id: string
  studentId: string
  courseId: string
  courseName: string
  examId?: string
  examName?: string
  enrollmentDate: string
  completionDate?: string
  status: 'ENROLLED' | 'IN_PROGRESS' | 'COMPLETED' | 'DROPPED' | 'SUSPENDED'
  progress: number // percentage
  lastAccessedDate?: string
  
  // Performance in this course
  testsAttempted: number
  testsPassed: number
  averageScore: number
  totalStudyTime: number // in minutes
  
  // Payment Information
  paymentStatus: 'PAID' | 'PENDING' | 'FAILED' | 'REFUNDED' | 'FREE'
  amountPaid?: number
  paymentDate?: string
  
  // Access Information
  accessExpiryDate?: string
  isAccessActive: boolean
}

export interface StudentPerformance {
  studentId: string
  courseId?: string
  subjectId?: string
  
  // Test Performance
  totalTests: number
  testsAttempted: number
  testsPassed: number
  averageScore: number
  highestScore: number
  lowestScore: number
  
  // Time Analytics
  totalStudyTime: number // in minutes
  averageSessionTime: number // in minutes
  totalSessions: number
  
  // Progress Analytics
  overallProgress: number // percentage
  subjectWiseProgress: SubjectProgress[]
  
  // Engagement Analytics
  streakDays: number
  lastActivityDate: string
  averageWeeklyActivity: number // hours per week
  
  // Comparative Analytics
  classRank?: number
  percentile?: number
  
  // Improvement Metrics
  progressTrend: 'IMPROVING' | 'DECLINING' | 'STABLE'
  weakAreas: string[]
  strongAreas: string[]
  recommendations: string[]
}

export interface SubjectProgress {
  subjectId: string
  subjectName: string
  progress: number // percentage
  testsAttempted: number
  averageScore: number
  timeSpent: number // in minutes
  lastAccessedDate: string
}

export interface StudentActivity {
  id: string
  studentId: string
  type: 'LOGIN' | 'COURSE_ACCESS' | 'TEST_ATTEMPT' | 'VIDEO_WATCH' | 'PDF_DOWNLOAD' | 'LOGOUT'
  description: string
  courseId?: string
  courseName?: string
  subjectId?: string
  subjectName?: string
  classId?: string
  className?: string
  testId?: string
  testName?: string
  duration?: number // in minutes
  score?: number
  timestamp: string
  ipAddress?: string
  deviceInfo?: string
}

export interface CreateStudentRequest {
  firstName: string
  lastName: string
  email: string
  phone?: string
  dateOfBirth?: string
  gender?: Student['gender']
  targetExam?: string
  currentClass?: string
  school?: string
  city?: string
  state?: string
  country?: string
  subscriptionType: Student['subscriptionType']
  emailNotifications?: boolean
  smsNotifications?: boolean
  pushNotifications?: boolean
  parentName?: string
  parentEmail?: string
  parentPhone?: string
  notes?: string
  tags?: string[]
}

export interface UpdateStudentRequest extends Partial<CreateStudentRequest> {
  id: string
  status?: Student['status']
}

export interface StudentFilters {
  search?: string
  status?: Student['status']
  subscriptionType?: Student['subscriptionType']
  subscriptionStatus?: Student['subscriptionStatus']
  targetExam?: string
  city?: string
  state?: string
  registrationDateFrom?: string
  registrationDateTo?: string
  lastLoginFrom?: string
  lastLoginTo?: string
  tags?: string[]
  sortBy?: 'firstName' | 'lastName' | 'email' | 'registrationDate' | 'lastLoginDate' | 'overallProgress' | 'averageScore'
  sortOrder?: 'asc' | 'desc'
}

export interface StudentStats {
  totalStudents: number
  activeStudents: number
  newStudentsThisMonth: number
  totalEnrollments: number
  averageProgress: number
  averageScore: number
  totalStudyTime: number
  subscriptionDistribution: {
    free: number
    basic: number
    premium: number
    enterprise: number
  }
  statusDistribution: {
    active: number
    inactive: number
    suspended: number
    pending: number
  }
  examDistribution: {
    examId: string
    examName: string
    studentCount: number
  }[]
  cityDistribution: {
    city: string
    studentCount: number
  }[]
  monthlyRegistrations: {
    month: string
    count: number
  }[]
}

export interface StudentCommunication {
  id: string
  studentId: string
  type: 'EMAIL' | 'SMS' | 'PUSH' | 'IN_APP'
  subject: string
  message: string
  status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'
  sentAt: string
  deliveredAt?: string
  readAt?: string
  sentBy: string
}

export interface BulkStudentAction {
  action: 'ACTIVATE' | 'DEACTIVATE' | 'SUSPEND' | 'DELETE' | 'SEND_MESSAGE' | 'ENROLL_COURSE' | 'UPDATE_SUBSCRIPTION'
  studentIds: string[]
  data?: any
}

export interface StudentImport {
  id: string
  fileName: string
  totalRecords: number
  successfulImports: number
  failedImports: number
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED'
  errors: string[]
  importedAt: string
  importedBy: string
}
