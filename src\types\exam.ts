export interface Exam {
  id: string
  name: string
  description: string
  category: 'JEE' | 'NEET' | 'CBSE' | 'ICSE' | 'STATE_BOARD' | 'OTHER'
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED'
  duration?: number // in minutes
  totalMarks?: number
  passingMarks?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  coursesCount: number
}

export interface CreateExamRequest {
  name: string
  description: string
  category: Exam['category']
  level: Exam['level']
  duration?: number
  totalMarks?: number
  passingMarks?: number
  isActive: boolean
}

export interface UpdateExamRequest extends Partial<CreateExamRequest> {
  id: string
}
