const { AuthorizationError } = require('../utils/errorHandler');
const { errorResponse } = require('../utils/responseHelper');

/**
 * Role-based Access Control Middleware
 * Handles role and permission-based authorization
 */
class RoleMiddleware {
  /**
   * Require specific role(s)
   * @param {string|array} roles - Required role(s)
   * @returns {function} Express middleware function
   */
  requireRole(roles) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const userRole = req.user.role?.code;
        if (!userRole) {
          return errorResponse(res, 'User role not found', 403);
        }

        // Convert single role to array
        const requiredRoles = Array.isArray(roles) ? roles : [roles];

        // Check if user has any of the required roles
        if (!requiredRoles.includes(userRole)) {
          return errorResponse(res, 'Insufficient role permissions', 403);
        }

        next();
      } catch (error) {
        console.error('Role middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Require minimum role level
   * @param {number} minLevel - Minimum role level required
   * @returns {function} Express middleware function
   */
  requireMinRoleLevel(minLevel) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const userRoleLevel = req.user.role?.level;
        if (typeof userRoleLevel !== 'number') {
          return errorResponse(res, 'User role level not found', 403);
        }

        // Lower level numbers indicate higher privileges (1 = Super Admin, 2 = Admin, etc.)
        if (userRoleLevel > minLevel) {
          return errorResponse(res, 'Insufficient role level', 403);
        }

        next();
      } catch (error) {
        console.error('Role level middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Require specific permission(s)
   * @param {string|array} permissions - Required permission(s)
   * @returns {function} Express middleware function
   */
  requirePermission(permissions) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const userPermissions = req.user.permissions || [];
        const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];

        // Super admin has all permissions
        if (req.user.role?.code === 'super_admin') {
          return next();
        }

        // Check if user has all required permissions
        const hasAllPermissions = requiredPermissions.every(permission => 
          userPermissions.includes(permission)
        );

        if (!hasAllPermissions) {
          const missingPermissions = requiredPermissions.filter(permission => 
            !userPermissions.includes(permission)
          );
          
          return errorResponse(res, 'Missing required permissions', 403, {
            missing_permissions: missingPermissions
          });
        }

        next();
      } catch (error) {
        console.error('Permission middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Require any of the specified permissions
   * @param {array} permissions - Array of permissions (user needs at least one)
   * @returns {function} Express middleware function
   */
  requireAnyPermission(permissions) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const userPermissions = req.user.permissions || [];
        const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];

        // Super admin has all permissions
        if (req.user.role?.code === 'super_admin') {
          return next();
        }

        // Check if user has any of the required permissions
        const hasAnyPermission = requiredPermissions.some(permission => 
          userPermissions.includes(permission)
        );

        if (!hasAnyPermission) {
          return errorResponse(res, 'Insufficient permissions', 403, {
            required_permissions: requiredPermissions,
            user_permissions: userPermissions
          });
        }

        next();
      } catch (error) {
        console.error('Any permission middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Allow only super admin
   * @returns {function} Express middleware function
   */
  requireSuperAdmin() {
    return this.requireRole('super_admin');
  }

  /**
   * Allow admin or super admin
   * @returns {function} Express middleware function
   */
  requireAdmin() {
    return this.requireRole(['admin', 'super_admin']);
  }

  /**
   * Check if user owns the resource or has admin privileges
   * @param {string} userIdField - Field name containing the user ID in request params
   * @returns {function} Express middleware function
   */
  requireOwnershipOrAdmin(userIdField = 'userId') {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const resourceUserId = req.params[userIdField];
        const currentUserId = req.user.id;
        const userRole = req.user.role?.code;

        // Allow if user owns the resource
        if (resourceUserId === currentUserId) {
          return next();
        }

        // Allow if user is admin or super admin
        if (['admin', 'super_admin'].includes(userRole)) {
          return next();
        }

        return errorResponse(res, 'Access denied: insufficient privileges', 403);
      } catch (error) {
        console.error('Ownership middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Dynamic permission check based on request context
   * @param {function} permissionResolver - Function that returns required permission(s)
   * @returns {function} Express middleware function
   */
  requireDynamicPermission(permissionResolver) {
    return async (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const requiredPermissions = await permissionResolver(req);
        
        if (!requiredPermissions) {
          return next(); // No permissions required
        }

        // Use existing permission middleware
        return this.requirePermission(requiredPermissions)(req, res, next);
      } catch (error) {
        console.error('Dynamic permission middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Rate limiting based on role
   * @param {object} roleLimits - Object mapping role codes to rate limits
   * @returns {function} Express middleware function
   */
  roleBasedRateLimit(roleLimits) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const userRole = req.user.role?.code;
        const limit = roleLimits[userRole] || roleLimits.default;

        if (!limit) {
          return next(); // No limit specified
        }

        // Store limit info in request for rate limiting middleware
        req.roleBasedLimit = limit;
        next();
      } catch (error) {
        console.error('Role-based rate limit middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Conditional access based on user status
   * @param {array} allowedStatuses - Array of allowed user statuses
   * @returns {function} Express middleware function
   */
  requireUserStatus(allowedStatuses = ['active']) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const userStatus = req.user.status;
        
        if (!allowedStatuses.includes(userStatus)) {
          return errorResponse(res, `User status '${userStatus}' not allowed`, 403);
        }

        next();
      } catch (error) {
        console.error('User status middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Time-based access control
   * @param {object} timeRestrictions - Time restrictions object
   * @returns {function} Express middleware function
   */
  requireTimeAccess(timeRestrictions) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          return errorResponse(res, 'Authentication required', 401);
        }

        const now = new Date();
        const currentHour = now.getHours();
        const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Check allowed hours
        if (timeRestrictions.allowedHours) {
          const { start, end } = timeRestrictions.allowedHours;
          if (currentHour < start || currentHour >= end) {
            return errorResponse(res, 'Access not allowed at this time', 403);
          }
        }

        // Check allowed days
        if (timeRestrictions.allowedDays) {
          if (!timeRestrictions.allowedDays.includes(currentDay)) {
            return errorResponse(res, 'Access not allowed on this day', 403);
          }
        }

        next();
      } catch (error) {
        console.error('Time access middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Feature flag based access control
   * @param {string} featureFlag - Feature flag name
   * @returns {function} Express middleware function
   */
  requireFeatureFlag(featureFlag) {
    return (req, res, next) => {
      try {
        // This would typically check against a feature flag service
        const isFeatureEnabled = process.env[`FEATURE_${featureFlag.toUpperCase()}`] === 'true';
        
        if (!isFeatureEnabled) {
          return errorResponse(res, 'Feature not available', 403);
        }

        next();
      } catch (error) {
        console.error('Feature flag middleware error:', error);
        return errorResponse(res, 'Authorization failed', 403);
      }
    };
  }

  /**
   * Log access attempts for auditing
   * @returns {function} Express middleware function
   */
  logAccess() {
    return (req, res, next) => {
      try {
        const logData = {
          user_id: req.user?.id,
          username: req.user?.username,
          role: req.user?.role?.code,
          method: req.method,
          path: req.path,
          ip: req.ip,
          user_agent: req.get('User-Agent'),
          timestamp: new Date().toISOString()
        };

        // Log to console (in production, you'd want to use a proper logging service)
        console.log('Access Log:', JSON.stringify(logData));

        // TODO: Store in database or send to logging service
        
        next();
      } catch (error) {
        console.error('Access logging error:', error);
        next(); // Don't block request on logging error
      }
    };
  }
}

module.exports = new RoleMiddleware();
