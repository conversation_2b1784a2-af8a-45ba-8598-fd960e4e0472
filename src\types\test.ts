export interface Test {
  id: string
  title: string
  description?: string
  examId?: string
  examName?: string
  courseId?: string
  courseName?: string
  subjectId?: string
  subjectName?: string
  classId?: string
  className?: string
  type: 'PRACTICE' | 'MOCK' | 'CHAPTER' | 'FULL_LENGTH' | 'QUICK' | 'REVISION'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  duration: number // in minutes
  totalQuestions: number
  totalMarks: number
  passingMarks?: number
  negativeMarking: boolean
  negativeMarkingRatio?: number // e.g., 0.25 for -1/4
  instructions?: string
  questions: Question[]
  isPublished: boolean
  isActive: boolean
  allowRetake: boolean
  maxAttempts?: number
  showResults: boolean
  showCorrectAnswers: boolean
  randomizeQuestions: boolean
  randomizeOptions: boolean
  timePerQuestion?: number // in seconds
  createdBy: string
  createdAt: string
  updatedAt: string
  publishedAt?: string
  tags: string[]
  // Analytics
  totalAttempts: number
  averageScore: number
  averageTime: number // in minutes
  completionRate: number // percentage
}

export interface Question {
  id: string
  type: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'FILL_BLANK' | 'NUMERICAL' | 'MATCH_FOLLOWING'
  question: string
  explanation?: string
  marks: number
  negativeMarks?: number
  options?: QuestionOption[]
  correctAnswer: string | string[] | number
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  subject?: string
  topic?: string
  tags: string[]
  imageUrl?: string
  order: number
  // Analytics
  totalAttempts: number
  correctAttempts: number
  averageTime: number // in seconds
}

export interface QuestionOption {
  id: string
  text: string
  isCorrect: boolean
  imageUrl?: string
}

export interface TestAttempt {
  id: string
  testId: string
  userId: string
  userName: string
  startedAt: string
  submittedAt?: string
  duration: number // actual time taken in minutes
  score: number
  percentage: number
  totalQuestions: number
  correctAnswers: number
  wrongAnswers: number
  unanswered: number
  status: 'IN_PROGRESS' | 'COMPLETED' | 'ABANDONED' | 'TIME_UP'
  answers: TestAnswer[]
  rank?: number
  isPassed: boolean
}

export interface TestAnswer {
  questionId: string
  selectedAnswer: string | string[] | number | null
  isCorrect: boolean
  marksObtained: number
  timeSpent: number // in seconds
}

export interface CreateTestRequest {
  title: string
  description?: string
  examId?: string
  courseId?: string
  subjectId?: string
  classId?: string
  type: Test['type']
  difficulty: Test['difficulty']
  duration: number
  totalMarks: number
  passingMarks?: number
  negativeMarking: boolean
  negativeMarkingRatio?: number
  instructions?: string
  isPublished: boolean
  isActive: boolean
  allowRetake: boolean
  maxAttempts?: number
  showResults: boolean
  showCorrectAnswers: boolean
  randomizeQuestions: boolean
  randomizeOptions: boolean
  timePerQuestion?: number
  tags: string[]
}

export interface CreateQuestionRequest {
  type: Question['type']
  question: string
  explanation?: string
  marks: number
  negativeMarks?: number
  options?: Omit<QuestionOption, 'id'>[]
  correctAnswer: string | string[] | number
  difficulty: Question['difficulty']
  subject?: string
  topic?: string
  tags: string[]
  imageUrl?: string
  order: number
}

export interface UpdateTestRequest extends Partial<CreateTestRequest> {
  id: string
}

export interface UpdateQuestionRequest extends Partial<CreateQuestionRequest> {
  id: string
}

export interface TestFilters {
  examId?: string
  courseId?: string
  subjectId?: string
  classId?: string
  type?: Test['type']
  difficulty?: Test['difficulty']
  isPublished?: boolean
  isActive?: boolean
  search?: string
  tags?: string[]
  sortBy?: 'title' | 'createdAt' | 'totalAttempts' | 'averageScore' | 'duration'
  sortOrder?: 'asc' | 'desc'
}

export interface TestStats {
  totalTests: number
  publishedTests: number
  activeTests: number
  totalQuestions: number
  totalAttempts: number
  averageScore: number
  averageCompletionRate: number
}

export interface TestAnalytics {
  testId: string
  totalAttempts: number
  averageScore: number
  averageTime: number
  completionRate: number
  passRate: number
  difficultyDistribution: {
    easy: number
    medium: number
    hard: number
  }
  scoreDistribution: {
    range: string
    count: number
  }[]
  questionAnalytics: {
    questionId: string
    question: string
    correctRate: number
    averageTime: number
    difficulty: string
  }[]
  timeAnalytics: {
    averageTimePerQuestion: number
    fastestCompletion: number
    slowestCompletion: number
  }
}

export interface QuestionBank {
  id: string
  name: string
  description?: string
  examId?: string
  courseId?: string
  subjectId?: string
  totalQuestions: number
  questionsByDifficulty: {
    easy: number
    medium: number
    hard: number
  }
  questionsByType: {
    singleChoice: number
    multipleChoice: number
    trueFalse: number
    fillBlank: number
    numerical: number
    matchFollowing: number
  }
  createdBy: string
  createdAt: string
  updatedAt: string
  isActive: boolean
}
