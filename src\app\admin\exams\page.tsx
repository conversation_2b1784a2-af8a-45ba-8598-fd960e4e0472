'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Plus, Search, Edit, Trash2, BookOpen, Clock, Target } from 'lucide-react'
import { Exam, CreateExamRequest } from '@/types/exam'
import { getExams, deleteExam, createExam } from '@/lib/exam-data'
import { AddExamModal } from '@/components/exams/add-exam-modal'

export default function ExamsPage() {
  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredExams, setFilteredExams] = useState<Exam[]>([])
  const [showAddModal, setShowAddModal] = useState(false)

  useEffect(() => {
    loadExams()
  }, [])

  useEffect(() => {
    const filtered = exams.filter(exam =>
      exam.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredExams(filtered)
  }, [exams, searchTerm])

  const loadExams = async () => {
    try {
      setLoading(true)
      const data = await getExams()
      setExams(data)
    } catch (error) {
      console.error('Failed to load exams:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteExam = async (id: string) => {
    if (confirm('Are you sure you want to delete this exam?')) {
      try {
        await deleteExam(id)
        await loadExams()
      } catch (error) {
        console.error('Failed to delete exam:', error)
      }
    }
  }

  const handleAddExam = async (data: CreateExamRequest) => {
    try {
      await createExam(data)
      await loadExams()
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add exam:', error)
      throw error
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'JEE': return 'bg-blue-100 text-blue-800'
      case 'NEET': return 'bg-green-100 text-green-800'
      case 'CBSE': return 'bg-purple-100 text-purple-800'
      case 'ICSE': return 'bg-orange-100 text-orange-800'
      case 'STATE_BOARD': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'BEGINNER': return 'bg-green-100 text-green-800'
      case 'INTERMEDIATE': return 'bg-yellow-100 text-yellow-800'
      case 'ADVANCED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading exams...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Exam Management</h1>
          <p className="text-gray-600 mt-1">Manage all exams and their configurations</p>
        </div>
        <Button
          className="flex items-center space-x-2"
          onClick={() => setShowAddModal(true)}
        >
          <Plus className="w-4 h-4" />
          <span>Add New Exam</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{exams.length}</div>
            <p className="text-xs text-muted-foreground">Active examinations</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Exams</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{exams.filter(e => e.isActive).length}</div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{exams.reduce((sum, exam) => sum + exam.coursesCount, 0)}</div>
            <p className="text-xs text-muted-foreground">Across all exams</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(exams.filter(e => e.duration).reduce((sum, exam) => sum + (exam.duration || 0), 0) / exams.filter(e => e.duration).length || 0)} min
            </div>
            <p className="text-xs text-muted-foreground">Average exam time</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search exams by name, description, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Exam Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Courses</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredExams.map((exam) => (
                <TableRow key={exam.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{exam.name}</div>
                      <div className="text-sm text-gray-500 mt-1">{exam.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getCategoryColor(exam.category)}>
                      {exam.category}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getLevelColor(exam.level)}>
                      {exam.level}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {exam.duration ? `${exam.duration} min` : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{exam.coursesCount}</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant={exam.isActive ? 'success' : 'secondary'}>
                      {exam.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDeleteExam(exam.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredExams.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No exams found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Exam Modal */}
      <AddExamModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddExam}
      />
    </div>
  )
}
