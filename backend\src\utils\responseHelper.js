/**
 * Response Helper Utilities
 * Standardized response formats for API endpoints
 */

/**
 * Send success response
 * @param {object} res - Express response object
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {object} meta - Additional metadata
 * @param {number} statusCode - HTTP status code (default: 200)
 */
function successResponse(res, data = null, message = 'Success', meta = {}, statusCode = 200) {
  const response = {
    success: true,
    message,
    data,
    ...meta,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
}

/**
 * Send error response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code (default: 400)
 * @param {any} errors - Detailed error information
 * @param {object} meta - Additional metadata
 */
function errorResponse(res, message = 'An error occurred', statusCode = 400, errors = null, meta = {}) {
  const response = {
    success: false,
    message,
    errors,
    ...meta,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
}

/**
 * Send paginated response
 * @param {object} res - Express response object
 * @param {array} data - Array of data items
 * @param {object} pagination - Pagination information
 * @param {string} message - Success message
 * @param {object} meta - Additional metadata
 */
function paginatedResponse(res, data, pagination, message = 'Data retrieved successfully', meta = {}) {
  const response = {
    success: true,
    message,
    data,
    pagination: {
      current_page: pagination.currentPage || 1,
      per_page: pagination.perPage || 25,
      total: pagination.total || 0,
      total_pages: pagination.totalPages || 0,
      has_next: pagination.hasNext || false,
      has_prev: pagination.hasPrev || false
    },
    ...meta,
    timestamp: new Date().toISOString()
  };

  return res.status(200).json(response);
}

/**
 * Send validation error response
 * @param {object} res - Express response object
 * @param {array} validationErrors - Array of validation errors
 * @param {string} message - Error message
 */
function validationErrorResponse(res, validationErrors, message = 'Validation failed') {
  const formattedErrors = validationErrors.map(error => ({
    field: error.param || error.path,
    message: error.msg || error.message,
    value: error.value,
    location: error.location
  }));

  return errorResponse(res, message, 422, formattedErrors);
}

/**
 * Send not found response
 * @param {object} res - Express response object
 * @param {string} resource - Resource name that was not found
 * @param {string} message - Custom error message
 */
function notFoundResponse(res, resource = 'Resource', message = null) {
  const errorMessage = message || `${resource} not found`;
  return errorResponse(res, errorMessage, 404);
}

/**
 * Send unauthorized response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 */
function unauthorizedResponse(res, message = 'Unauthorized access') {
  return errorResponse(res, message, 401);
}

/**
 * Send forbidden response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 */
function forbiddenResponse(res, message = 'Access forbidden') {
  return errorResponse(res, message, 403);
}

/**
 * Send conflict response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 */
function conflictResponse(res, message = 'Resource conflict') {
  return errorResponse(res, message, 409);
}

/**
 * Send too many requests response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {number} retryAfter - Retry after seconds
 */
function tooManyRequestsResponse(res, message = 'Too many requests', retryAfter = 60) {
  res.set('Retry-After', retryAfter);
  return errorResponse(res, message, 429, null, { retry_after: retryAfter });
}

/**
 * Send internal server error response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {any} error - Error details (only in development)
 */
function internalServerErrorResponse(res, message = 'Internal server error', error = null) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const errorDetails = isDevelopment && error ? {
    stack: error.stack,
    details: error.message
  } : null;

  return errorResponse(res, message, 500, errorDetails);
}

/**
 * Send created response
 * @param {object} res - Express response object
 * @param {any} data - Created resource data
 * @param {string} message - Success message
 * @param {object} meta - Additional metadata
 */
function createdResponse(res, data, message = 'Resource created successfully', meta = {}) {
  return successResponse(res, data, message, meta, 201);
}

/**
 * Send accepted response
 * @param {object} res - Express response object
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {object} meta - Additional metadata
 */
function acceptedResponse(res, data = null, message = 'Request accepted', meta = {}) {
  return successResponse(res, data, message, meta, 202);
}

/**
 * Send no content response
 * @param {object} res - Express response object
 */
function noContentResponse(res) {
  return res.status(204).send();
}

/**
 * Send file download response
 * @param {object} res - Express response object
 * @param {string} filePath - Path to file
 * @param {string} fileName - Download filename
 * @param {string} contentType - MIME type
 */
function fileDownloadResponse(res, filePath, fileName, contentType = 'application/octet-stream') {
  res.setHeader('Content-Type', contentType);
  res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
  return res.download(filePath, fileName);
}

/**
 * Send streaming response
 * @param {object} res - Express response object
 * @param {stream} stream - Readable stream
 * @param {string} contentType - MIME type
 */
function streamResponse(res, stream, contentType = 'application/octet-stream') {
  res.setHeader('Content-Type', contentType);
  return stream.pipe(res);
}

/**
 * Send redirect response
 * @param {object} res - Express response object
 * @param {string} url - Redirect URL
 * @param {number} statusCode - HTTP status code (default: 302)
 */
function redirectResponse(res, url, statusCode = 302) {
  return res.redirect(statusCode, url);
}

/**
 * Send cache response with appropriate headers
 * @param {object} res - Express response object
 * @param {any} data - Response data
 * @param {number} maxAge - Cache max age in seconds
 * @param {string} etag - ETag value
 * @param {string} message - Success message
 */
function cacheResponse(res, data, maxAge = 300, etag = null, message = 'Success') {
  res.set('Cache-Control', `public, max-age=${maxAge}`);
  
  if (etag) {
    res.set('ETag', etag);
  }

  return successResponse(res, data, message);
}

/**
 * Check if request wants JSON response
 * @param {object} req - Express request object
 * @returns {boolean} True if JSON is preferred
 */
function wantsJSON(req) {
  return req.accepts(['html', 'json']) === 'json';
}

/**
 * Format error for logging
 * @param {Error} error - Error object
 * @param {object} req - Express request object
 * @returns {object} Formatted error object
 */
function formatErrorForLogging(error, req = null) {
  return {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    request: req ? {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      user: req.user ? { id: req.user.id, email: req.user.email } : null
    } : null
  };
}

module.exports = {
  successResponse,
  errorResponse,
  paginatedResponse,
  validationErrorResponse,
  notFoundResponse,
  unauthorizedResponse,
  forbiddenResponse,
  conflictResponse,
  tooManyRequestsResponse,
  internalServerErrorResponse,
  createdResponse,
  acceptedResponse,
  noContentResponse,
  fileDownloadResponse,
  streamResponse,
  redirectResponse,
  cacheResponse,
  wantsJSON,
  formatErrorForLogging
};
