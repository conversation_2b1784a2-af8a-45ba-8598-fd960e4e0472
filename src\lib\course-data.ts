import { Course, CreateCourseRequest, UpdateCourseRequest, CourseFilters } from '@/types/course'

// Mock data - In real app, this would come from your API
const mockCourses: Course[] = [
  {
    id: '1',
    name: 'JEE Main Physics Complete Course',
    description: 'Comprehensive physics course covering all JEE Main topics with detailed explanations and practice problems.',
    examId: '1',
    examName: 'JEE Main',
    price: 4999,
    isFree: false,
    status: 'PUBLISHED',
    thumbnail: '/images/physics-course.jpg',
    instructorName: 'Dr. <PERSON><PERSON>',
    instructorBio: 'PhD in Physics, 15+ years teaching experience',
    duration: 120,
    totalLessons: 85,
    totalStudents: 450,
    rating: 4.8,
    totalRatings: 234,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    tags: ['Physics', 'JEE Main', 'Mechanics', 'Thermodynamics'],
    features: ['Video Lectures', 'Practice Tests', 'Doubt Support', 'Study Material']
  },
  {
    id: '2',
    name: 'NEET Biology Masterclass',
    description: 'Complete biology preparation for NEET with focus on botany and zoology concepts.',
    examId: '3',
    examName: 'NEET UG',
    price: 5999,
    isFree: false,
    status: 'PUBLISHED',
    thumbnail: '/images/biology-course.jpg',
    instructorName: 'Dr. Priya Sharma',
    instructorBio: 'MSc Biology, NEET expert with 12+ years experience',
    duration: 150,
    totalLessons: 120,
    totalStudents: 380,
    rating: 4.9,
    totalRatings: 189,
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    tags: ['Biology', 'NEET', 'Botany', 'Zoology'],
    features: ['HD Video Lectures', 'Mock Tests', '24/7 Support', 'Notes PDF']
  },
  {
    id: '3',
    name: 'JEE Advanced Mathematics',
    description: 'Advanced mathematics course for JEE Advanced preparation with complex problem solving.',
    examId: '2',
    examName: 'JEE Advanced',
    price: 6999,
    isFree: false,
    status: 'PUBLISHED',
    thumbnail: '/images/math-course.jpg',
    instructorName: 'Prof. Amit Singh',
    instructorBio: 'IIT Graduate, Mathematics expert',
    duration: 100,
    totalLessons: 75,
    totalStudents: 320,
    rating: 4.6,
    totalRatings: 156,
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    tags: ['Mathematics', 'JEE Advanced', 'Calculus', 'Algebra'],
    features: ['Problem Solving', 'Previous Year Questions', 'Live Sessions']
  },
  {
    id: '4',
    name: 'Class 12 Physics CBSE',
    description: 'Complete Class 12 Physics course aligned with CBSE curriculum.',
    examId: '4',
    examName: 'Class 12 CBSE',
    price: 2999,
    isFree: false,
    status: 'PUBLISHED',
    thumbnail: '/images/class12-physics.jpg',
    instructorName: 'Mr. Suresh Gupta',
    instructorBio: 'Senior Physics Teacher, 20+ years experience',
    duration: 80,
    totalLessons: 60,
    totalStudents: 520,
    rating: 4.7,
    totalRatings: 298,
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
    tags: ['Physics', 'Class 12', 'CBSE', 'Board Exam'],
    features: ['NCERT Solutions', 'Sample Papers', 'Chapter Tests']
  },
  {
    id: '5',
    name: 'Free NEET Chemistry Basics',
    description: 'Free introductory chemistry course for NEET aspirants.',
    examId: '3',
    examName: 'NEET UG',
    price: 0,
    isFree: true,
    status: 'PUBLISHED',
    thumbnail: '/images/chemistry-free.jpg',
    instructorName: 'Dr. Neha Agarwal',
    instructorBio: 'Chemistry PhD, NEET mentor',
    duration: 30,
    totalLessons: 25,
    totalStudents: 1200,
    rating: 4.5,
    totalRatings: 567,
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    tags: ['Chemistry', 'NEET', 'Free', 'Basics'],
    features: ['Basic Concepts', 'Free Access', 'Community Support']
  }
]

export const getCourses = (filters?: CourseFilters): Promise<Course[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredCourses = [...mockCourses]
      
      if (filters) {
        if (filters.examId) {
          filteredCourses = filteredCourses.filter(c => c.examId === filters.examId)
        }
        if (filters.status) {
          filteredCourses = filteredCourses.filter(c => c.status === filters.status)
        }
        if (filters.isFree !== undefined) {
          filteredCourses = filteredCourses.filter(c => c.isFree === filters.isFree)
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredCourses = filteredCourses.filter(c => 
            c.name.toLowerCase().includes(searchLower) ||
            c.description.toLowerCase().includes(searchLower) ||
            c.instructorName.toLowerCase().includes(searchLower)
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredCourses.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredCourses)
    }, 100)
  })
}

export const getCourseById = (id: string): Promise<Course | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const course = mockCourses.find(c => c.id === id) || null
      resolve(course)
    }, 100)
  })
}

export const createCourse = (data: CreateCourseRequest): Promise<Course> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Find exam name
      const examNames: Record<string, string> = {
        '1': 'JEE Main',
        '2': 'JEE Advanced',
        '3': 'NEET UG',
        '4': 'Class 12 CBSE',
        '5': 'Class 11 CBSE'
      }
      
      const newCourse: Course = {
        id: Date.now().toString(),
        ...data,
        examName: examNames[data.examId] || 'Unknown Exam',
        totalLessons: 0,
        totalStudents: 0,
        rating: 0,
        totalRatings: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      mockCourses.push(newCourse)
      resolve(newCourse)
    }, 200)
  })
}

export const updateCourse = (data: UpdateCourseRequest): Promise<Course> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockCourses.findIndex(c => c.id === data.id)
      if (index === -1) {
        reject(new Error('Course not found'))
        return
      }
      
      mockCourses[index] = {
        ...mockCourses[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockCourses[index])
    }, 200)
  })
}

export const deleteCourse = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockCourses.findIndex(c => c.id === id)
      if (index === -1) {
        reject(new Error('Course not found'))
        return
      }
      
      mockCourses.splice(index, 1)
      resolve()
    }, 200)
  })
}
