import { Test, CreateTestRequest, UpdateTestRequest, TestFilters, TestStats, Question, CreateQuestionRequest } from '@/types/test'

// Mock data - In real app, this would come from your API
const mockTests: Test[] = [
  {
    id: '1',
    title: 'JEE Main Physics Mock Test 1',
    description: 'Comprehensive mock test covering all physics topics for JEE Main preparation',
    examId: '1',
    examName: 'JEE Main',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    subjectId: '1',
    subjectName: 'Mechanics',
    type: 'MOCK',
    difficulty: 'MEDIUM',
    duration: 180, // 3 hours
    totalQuestions: 25,
    totalMarks: 100,
    passingMarks: 40,
    negativeMarking: true,
    negativeMarkingRatio: 0.25,
    instructions: 'This is a mock test for JEE Main Physics. Each question carries 4 marks with -1 for wrong answers.',
    questions: [
      {
        id: 'q1',
        type: 'SINGLE_CHOICE',
        question: 'A particle moves in a straight line with constant acceleration. If it covers 10m in the first 2 seconds, what is its acceleration?',
        explanation: 'Using s = ut + (1/2)at², where u = 0, s = 10m, t = 2s, we get a = 5 m/s²',
        marks: 4,
        negativeMarks: 1,
        options: [
          { id: 'opt1', text: '2.5 m/s²', isCorrect: false },
          { id: 'opt2', text: '5 m/s²', isCorrect: true },
          { id: 'opt3', text: '10 m/s²', isCorrect: false },
          { id: 'opt4', text: '20 m/s²', isCorrect: false }
        ],
        correctAnswer: 'opt2',
        difficulty: 'EASY',
        subject: 'Physics',
        topic: 'Kinematics',
        tags: ['Motion', 'Acceleration', 'JEE Main'],
        order: 1,
        totalAttempts: 150,
        correctAttempts: 120,
        averageTime: 45
      }
    ],
    isPublished: true,
    isActive: true,
    allowRetake: true,
    maxAttempts: 3,
    showResults: true,
    showCorrectAnswers: true,
    randomizeQuestions: false,
    randomizeOptions: true,
    timePerQuestion: 432, // 7.2 minutes per question
    createdBy: 'Dr. Rajesh Kumar',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    publishedAt: '2024-01-15T12:00:00Z',
    tags: ['Physics', 'JEE Main', 'Mock Test', 'Mechanics'],
    totalAttempts: 245,
    averageScore: 72.5,
    averageTime: 165,
    completionRate: 89.2
  },
  {
    id: '2',
    title: 'NEET Biology Chapter Test - Cell Biology',
    description: 'Chapter-wise test on cell biology covering plant and animal cells',
    examId: '3',
    examName: 'NEET UG',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    subjectId: '3',
    subjectName: 'Botany',
    type: 'CHAPTER',
    difficulty: 'MEDIUM',
    duration: 60,
    totalQuestions: 15,
    totalMarks: 60,
    passingMarks: 24,
    negativeMarking: true,
    negativeMarkingRatio: 0.25,
    instructions: 'Chapter test on Cell Biology. Each question carries 4 marks with -1 for wrong answers.',
    questions: [],
    isPublished: true,
    isActive: true,
    allowRetake: true,
    maxAttempts: 5,
    showResults: true,
    showCorrectAnswers: false,
    randomizeQuestions: true,
    randomizeOptions: true,
    timePerQuestion: 240, // 4 minutes per question
    createdBy: 'Dr. Priya Sharma',
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    publishedAt: '2024-01-12T14:00:00Z',
    tags: ['Biology', 'NEET', 'Cell Biology', 'Chapter Test'],
    totalAttempts: 189,
    averageScore: 68.3,
    averageTime: 52,
    completionRate: 94.7
  },
  {
    id: '3',
    title: 'Quick Calculus Practice',
    description: 'Quick practice test for calculus concepts',
    examId: '2',
    examName: 'JEE Advanced',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    subjectId: '5',
    subjectName: 'Calculus',
    type: 'QUICK',
    difficulty: 'HARD',
    duration: 30,
    totalQuestions: 10,
    totalMarks: 40,
    passingMarks: 16,
    negativeMarking: true,
    negativeMarkingRatio: 0.25,
    instructions: 'Quick practice test for calculus. Each question carries 4 marks with -1 for wrong answers.',
    questions: [],
    isPublished: true,
    isActive: true,
    allowRetake: true,
    showResults: true,
    showCorrectAnswers: true,
    randomizeQuestions: false,
    randomizeOptions: false,
    timePerQuestion: 180, // 3 minutes per question
    createdBy: 'Prof. Amit Singh',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    publishedAt: '2024-01-10T16:00:00Z',
    tags: ['Mathematics', 'Calculus', 'JEE Advanced', 'Quick Test'],
    totalAttempts: 156,
    averageScore: 58.7,
    averageTime: 28,
    completionRate: 87.2
  },
  {
    id: '4',
    title: 'Class 12 Physics Full Length Test',
    description: 'Full length test covering entire Class 12 Physics syllabus',
    examId: '4',
    examName: 'Class 12 CBSE',
    courseId: '4',
    courseName: 'Class 12 Physics CBSE',
    subjectId: '7',
    subjectName: 'Optics',
    type: 'FULL_LENGTH',
    difficulty: 'MEDIUM',
    duration: 180,
    totalQuestions: 30,
    totalMarks: 70,
    passingMarks: 23,
    negativeMarking: false,
    instructions: 'Full length test for Class 12 Physics. No negative marking.',
    questions: [],
    isPublished: true,
    isActive: true,
    allowRetake: false,
    maxAttempts: 1,
    showResults: true,
    showCorrectAnswers: true,
    randomizeQuestions: false,
    randomizeOptions: false,
    timePerQuestion: 360, // 6 minutes per question
    createdBy: 'Mr. Suresh Gupta',
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
    publishedAt: '2024-01-08T13:00:00Z',
    tags: ['Physics', 'Class 12', 'CBSE', 'Full Length'],
    totalAttempts: 98,
    averageScore: 78.4,
    averageTime: 162,
    completionRate: 92.9
  },
  {
    id: '5',
    title: 'Organic Chemistry Revision Test',
    description: 'Revision test for organic chemistry concepts',
    examId: '3',
    examName: 'NEET UG',
    courseId: '5',
    courseName: 'Free NEET Chemistry Basics',
    subjectId: '8',
    subjectName: 'Organic Chemistry Basics',
    type: 'REVISION',
    difficulty: 'EASY',
    duration: 45,
    totalQuestions: 12,
    totalMarks: 48,
    passingMarks: 19,
    negativeMarking: true,
    negativeMarkingRatio: 0.25,
    instructions: 'Revision test for organic chemistry basics. Each question carries 4 marks with -1 for wrong answers.',
    questions: [],
    isPublished: true,
    isActive: true,
    allowRetake: true,
    showResults: true,
    showCorrectAnswers: true,
    randomizeQuestions: true,
    randomizeOptions: true,
    timePerQuestion: 225, // 3.75 minutes per question
    createdBy: 'Dr. Neha Agarwal',
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    publishedAt: '2024-01-05T15:00:00Z',
    tags: ['Chemistry', 'Organic', 'NEET', 'Revision'],
    totalAttempts: 234,
    averageScore: 82.1,
    averageTime: 41,
    completionRate: 96.2
  }
]

export const getTests = (filters?: TestFilters): Promise<Test[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredTests = [...mockTests]
      
      if (filters) {
        if (filters.examId) {
          filteredTests = filteredTests.filter(t => t.examId === filters.examId)
        }
        if (filters.courseId) {
          filteredTests = filteredTests.filter(t => t.courseId === filters.courseId)
        }
        if (filters.subjectId) {
          filteredTests = filteredTests.filter(t => t.subjectId === filters.subjectId)
        }
        if (filters.classId) {
          filteredTests = filteredTests.filter(t => t.classId === filters.classId)
        }
        if (filters.type) {
          filteredTests = filteredTests.filter(t => t.type === filters.type)
        }
        if (filters.difficulty) {
          filteredTests = filteredTests.filter(t => t.difficulty === filters.difficulty)
        }
        if (filters.isPublished !== undefined) {
          filteredTests = filteredTests.filter(t => t.isPublished === filters.isPublished)
        }
        if (filters.isActive !== undefined) {
          filteredTests = filteredTests.filter(t => t.isActive === filters.isActive)
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredTests = filteredTests.filter(t => 
            t.title.toLowerCase().includes(searchLower) ||
            t.description?.toLowerCase().includes(searchLower) ||
            t.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
            t.createdBy.toLowerCase().includes(searchLower)
          )
        }
        if (filters.tags && filters.tags.length > 0) {
          filteredTests = filteredTests.filter(t => 
            filters.tags!.some(tag => t.tags.includes(tag))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredTests.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredTests)
    }, 100)
  })
}

export const getTestById = (id: string): Promise<Test | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const test = mockTests.find(t => t.id === id) || null
      resolve(test)
    }, 100)
  })
}

export const getTestStats = (): Promise<TestStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: TestStats = {
        totalTests: mockTests.length,
        publishedTests: mockTests.filter(t => t.isPublished).length,
        activeTests: mockTests.filter(t => t.isActive).length,
        totalQuestions: mockTests.reduce((sum, t) => sum + t.totalQuestions, 0),
        totalAttempts: mockTests.reduce((sum, t) => sum + t.totalAttempts, 0),
        averageScore: mockTests.reduce((sum, t) => sum + t.averageScore, 0) / mockTests.length,
        averageCompletionRate: mockTests.reduce((sum, t) => sum + t.completionRate, 0) / mockTests.length
      }
      resolve(stats)
    }, 100)
  })
}

export const createTest = (data: CreateTestRequest): Promise<Test> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newTest: Test = {
        id: Date.now().toString(),
        ...data,
        questions: [],
        createdBy: 'Admin User',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        publishedAt: data.isPublished ? new Date().toISOString() : undefined,
        totalAttempts: 0,
        averageScore: 0,
        averageTime: 0,
        completionRate: 0
      }
      mockTests.push(newTest)
      resolve(newTest)
    }, 200)
  })
}

export const updateTest = (data: UpdateTestRequest): Promise<Test> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockTests.findIndex(t => t.id === data.id)
      if (index === -1) {
        reject(new Error('Test not found'))
        return
      }
      
      mockTests[index] = {
        ...mockTests[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockTests[index])
    }, 200)
  })
}

export const deleteTest = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockTests.findIndex(t => t.id === id)
      if (index === -1) {
        reject(new Error('Test not found'))
        return
      }
      
      mockTests.splice(index, 1)
      resolve()
    }, 200)
  })
}

export const addQuestionToTest = (testId: string, question: CreateQuestionRequest): Promise<Question> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const testIndex = mockTests.findIndex(t => t.id === testId)
      if (testIndex === -1) {
        reject(new Error('Test not found'))
        return
      }
      
      const newQuestion: Question = {
        id: Date.now().toString(),
        ...question,
        totalAttempts: 0,
        correctAttempts: 0,
        averageTime: 0
      }
      
      mockTests[testIndex].questions.push(newQuestion)
      mockTests[testIndex].totalQuestions = mockTests[testIndex].questions.length
      mockTests[testIndex].updatedAt = new Date().toISOString()
      
      resolve(newQuestion)
    }, 200)
  })
}
