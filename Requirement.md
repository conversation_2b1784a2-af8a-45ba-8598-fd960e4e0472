---

```markdown
# 🛠️ Utkrishta Classes - Admin Panel Functionality Specification

## 🧱 Tech Stack

- **Frontend Framework**: Next.js (v14+)
- **Language**: TypeScript
- **Styling**: TailwindCSS + Shadcn/UI + Lucide Icons
- **Authentication**: Firebase Admin SDK (OAuth + RBAC)
- **Charting**: Recharts.js or Chart.js
- **State Management**: React Query or Zustand
- **API Integration**: REST API (from backend project)

---

## 🔐 Access & Security

- Admins log in using **Firebase Auth (Email/Password or Google)**
- Only users with role = `"admin"` can access admin routes
- Role-based views (Super Admin, Content Manager, etc.)
- Secure routes using middleware for session/auth protection

---

## 🧭 Layout Structure

```

Sidebar Menu ➝ Main Content Area ➝ Header (Profile, Notifications)

```

- Responsive & mobile-friendly
- Sidebar collapsible
- Sticky top bar for notifications and user profile menu

---

## 📋 Core Admin Functionalities

### 1. 🏠 Dashboard (Analytics Overview)

**Route**: `/admin/dashboard`

- Total Users
- Total Courses
- Total Videos/Classes
- New Signups This Week
- Payments Overview (daily/weekly)
- Popular Courses & Tests
- Graphs: Line chart (revenue), Bar chart (student engagement)

---

### 2. 📚 Exam & Course Management

#### Route: `/admin/exams`

- ✅ List all exams
- ➕ Add new exam
- ✏️ Edit/Delete existing exams

#### Route: `/admin/courses`

- Filter by Exam
- View course cards
- Add new course (name, price, isFree, status)
- Link course to exam
- Manage course thumbnail, description, instructor name

---

### 3. 📘 Subject & Class Management

#### Route: `/admin/subjects`

- Filter by Course
- Add/Edit/Delete subjects

#### Route: `/admin/classes`

- Filter by Subject or Course
- Upload Class Details:
  - Title
  - Order
  - Video URL
  - PDF URL
  - Test ID (optional)
  - Duration
- Toggle visibility (publish/unpublish)

---

### 4. 📄 PDF Notes Upload

#### Route: `/admin/notes`

- Upload new note
- Assign to subject/class
- Drag-and-drop uploader
- View uploaded notes list (preview + delete option)

---

### 5. 🎥 Video Upload Interface

#### Route: `/admin/media/upload`

- Upload via:
  - File upload (local)
  - External Link (YouTube, Vimeo, etc.)
- Auto-generate thumbnail preview
- Map video to class

---

### 6. 🧪 Test Management

#### Route: `/admin/tests`

- Create new test
- Add/edit/delete questions (rich text editor)
- Assign to subject or class
- Preview test
- Set time limit and difficulty
- Toggle "Live" or "Draft"

---

### 7. 🎓 Student Management

#### Route: `/admin/students`

- Search by name, email, UID
- View profile:
  - Purchased Courses
  - Progress %
  - Test Scores
- Enable/disable account

---

### 8. 💳 Payments Management

#### Route: `/admin/payments`

- View transactions
- Filter by date, course, student
- Export CSV
- View payment details (status, transaction ID, etc.)

---

### 9. 🔴 Live Class Scheduler

#### Route: `/admin/live-classes`

- Add new live class:
  - Title, Course, Subject
  - Zoom/Jitsi link
  - Date/time, duration
- Notify enrolled students
- Mark as completed/expired

---

### 10. 📣 Notification Center

#### Route: `/admin/notifications`

- Compose new push message
- Select audience (all users, course-wise, exam-wise)
- Schedule or send now
- View notification history

---

### 11. 🧾 Content Review & Moderation

#### Route: `/admin/review`

- See recent uploads
- Moderate flagged comments or abuse (optional future scope)
- Approve/Reject student feedback or testimonials

---

### 12. 🔐 Admin Profile & Roles

#### Route: `/admin/settings`

- View current logged-in admin
- Edit profile (name, email)
- Role Management (Super Admin can add/remove admins)
- Activity logs (who did what & when)

---

## 🎨 UI Guidelines

- Use **Shadcn UI** (pre-built components + accessible)
- Clean, minimal design (white background, card layout)
- Icons: `lucide-react` (feather icons for elegance)
- Animation: Framer Motion for smooth transitions
- Theme: Light + Dark mode toggle
- Maintain uniform spacing (p-4, rounded-2xl, shadow-md)
- Use `Table`, `Card`, `Modal`, and `Tabs` wherever appropriate

---

## ✅ Admin UX Highlights

- 🔍 Search & Filters everywhere
- 📅 Date range pickers
- 🧩 Modular pages (Tab-based edit sections)
- 📤 Drag & Drop file uploads
- 🔁 Loading + Toast success/error messages
- 🧾 Copyable IDs, download CSVs

---

## 🧑‍🔧 Dev Considerations for Augment AI

- Use `app` directory in Next.js 14 for routing
- Layout with `SidebarLayout.tsx` and `AdminRouteGuard.tsx`
- Fetch backend data using `React Query`
- Persist auth using `Firebase Admin SDK` or secure cookie session
- Dynamic meta tags for SEO (optional)

---

## ✅ MVP Admin Panel Features

- [x] Login / Auth Guard
- [x] Dashboard Stats
- [x] Exam/Course/Class Management
- [x] Video/PDF Upload & Mapping
- [x] Test Creation & Result View
- [x] Student Management
- [x] Notifications
- [x] Live Class Schedule

---

```

```
