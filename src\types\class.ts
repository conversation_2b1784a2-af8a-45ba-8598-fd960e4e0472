export interface Class {
  id: string
  title: string
  description?: string
  subjectId: string
  subjectName: string
  courseId: string
  courseName: string
  order: number
  videoUrl?: string
  pdfUrl?: string
  testId?: string
  testName?: string
  duration: number // in minutes
  isPublished: boolean
  isFree: boolean
  viewCount: number
  completionRate: number
  createdAt: string
  updatedAt: string
  thumbnail?: string
  notes?: string
  resources: ClassResource[]
}

export interface ClassResource {
  id: string
  name: string
  type: 'PDF' | 'VIDEO' | 'LINK' | 'DOCUMENT'
  url: string
  size?: number // in bytes
  uploadedAt: string
}

export interface CreateClassRequest {
  title: string
  description?: string
  subjectId: string
  order: number
  duration: number
  videoUrl?: string
  pdfUrl?: string
  testId?: string
  notes?: string
  resources?: string[]
  isPublished: boolean
  isFree: boolean
  thumbnail?: string
}

export interface UpdateClassRequest extends Partial<CreateClassRequest> {
  id: string
}

export interface ClassFilters {
  subjectId?: string
  courseId?: string
  isPublished?: boolean
  isFree?: boolean
  hasVideo?: boolean
  hasPdf?: boolean
  hasTest?: boolean
  search?: string
  sortBy?: 'title' | 'order' | 'duration' | 'viewCount' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface ClassStats {
  totalClasses: number
  publishedClasses: number
  totalDuration: number
  averageCompletion: number
  totalViews: number
}
