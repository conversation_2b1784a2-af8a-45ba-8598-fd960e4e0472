export interface AdminUser {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  fullName: string
  
  // Profile Information
  avatar?: string
  phone?: string
  dateOfBirth?: string
  gender?: 'MALE' | 'FEMALE' | 'OTHER'
  address?: Address
  
  // Professional Information
  designation: string
  department: string
  employeeId?: string
  joiningDate: string
  experience: number // in years
  specializations: string[]
  bio?: string
  
  // Role and Permissions
  role: AdminRole
  permissions: Permission[]
  customPermissions: CustomPermission[]
  
  // Account Status
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING'
  isEmailVerified: boolean
  isPhoneVerified: boolean
  isTwoFactorEnabled: boolean
  
  // Security
  lastLogin?: string
  lastPasswordChange: string
  loginAttempts: number
  lockedUntil?: string
  passwordResetToken?: string
  passwordResetExpires?: string
  
  // Activity
  totalLogins: number
  totalSessions: number
  averageSessionDuration: number
  lastActivity?: string
  
  // Preferences
  preferences: AdminPreferences
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  
  // Analytics
  analytics: AdminAnalytics
}

export interface Address {
  street: string
  city: string
  state: string
  country: string
  postalCode: string
  coordinates?: {
    latitude: number
    longitude: number
  }
}

export interface AdminRole {
  id: string
  name: string
  displayName: string
  description: string
  level: number // 1 = Super Admin, 2 = Admin, 3 = Manager, 4 = Staff
  color: string
  
  // Permissions
  permissions: string[] // Permission IDs
  inheritsFrom?: string[] // Parent role IDs
  
  // Restrictions
  restrictions: RoleRestriction[]
  
  // Status
  isActive: boolean
  isSystem: boolean // Cannot be deleted
  
  // Usage
  userCount: number
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface Permission {
  id: string
  name: string
  displayName: string
  description: string
  category: PermissionCategory
  
  // Permission Details
  resource: string // e.g., 'students', 'courses', 'payments'
  actions: PermissionAction[] // e.g., ['read', 'write', 'delete']
  
  // Scope
  scope: 'GLOBAL' | 'DEPARTMENT' | 'SELF' | 'CUSTOM'
  conditions?: PermissionCondition[]
  
  // Status
  isActive: boolean
  isSystem: boolean
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface PermissionCategory {
  id: string
  name: string
  displayName: string
  description: string
  icon: string
  color: string
  order: number
}

export interface PermissionAction {
  id: string
  name: string
  displayName: string
  description: string
  level: 'READ' | 'WRITE' | 'DELETE' | 'ADMIN'
}

export interface PermissionCondition {
  field: string
  operator: 'EQUALS' | 'NOT_EQUALS' | 'IN' | 'NOT_IN' | 'GREATER_THAN' | 'LESS_THAN'
  value: any
  description: string
}

export interface CustomPermission {
  id: string
  adminId: string
  permissionId: string
  granted: boolean
  grantedBy: string
  grantedAt: string
  expiresAt?: string
  reason?: string
}

export interface RoleRestriction {
  type: 'TIME' | 'IP' | 'LOCATION' | 'DEVICE' | 'FEATURE'
  condition: string
  value: any
  description: string
}

export interface AdminPreferences {
  // UI Preferences
  theme: 'LIGHT' | 'DARK' | 'AUTO'
  language: string
  timezone: string
  dateFormat: string
  timeFormat: '12H' | '24H'
  
  // Dashboard Preferences
  dashboardLayout: 'DEFAULT' | 'COMPACT' | 'DETAILED'
  defaultPage: string
  sidebarCollapsed: boolean
  
  // Notification Preferences
  emailNotifications: boolean
  pushNotifications: boolean
  smsNotifications: boolean
  notificationSound: boolean
  
  // Security Preferences
  sessionTimeout: number // in minutes
  requirePasswordChange: boolean
  allowMultipleSessions: boolean
  
  // Data Preferences
  itemsPerPage: number
  defaultSortOrder: 'ASC' | 'DESC'
  showAdvancedFilters: boolean
  
  // Privacy Preferences
  showOnlineStatus: boolean
  allowProfileView: boolean
  shareAnalytics: boolean
}

export interface AdminAnalytics {
  // Login Analytics
  totalLogins: number
  uniqueLoginDays: number
  averageSessionDuration: number
  lastLoginStreak: number
  longestLoginStreak: number
  
  // Activity Analytics
  totalActions: number
  actionsToday: number
  actionsThisWeek: number
  actionsThisMonth: number
  
  // Feature Usage
  mostUsedFeatures: {
    feature: string
    count: number
    lastUsed: string
  }[]
  
  // Performance Metrics
  averageResponseTime: number
  errorRate: number
  successRate: number
  
  // Time Analytics
  activeHours: {
    hour: number
    count: number
  }[]
  
  activeDays: {
    day: string
    count: number
  }[]
}

export interface AdminSession {
  id: string
  adminId: string
  
  // Session Details
  startTime: string
  endTime?: string
  duration?: number
  isActive: boolean
  
  // Device Information
  deviceType: 'DESKTOP' | 'MOBILE' | 'TABLET'
  deviceName: string
  browser: string
  browserVersion: string
  os: string
  osVersion: string
  
  // Location Information
  ipAddress: string
  country: string
  city: string
  timezone: string
  
  // Activity
  pageViews: number
  actionsPerformed: number
  lastActivity: string
  
  // Security
  isSecure: boolean
  authMethod: 'PASSWORD' | 'TWO_FACTOR' | 'SSO'
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface AdminActivity {
  id: string
  adminId: string
  adminName: string
  
  // Activity Details
  action: string
  resource: string
  resourceId?: string
  description: string
  
  // Context
  module: string
  feature: string
  method: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'EXECUTE'
  
  // Request Details
  ipAddress: string
  userAgent: string
  
  // Data
  oldData?: any
  newData?: any
  metadata?: Record<string, any>
  
  // Status
  status: 'SUCCESS' | 'FAILED' | 'PENDING'
  errorMessage?: string
  
  // Timing
  timestamp: string
  duration?: number
  
  // Security
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  flagged: boolean
  flagReason?: string
}

export interface CreateAdminRequest {
  username: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  designation: string
  department: string
  employeeId?: string
  joiningDate: string
  roleId: string
  password: string
  sendWelcomeEmail: boolean
  specializations?: string[]
  bio?: string
}

export interface UpdateAdminRequest extends Partial<CreateAdminRequest> {
  id: string
  status?: AdminUser['status']
  permissions?: string[]
  customPermissions?: CustomPermission[]
}

export interface AdminFilters {
  search?: string
  role?: string
  department?: string
  status?: AdminUser['status']
  joiningDateFrom?: string
  joiningDateTo?: string
  lastLoginFrom?: string
  lastLoginTo?: string
  specializations?: string[]
  sortBy?: 'firstName' | 'lastName' | 'email' | 'joiningDate' | 'lastLogin' | 'totalLogins'
  sortOrder?: 'asc' | 'desc'
}

export interface AdminStats {
  totalAdmins: number
  activeAdmins: number
  inactiveAdmins: number
  suspendedAdmins: number
  pendingAdmins: number
  
  // Role Distribution
  roleDistribution: {
    roleId: string
    roleName: string
    count: number
    percentage: number
  }[]
  
  // Department Distribution
  departmentDistribution: {
    department: string
    count: number
    percentage: number
  }[]
  
  // Activity Metrics
  totalSessions: number
  activeSessions: number
  averageSessionDuration: number
  totalActions: number
  actionsToday: number
  
  // Login Analytics
  loginsToday: number
  loginsThisWeek: number
  loginsThisMonth: number
  uniqueLoginsToday: number
  
  // Security Metrics
  failedLoginAttempts: number
  lockedAccounts: number
  twoFactorEnabled: number
  passwordResetRequests: number
  
  // Growth Metrics
  newAdminsThisMonth: number
  monthlyGrowthRate: number
  
  // Recent Activity
  recentLogins: {
    adminId: string
    adminName: string
    loginTime: string
    ipAddress: string
    location: string
  }[]
  
  // Top Performers
  mostActiveAdmins: {
    adminId: string
    adminName: string
    totalActions: number
    lastActivity: string
  }[]
}

export interface RolePermissionMatrix {
  roleId: string
  roleName: string
  permissions: {
    categoryId: string
    categoryName: string
    permissions: {
      permissionId: string
      permissionName: string
      granted: boolean
      inherited: boolean
      source?: string // Role ID if inherited
    }[]
  }[]
}

export interface AdminAuditLog {
  id: string
  adminId: string
  adminName: string
  
  // Audit Details
  action: string
  resource: string
  resourceId?: string
  description: string
  
  // Changes
  changes: {
    field: string
    oldValue: any
    newValue: any
  }[]
  
  // Context
  ipAddress: string
  userAgent: string
  sessionId: string
  
  // Risk Assessment
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  riskFactors: string[]
  
  // Status
  status: 'SUCCESS' | 'FAILED' | 'BLOCKED'
  failureReason?: string
  
  // Metadata
  timestamp: string
  metadata?: Record<string, any>
}

export interface SecuritySettings {
  // Password Policy
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    preventReuse: number
    maxAge: number // days
  }
  
  // Session Settings
  sessionSettings: {
    timeout: number // minutes
    maxConcurrentSessions: number
    requireReauth: boolean
    reauthTimeout: number // minutes
  }
  
  // Login Settings
  loginSettings: {
    maxFailedAttempts: number
    lockoutDuration: number // minutes
    requireTwoFactor: boolean
    allowedIpRanges?: string[]
    blockedIpRanges?: string[]
  }
  
  // Audit Settings
  auditSettings: {
    logAllActions: boolean
    retentionPeriod: number // days
    alertOnSuspiciousActivity: boolean
    realTimeMonitoring: boolean
  }
}

export interface AdminNotification {
  id: string
  adminId: string
  
  // Notification Details
  type: 'SECURITY' | 'SYSTEM' | 'ROLE_CHANGE' | 'PERMISSION_CHANGE' | 'ACCOUNT' | 'REMINDER'
  title: string
  message: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  
  // Status
  read: boolean
  readAt?: string
  dismissed: boolean
  dismissedAt?: string
  
  // Action
  actionRequired: boolean
  actionUrl?: string
  actionLabel?: string
  
  // Metadata
  createdAt: string
  expiresAt?: string
  metadata?: Record<string, any>
}
