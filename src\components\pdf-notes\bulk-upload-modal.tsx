'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FileUpload } from '@/components/ui/file-upload'
import { Progress } from '@/components/ui/progress'
import { X, Upload, CheckCircle, AlertCircle, FileText } from 'lucide-react'
import { PDFUploadProgress } from '@/types/pdf-note'

interface BulkUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onBulkUpload: (files: File[]) => Promise<void>
}

interface BulkUploadState {
  files: File[]
  uploading: boolean
  progress: PDFUploadProgress[]
  completed: boolean
  error?: string
}

export function BulkUploadModal({ 
  isOpen, 
  onClose, 
  onBulkUpload
}: BulkUploadModalProps) {
  const [uploadState, setUploadState] = useState<BulkUploadState>({
    files: [],
    uploading: false,
    progress: [],
    completed: false
  })

  const handleFileSelect = (files: File[]) => {
    setUploadState(prev => ({ 
      ...prev, 
      files,
      progress: files.map(file => ({
        id: Math.random().toString(36).substr(2, 9),
        fileName: file.name,
        progress: 0,
        status: 'uploading' as const
      }))
    }))
  }

  const handleFileRemove = (index: number) => {
    setUploadState(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index),
      progress: prev.progress.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!uploadState.files.length) {
      return
    }

    setUploadState(prev => ({ ...prev, uploading: true, error: undefined }))

    try {
      // Simulate bulk upload with progress tracking
      const progressUpdates = uploadState.progress.map((item, index) => {
        return new Promise<void>((resolve) => {
          let currentProgress = 0
          const interval = setInterval(() => {
            currentProgress += Math.random() * 20
            if (currentProgress >= 100) {
              currentProgress = 100
              clearInterval(interval)
              
              setUploadState(prev => ({
                ...prev,
                progress: prev.progress.map((p, i) => 
                  i === index 
                    ? { ...p, progress: 100, status: 'completed' as const }
                    : p
                )
              }))
              resolve()
            } else {
              setUploadState(prev => ({
                ...prev,
                progress: prev.progress.map((p, i) => 
                  i === index 
                    ? { ...p, progress: currentProgress }
                    : p
                )
              }))
            }
          }, 200 + Math.random() * 300) // Vary speed for realism
        })
      })

      await Promise.all(progressUpdates)
      await onBulkUpload(uploadState.files)
      
      setUploadState(prev => ({ ...prev, completed: true, uploading: false }))
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 3000)
      
    } catch (error) {
      setUploadState(prev => ({ 
        ...prev, 
        uploading: false, 
        error: error instanceof Error ? error.message : 'Bulk upload failed' 
      }))
    }
  }

  const resetForm = () => {
    setUploadState({
      files: [],
      uploading: false,
      progress: [],
      completed: false
    })
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getOverallProgress = () => {
    if (uploadState.progress.length === 0) return 0
    const totalProgress = uploadState.progress.reduce((sum, item) => sum + item.progress, 0)
    return Math.round(totalProgress / uploadState.progress.length)
  }

  const getStatusColor = (status: PDFUploadProgress['status']) => {
    switch (status) {
      case 'uploading': return 'bg-blue-100 text-blue-800'
      case 'processing': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold">Bulk Upload PDF Notes</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {uploadState.completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Bulk Upload Successful!</h3>
              <p className="text-gray-600">
                {uploadState.files.length} PDF{uploadState.files.length > 1 ? 's' : ''} uploaded and processed successfully.
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* File Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Multiple PDF Files
                </label>
                <FileUpload
                  accept=".pdf"
                  maxSize={50 * 1024 * 1024} // 50MB per file
                  multiple={true}
                  onFileSelect={handleFileSelect}
                  onFileRemove={handleFileRemove}
                  disabled={uploadState.uploading}
                />
                <p className="text-sm text-gray-500 mt-2">
                  You can select multiple PDF files. Each file will be processed individually.
                </p>
              </div>

              {/* Overall Progress */}
              {uploadState.uploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Overall Progress</span>
                    <span className="text-sm text-gray-600">{getOverallProgress()}%</span>
                  </div>
                  <Progress value={getOverallProgress()} />
                </div>
              )}

              {/* Error Display */}
              {uploadState.error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{uploadState.error}</span>
                </div>
              )}

              {/* File Progress List */}
              {uploadState.progress.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-700">Upload Progress</h3>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {uploadState.progress.map((item, index) => (
                      <Card key={item.id} className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-red-600" />
                            <span className="text-sm font-medium truncate max-w-xs">
                              {item.fileName}
                            </span>
                            <Badge className={getStatusColor(item.status)}>
                              {item.status}
                            </Badge>
                          </div>
                          <span className="text-sm text-gray-500">
                            {formatFileSize(uploadState.files[index]?.size || 0)}
                          </span>
                        </div>
                        
                        {item.status === 'uploading' && (
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-500">Uploading...</span>
                              <span className="text-xs text-gray-500">{Math.round(item.progress)}%</span>
                            </div>
                            <Progress value={item.progress} className="h-1" />
                          </div>
                        )}
                        
                        {item.error && (
                          <div className="flex items-center space-x-1 mt-1">
                            <AlertCircle className="w-3 h-3 text-red-500" />
                            <span className="text-xs text-red-600">{item.error}</span>
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Instructions */}
              {uploadState.files.length > 0 && !uploadState.uploading && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Bulk Upload Instructions</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• All files will be uploaded with default settings</li>
                    <li>• You can edit individual PDF details after upload</li>
                    <li>• Files will be set as private by default</li>
                    <li>• Categories will be auto-detected based on filename</li>
                  </ul>
                </div>
              )}

              {/* Submit Buttons */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-gray-500">
                  {uploadState.files.length} file{uploadState.files.length !== 1 ? 's' : ''} selected
                </div>
                <div className="flex items-center space-x-3">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={!uploadState.files.length || uploadState.uploading}
                    className="flex items-center space-x-2"
                  >
                    <Upload className="w-4 h-4" />
                    <span>
                      {uploadState.uploading 
                        ? `Uploading ${uploadState.files.length} files...` 
                        : `Upload ${uploadState.files.length} PDF${uploadState.files.length > 1 ? 's' : ''}`
                      }
                    </span>
                  </Button>
                </div>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
