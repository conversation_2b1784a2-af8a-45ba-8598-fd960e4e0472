'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Download, 
  Eye, 
  FileText, 
  Star,
  Upload,
  Filter,
  MoreHorizontal,
  ExternalLink
} from 'lucide-react'
import { PDFNote, PDFNoteFilters, PDFNoteStats, CreatePDFNoteRequest } from '@/types/pdf-note'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'
import { Exam } from '@/types/exam'
import { getPDFNotes, deletePDFNote, getPDFNoteStats, createPDFNote } from '@/lib/pdf-note-data'
import { getCourses } from '@/lib/course-data'
import { getSubjects } from '@/lib/subject-data'
import { getExams } from '@/lib/exam-data'
import { UploadModal } from '@/components/pdf-notes/upload-modal'

export default function PDFNotesPage() {
  const [pdfNotes, setPdfNotes] = useState<PDFNote[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [stats, setStats] = useState<PDFNoteStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [filters, setFilters] = useState<PDFNoteFilters>({
    search: '',
    sortBy: 'uploadedAt',
    sortOrder: 'desc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadPDFNotes()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [notesData, coursesData, subjectsData, examsData, statsData] = await Promise.all([
        getPDFNotes(),
        getCourses(),
        getSubjects(),
        getExams(),
        getPDFNoteStats()
      ])
      setPdfNotes(notesData)
      setCourses(coursesData)
      setSubjects(subjectsData)
      setExams(examsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadPDFNotes = async () => {
    try {
      const data = await getPDFNotes(filters)
      setPdfNotes(data)
    } catch (error) {
      console.error('Failed to load PDF notes:', error)
    }
  }

  const handleDeleteNote = async (id: string) => {
    if (confirm('Are you sure you want to delete this PDF note?')) {
      try {
        await deletePDFNote(id)
        await loadPDFNotes()
        // Reload stats
        const statsData = await getPDFNoteStats()
        setStats(statsData)
      } catch (error) {
        console.error('Failed to delete PDF note:', error)
      }
    }
  }

  const handleFilterChange = (key: keyof PDFNoteFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleUpload = async (data: CreatePDFNoteRequest) => {
    try {
      await createPDFNote(data)
      await loadPDFNotes()
      // Reload stats
      const statsData = await getPDFNoteStats()
      setStats(statsData)
      setShowUploadModal(false)
    } catch (error) {
      console.error('Failed to upload PDF:', error)
      throw error
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'NOTES': return 'bg-blue-100 text-blue-800'
      case 'FORMULA_SHEET': return 'bg-green-100 text-green-800'
      case 'SUMMARY': return 'bg-purple-100 text-purple-800'
      case 'PRACTICE_PROBLEMS': return 'bg-orange-100 text-orange-800'
      case 'REFERENCE': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'HARD': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading PDF notes...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">PDF Notes Management</h1>
          <p className="text-gray-600 mt-1">Upload, organize, and manage PDF study materials</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Bulk Upload</span>
          </Button>
          <Button 
            className="flex items-center space-x-2"
            onClick={() => setShowUploadModal(true)}
          >
            <Plus className="w-4 h-4" />
            <span>Upload PDF</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total PDFs</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalNotes}</div>
              <p className="text-xs text-muted-foreground">All PDF notes</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Public</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.publicNotes}</div>
              <p className="text-xs text-muted-foreground">Publicly available</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Free</CardTitle>
              <Download className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.freeNotes}</div>
              <p className="text-xs text-muted-foreground">Free downloads</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Downloads</CardTitle>
              <Download className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDownloads.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Total downloads</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Views</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Total views</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">User rating</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search PDF notes by title, description, tags, or author..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.examId || 'all'} onValueChange={(value) => handleFilterChange('examId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by Exam" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Exams</SelectItem>
                {exams.map((exam) => (
                  <SelectItem key={exam.id} value={exam.id}>
                    {exam.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="NOTES">Notes</SelectItem>
                <SelectItem value="FORMULA_SHEET">Formula Sheet</SelectItem>
                <SelectItem value="SUMMARY">Summary</SelectItem>
                <SelectItem value="PRACTICE_PROBLEMS">Practice Problems</SelectItem>
                <SelectItem value="REFERENCE">Reference</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.isFree?.toString() || 'all'} onValueChange={(value) => handleFilterChange('isFree', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="false">Paid</SelectItem>
                <SelectItem value="true">Free</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>PDF Details</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Course/Subject</TableHead>
                <TableHead>File Info</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pdfNotes.map((note) => (
                <TableRow key={note.id}>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4 text-red-600" />
                        <div className="font-medium">{note.title}</div>
                      </div>
                      <div className="text-sm text-gray-500 line-clamp-2">{note.description}</div>
                      <div className="flex items-center space-x-2">
                        {note.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {note.tags.length > 3 && (
                          <span className="text-xs text-gray-400">+{note.tags.length - 3} more</span>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">
                        By {note.uploadedBy} • v{note.version}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getCategoryColor(note.category)}>
                        {note.category.replace('_', ' ')}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <Badge className={getDifficultyColor(note.difficulty)} variant="outline">
                          {note.difficulty}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {note.language}
                        </Badge>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {note.examName && (
                        <div className="text-sm font-medium">{note.examName}</div>
                      )}
                      {note.courseName && (
                        <div className="text-xs text-gray-500">{note.courseName}</div>
                      )}
                      {note.subjectName && (
                        <div className="text-xs text-blue-600">{note.subjectName}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{formatFileSize(note.fileSize)}</div>
                      {note.pageCount && (
                        <div className="text-xs text-gray-500">{note.pageCount} pages</div>
                      )}
                      <div className="text-xs text-gray-400">
                        {new Date(note.uploadedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Download className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{note.downloadCount.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{note.viewCount.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                        <span className="text-sm">{note.rating}</span>
                        <span className="text-xs text-gray-400">({note.totalRatings})</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Badge variant={note.isPublic ? 'success' : 'secondary'}>
                          {note.isPublic ? 'Public' : 'Private'}
                        </Badge>
                        {note.isFree && (
                          <Badge variant="success" className="text-xs">Free</Badge>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" title="Preview">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Download">
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Edit">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        title="Delete"
                        onClick={() => handleDeleteNote(note.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {pdfNotes.length === 0 && (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No PDF notes found matching your filters.</p>
              <Button onClick={() => setShowUploadModal(true)}>
                Upload your first PDF
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Upload className="w-4 h-4" />
              <span>Bulk Upload PDFs</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Filter className="w-4 h-4" />
              <span>Advanced Filters</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Download className="w-4 h-4" />
              <span>Export List</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <MoreHorizontal className="w-4 h-4" />
              <span>More Actions</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Upload Modal */}
      <UploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUpload}
        courses={courses}
        subjects={subjects}
        exams={exams}
      />
    </div>
  )
}
