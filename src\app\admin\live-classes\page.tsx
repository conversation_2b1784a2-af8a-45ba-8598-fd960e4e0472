'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar,
  Clock,
  Users,
  Video,
  Play,
  Square,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Star,
  MapPin,
  Monitor,
  Smartphone,
  Globe,
  Download,
  Filter,
  MoreHorizontal,
  Copy,
  Settings,
  BarChart3,
  UserCheck,
  MessageSquare
} from 'lucide-react'
import { LiveClass, LiveClassFilters, LiveClassStats, CreateLiveClassRequest, Instructor } from '@/types/live-class'
import { Exam } from '@/types/exam'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'
import { getLiveClasses, deleteLiveClass, getLiveClassStats, createLiveClass, getInstructors } from '@/lib/live-class-data'
import { getExams } from '@/lib/exam-data'
import { getCourses } from '@/lib/course-data'
import { getSubjects } from '@/lib/subject-data'
import { AddLiveClassModal } from '@/components/live-classes/add-live-class-modal'

export default function LiveClassesPage() {
  const [liveClasses, setLiveClasses] = useState<LiveClass[]>([])
  const [instructors, setInstructors] = useState<Instructor[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [stats, setStats] = useState<LiveClassStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedClasses, setSelectedClasses] = useState<string[]>([])
  const [filters, setFilters] = useState<LiveClassFilters>({
    search: '',
    sortBy: 'scheduledDate',
    sortOrder: 'asc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadLiveClasses()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [classesData, instructorsData, examsData, coursesData, subjectsData, statsData] = await Promise.all([
        getLiveClasses(),
        getInstructors(),
        getExams(),
        getCourses(),
        getSubjects(),
        getLiveClassStats()
      ])
      setLiveClasses(classesData)
      setInstructors(instructorsData)
      setExams(examsData)
      setCourses(coursesData)
      setSubjects(subjectsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadLiveClasses = async () => {
    try {
      const data = await getLiveClasses(filters)
      setLiveClasses(data)
    } catch (error) {
      console.error('Failed to load live classes:', error)
    }
  }

  const handleDeleteClass = async (id: string) => {
    if (confirm('Are you sure you want to delete this live class? This action cannot be undone.')) {
      try {
        await deleteLiveClass(id)
        await loadLiveClasses()
        // Reload stats
        const statsData = await getLiveClassStats()
        setStats(statsData)
      } catch (error) {
        console.error('Failed to delete live class:', error)
      }
    }
  }

  const handleFilterChange = (key: keyof LiveClassFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleAddClass = async (data: CreateLiveClassRequest) => {
    try {
      await createLiveClass(data)
      await loadLiveClasses()
      // Reload stats
      const statsData = await getLiveClassStats()
      setStats(statsData)
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add live class:', error)
      throw error
    }
  }

  const handleSelectClass = (classId: string) => {
    setSelectedClasses(prev => 
      prev.includes(classId) 
        ? prev.filter(id => id !== classId)
        : [...prev, classId]
    )
  }

  const handleSelectAll = () => {
    setSelectedClasses(
      selectedClasses.length === liveClasses.length 
        ? [] 
        : liveClasses.map(c => c.id)
    )
  }

  const formatDateTime = (date: string, time: string): string => {
    const dateObj = new Date(`${date}T${time}`)
    return dateObj.toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SCHEDULED': return 'bg-blue-100 text-blue-800'
      case 'LIVE': return 'bg-green-100 text-green-800'
      case 'COMPLETED': return 'bg-gray-100 text-gray-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'POSTPONED': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SCHEDULED': return <Clock className="w-4 h-4" />
      case 'LIVE': return <Play className="w-4 h-4" />
      case 'COMPLETED': return <CheckCircle className="w-4 h-4" />
      case 'CANCELLED': return <XCircle className="w-4 h-4" />
      case 'POSTPONED': return <AlertTriangle className="w-4 h-4" />
      default: return <Square className="w-4 h-4" />
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'ZOOM': return <Video className="w-4 h-4" />
      case 'GOOGLE_MEET': return <Globe className="w-4 h-4" />
      case 'MICROSOFT_TEAMS': return <Monitor className="w-4 h-4" />
      case 'CUSTOM': return <Settings className="w-4 h-4" />
      case 'OFFLINE': return <MapPin className="w-4 h-4" />
      default: return <Video className="w-4 h-4" />
    }
  }

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'FREE': return 'bg-green-100 text-green-800'
      case 'BASIC': return 'bg-blue-100 text-blue-800'
      case 'PREMIUM': return 'bg-purple-100 text-purple-800'
      case 'ENTERPRISE': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading live classes...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Live Class Scheduler</h1>
          <p className="text-gray-600 mt-1">Schedule, manage, and track live classes and sessions</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center space-x-2">
            <Calendar className="w-4 h-4" />
            <span>Calendar View</span>
          </Button>
          <Button variant="outline" className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export Schedule</span>
          </Button>
          <Button 
            className="flex items-center space-x-2"
            onClick={() => setShowAddModal(true)}
          >
            <Plus className="w-4 h-4" />
            <span>Schedule Class</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-8 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalClasses}</div>
              <p className="text-xs text-muted-foreground">All scheduled</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.scheduledClasses}</div>
              <p className="text-xs text-muted-foreground">Upcoming classes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Live Now</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.liveClasses}</div>
              <p className="text-xs text-muted-foreground">Currently live</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedClasses}</div>
              <p className="text-xs text-muted-foreground">Finished classes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Registrations</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalRegistrations}</div>
              <p className="text-xs text-muted-foreground">Total bookings</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageAttendanceRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Avg attendance</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{(stats.totalRevenue / 1000).toFixed(0)}K</div>
              <p className="text-xs text-muted-foreground">Total earnings</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Instructors</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeInstructors}</div>
              <p className="text-xs text-muted-foreground">Active teachers</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search classes by title, instructor, or topic..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                <SelectItem value="LIVE">Live Now</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="POSTPONED">Postponed</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.instructorId || 'all'} onValueChange={(value) => handleFilterChange('instructorId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Instructor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Instructors</SelectItem>
                {instructors.map((instructor) => (
                  <SelectItem key={instructor.id} value={instructor.id}>
                    {instructor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.platform || 'all'} onValueChange={(value) => handleFilterChange('platform', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Platforms</SelectItem>
                <SelectItem value="ZOOM">Zoom</SelectItem>
                <SelectItem value="GOOGLE_MEET">Google Meet</SelectItem>
                <SelectItem value="MICROSOFT_TEAMS">Teams</SelectItem>
                <SelectItem value="CUSTOM">Custom</SelectItem>
                <SelectItem value="OFFLINE">Offline</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.accessLevel || 'all'} onValueChange={(value) => handleFilterChange('accessLevel', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Access" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Access</SelectItem>
                <SelectItem value="FREE">Free</SelectItem>
                <SelectItem value="BASIC">Basic</SelectItem>
                <SelectItem value="PREMIUM">Premium</SelectItem>
                <SelectItem value="ENTERPRISE">Enterprise</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>More Filters</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Bulk Actions */}
          {selectedClasses.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  {selectedClasses.length} class{selectedClasses.length > 1 ? 'es' : ''} selected
                </span>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline">
                    <Copy className="w-4 h-4 mr-1" />
                    Duplicate
                  </Button>
                  <Button size="sm" variant="outline">
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Send Reminder
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
            </div>
          )}

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedClasses.length === liveClasses.length && liveClasses.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300"
                  />
                </TableHead>
                <TableHead>Class Details</TableHead>
                <TableHead>Instructor</TableHead>
                <TableHead>Schedule</TableHead>
                <TableHead>Platform & Access</TableHead>
                <TableHead>Participants</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {liveClasses.map((liveClass) => (
                <TableRow key={liveClass.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedClasses.includes(liveClass.id)}
                      onChange={() => handleSelectClass(liveClass.id)}
                      className="rounded border-gray-300"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Video className="w-4 h-4 text-blue-600" />
                        <div className="font-medium">{liveClass.title}</div>
                      </div>
                      <div className="text-sm text-gray-500 line-clamp-2">{liveClass.description}</div>
                      <div className="flex items-center space-x-2">
                        {liveClass.examName && (
                          <Badge variant="outline" className="text-xs">
                            {liveClass.examName}
                          </Badge>
                        )}
                        {liveClass.subjectName && (
                          <Badge variant="outline" className="text-xs">
                            {liveClass.subjectName}
                          </Badge>
                        )}
                        {liveClass.isFree && (
                          <Badge className="bg-green-100 text-green-800 text-xs">
                            FREE
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {liveClass.topics.slice(0, 2).map((topic, index) => (
                          <span key={index} className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                            {topic}
                          </span>
                        ))}
                        {liveClass.topics.length > 2 && (
                          <span className="text-xs text-gray-400">+{liveClass.topics.length - 2} more</span>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                        {liveClass.instructorName.split(' ').map(n => n.charAt(0)).join('')}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{liveClass.instructorName}</div>
                        <div className="text-xs text-gray-500">{liveClass.instructorEmail}</div>
                        {liveClass.averageRating && (
                          <div className="flex items-center space-x-1 mt-1">
                            <Star className="w-3 h-3 text-yellow-500 fill-current" />
                            <span className="text-xs text-gray-600">{liveClass.averageRating.toFixed(1)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{formatDateTime(liveClass.scheduledDate, liveClass.startTime)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{formatDuration(liveClass.duration)}</span>
                      </div>
                      {liveClass.isRecurring && (
                        <div className="text-xs text-blue-600 flex items-center space-x-1">
                          <span>🔄</span>
                          <span>Recurring</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        {getPlatformIcon(liveClass.platform)}
                        <span className="text-sm font-medium">{liveClass.platform.replace('_', ' ')}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Badge className={getAccessLevelColor(liveClass.accessLevel)} variant="outline">
                          {liveClass.accessLevel}
                        </Badge>
                      </div>
                      {liveClass.price && (
                        <div className="text-sm text-green-600 font-medium">
                          ₹{liveClass.price}
                        </div>
                      )}
                      <div className="text-xs text-gray-500">
                        {liveClass.mode} • {liveClass.type}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Users className="w-3 h-3 text-gray-400" />
                        <span className="text-sm font-medium">
                          {liveClass.currentParticipants}
                          {liveClass.maxParticipants && `/${liveClass.maxParticipants}`}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {liveClass.totalRegistrations} registered
                      </div>
                      {liveClass.attendanceCount > 0 && (
                        <div className="text-xs text-green-600">
                          {liveClass.attendanceCount} attended
                        </div>
                      )}
                      {liveClass.maxParticipants && (
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full"
                            style={{ width: `${(liveClass.currentParticipants / liveClass.maxParticipants) * 100}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getStatusColor(liveClass.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(liveClass.status)}
                          <span>{liveClass.status}</span>
                        </div>
                      </Badge>
                      {liveClass.recordingUrl && (
                        <div className="text-xs text-purple-600">
                          Recording available
                        </div>
                      )}
                      {liveClass.reminderSent && (
                        <div className="text-xs text-green-600">
                          Reminder sent
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button variant="outline" size="sm" title="View Details">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Edit Class">
                        <Edit className="w-4 h-4" />
                      </Button>
                      {liveClass.status === 'SCHEDULED' && (
                        <Button variant="outline" size="sm" title="Start Class">
                          <Play className="w-4 h-4" />
                        </Button>
                      )}
                      <Button variant="outline" size="sm" title="Analytics">
                        <BarChart3 className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="More Options">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        title="Delete Class"
                        onClick={() => handleDeleteClass(liveClass.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {liveClasses.length === 0 && (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No live classes found matching your filters.</p>
              <Button onClick={() => setShowAddModal(true)}>
                Schedule your first class
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Platform Distribution & Popular Time Slots */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Platform Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.platformDistribution.map((platform) => (
                  <div key={platform.platform} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getPlatformIcon(platform.platform)}
                      <span className="text-sm font-medium">{platform.platform.replace('_', ' ')}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold">{platform.count} classes</div>
                      <div className="text-xs text-gray-500">{platform.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Popular Time Slots</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.popularTimeSlots.map((slot) => (
                  <div key={slot.timeSlot} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium">{slot.timeSlot}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold">{slot.count} classes</div>
                      <div className="text-xs text-green-600">{slot.attendanceRate}% attendance</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Calendar className="w-4 h-4" />
              <span>Calendar View</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <MessageSquare className="w-4 h-4" />
              <span>Send Reminders</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Download className="w-4 h-4" />
              <span>Export Reports</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Settings className="w-4 h-4" />
              <span>Platform Settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Add Live Class Modal */}
      <AddLiveClassModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddClass}
        instructors={instructors}
        exams={exams}
        courses={courses}
        subjects={subjects}
      />
    </div>
  )
}
