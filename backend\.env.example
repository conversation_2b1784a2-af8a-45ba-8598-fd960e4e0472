# Environment Configuration
NODE_ENV=development
PORT=5000
API_VERSION=1.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=utkrishta_admin
DB_CONNECTION_LIMIT=10
DB_SSL=false

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_complex
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here_make_it_different
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
CACHE_PREFIX=utkrishta

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_NAME=Utkrishta Admin
SMTP_FROM_EMAIL=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# File Upload Configuration
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document
UPLOAD_PATH=uploads/

# CDN Configuration
CDN_BASE_URL=https://cdn.utkrishta.com
CDN_ACCESS_KEY=your_cdn_access_key
CDN_SECRET_KEY=your_cdn_secret_key
CDN_BUCKET=utkrishta-assets

# Payment Gateway Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# Video Platform Configuration
ZOOM_API_KEY=your_zoom_api_key
ZOOM_API_SECRET=your_zoom_api_secret
ZOOM_WEBHOOK_SECRET=your_zoom_webhook_secret

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Feature Flags
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_LIVE_CLASSES=true
FEATURE_CONTENT_REVIEW=true
FEATURE_PAYMENTS=true

# External Services
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
SENTRY_DSN=https://your-sentry-dsn-here
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# Development/Testing
DEBUG=utkrishta:*
MOCK_EXTERNAL_SERVICES=false
SEED_DATABASE=false
