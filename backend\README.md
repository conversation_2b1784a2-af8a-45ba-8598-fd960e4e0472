# Utkrishta Admin Panel Backend

A comprehensive backend API system for managing coaching business operations, built with Node.js, Express, and MySQL.

## 🚀 Features

### Dashboard Analytics
- **Real-time KPI Metrics**: Student enrollments, revenue, course performance
- **Advanced Analytics**: Trend analysis, growth metrics, performance insights
- **Interactive Widgets**: Customizable dashboard components
- **WebSocket Support**: Real-time updates and live data streaming
- **Export Capabilities**: CSV, Excel, PDF export functionality

### Core Modules
- **User Authentication**: JWT-based secure authentication
- **Role-Based Access Control**: Granular permissions and role management
- **Student Management**: Complete student lifecycle management
- **Course Management**: Course creation, content management, enrollment tracking
- **Instructor Management**: Instructor profiles, performance analytics
- **Test Management**: Test creation, result processing, analytics
- **Assignment Management**: Assignment lifecycle, submission tracking
- **Payment Processing**: Multi-gateway payment integration
- **Live Class Scheduling**: Video platform integration, attendance tracking
- **Notification System**: Multi-channel notifications (Email, SMS, Push)
- **Content Review**: Quality assurance and moderation workflows

## 🛠️ Technology Stack

- **Runtime**: Node.js 16+
- **Framework**: Express.js
- **Database**: MySQL 8.0+
- **Cache**: Redis
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Express Validator
- **Real-time**: WebSocket (ws)
- **File Processing**: Multer, Sharp
- **Documentation**: OpenAPI/Swagger
- **Testing**: Jest, Supertest
- **Code Quality**: ESLint, Prettier

## 📋 Prerequisites

- Node.js 16.0 or higher
- MySQL 8.0 or higher
- Redis 6.0 or higher (optional, for caching)
- npm or yarn package manager

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/utkrishta/admin-backend.git
cd admin-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:
```env
NODE_ENV=development
PORT=5000
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=utkrishta_admin
JWT_SECRET=your_jwt_secret
REDIS_HOST=localhost
```

### 4. Database Setup
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE utkrishta_admin;"

# Run migrations (when available)
npm run db:migrate

# Seed database (optional)
npm run db:seed
```

### 5. Start the Server
```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:5000`

## 📚 API Documentation

### Dashboard Analytics APIs

#### GET /api/dashboard/overview
Get comprehensive dashboard statistics and metrics.

**Query Parameters:**
- `period` (optional): `1d`, `7d`, `30d`, `90d`, `1y`
- `compare_previous` (optional): `true`, `false`
- `timezone` (optional): Timezone string

**Response:**
```json
{
  "success": true,
  "data": {
    "key_metrics": {
      "total_students": {
        "value": 1247,
        "change": 12.5,
        "change_type": "increase"
      }
    },
    "growth_metrics": {},
    "recent_activities": [],
    "revenue_chart": {},
    "top_courses": [],
    "upcoming_events": [],
    "alerts": []
  }
}
```

#### GET /api/dashboard/analytics
Get detailed analytics data with period comparisons.

#### GET /api/dashboard/widgets
Get customizable dashboard widgets with chart data.

#### POST /api/dashboard/widgets/layout
Save custom dashboard widget layout.

#### GET /api/dashboard/reports
Get comprehensive dashboard reports with insights.

#### GET /api/dashboard/export
Export dashboard data in various formats.

### WebSocket Real-time Updates

Connect to `ws://localhost:5000/ws?token=your_jwt_token` for real-time updates.

**Message Types:**
- `initial_data`: Initial dashboard data
- `periodic_update`: Periodic updates every 30 seconds
- `ping`/`pong`: Keep-alive messages

## 🔐 Authentication

All API endpoints require authentication using JWT tokens.

**Headers:**
```
Authorization: Bearer your_jwt_token_here
```

**Token Payload:**
```json
{
  "id": "user_id",
  "username": "admin_user",
  "email": "<EMAIL>",
  "role": {
    "code": "admin",
    "level": 2
  }
}
```

## 🛡️ Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Granular permission system
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS Protection**: Configurable CORS policies
- **Helmet Security**: Security headers and protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Input sanitization

## 📊 Database Schema

### Core Tables
- `admin_users`: Admin user accounts
- `roles`: User roles and permissions
- `students`: Student information
- `courses`: Course catalog
- `instructors`: Instructor profiles
- `tests`: Test and exam data
- `assignments`: Assignment management
- `payments`: Payment transactions
- `live_classes`: Live class scheduling
- `notifications`: Notification system

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `5000` |
| `DB_HOST` | Database host | `localhost` |
| `DB_USER` | Database user | `root` |
| `JWT_SECRET` | JWT secret key | Required |
| `REDIS_HOST` | Redis host | `localhost` |

### Feature Flags

Enable/disable features using environment variables:
- `FEATURE_ANALYTICS=true`
- `FEATURE_NOTIFICATIONS=true`
- `FEATURE_LIVE_CLASSES=true`

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📝 Development

### Code Style
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Database Migrations
```bash
# Create new migration
npm run db:create-migration migration_name

# Run migrations
npm run db:migrate

# Rollback migrations
npm run db:rollback
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
npm run docker:build

# Run Docker container
npm run docker:run
```

### Production Deployment
1. Set `NODE_ENV=production`
2. Configure production database
3. Set up Redis for caching
4. Configure reverse proxy (Nginx)
5. Set up SSL certificates
6. Configure monitoring and logging

## 📈 Monitoring

### Health Check
```bash
curl http://localhost:5000/health
```

### Metrics Endpoints
- `/health` - Application health status
- `/metrics` - Application metrics (when enabled)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [API Docs](https://docs.utkrishta.com)

## 🔄 Changelog

### v1.0.0 (Current)
- Initial release
- Dashboard Analytics APIs
- Authentication system
- Role-based access control
- Real-time WebSocket support
- Comprehensive error handling
- Production-ready architecture
