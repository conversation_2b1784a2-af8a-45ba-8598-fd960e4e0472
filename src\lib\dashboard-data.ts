import { DashboardStats, RevenueData, EngagementData, PopularCourse, RecentActivity } from '@/types/dashboard'

// Mock data - In real app, this would come from your API
export const getDashboardStats = (): DashboardStats => ({
  totalUsers: 2847,
  totalCourses: 24,
  totalVideos: 342,
  newSignupsThisWeek: 127,
  totalRevenue: 485000,
  revenueGrowth: 12.5
})

export const getRevenueData = (): RevenueData[] => [
  { date: '2024-01-01', revenue: 45000 },
  { date: '2024-01-02', revenue: 52000 },
  { date: '2024-01-03', revenue: 48000 },
  { date: '2024-01-04', revenue: 61000 },
  { date: '2024-01-05', revenue: 55000 },
  { date: '2024-01-06', revenue: 67000 },
  { date: '2024-01-07', revenue: 72000 },
  { date: '2024-01-08', revenue: 58000 },
  { date: '2024-01-09', revenue: 63000 },
  { date: '2024-01-10', revenue: 69000 },
  { date: '2024-01-11', revenue: 74000 },
  { date: '2024-01-12', revenue: 81000 },
  { date: '2024-01-13', revenue: 78000 },
  { date: '2024-01-14', revenue: 85000 },
]

export const getEngagementData = (): EngagementData[] => [
  { course: 'JEE Main Physics', students: 450, completion: 78 },
  { course: 'NEET Biology', students: 380, completion: 82 },
  { course: 'JEE Advanced Math', students: 320, completion: 65 },
  { course: 'NEET Chemistry', students: 290, completion: 75 },
  { course: 'Class 12 Physics', students: 520, completion: 88 },
  { course: 'Class 11 Math', students: 410, completion: 71 },
]

export const getPopularCourses = (): PopularCourse[] => [
  {
    id: '1',
    name: 'JEE Main Complete Course',
    students: 450,
    revenue: 135000,
    rating: 4.8
  },
  {
    id: '2',
    name: 'NEET Biology Masterclass',
    students: 380,
    revenue: 114000,
    rating: 4.9
  },
  {
    id: '3',
    name: 'Class 12 Physics',
    students: 520,
    revenue: 104000,
    rating: 4.7
  },
  {
    id: '4',
    name: 'JEE Advanced Mathematics',
    students: 320,
    revenue: 96000,
    rating: 4.6
  }
]

export const getRecentActivity = (): RecentActivity[] => [
  {
    id: '1',
    type: 'enrollment',
    message: 'New student enrolled in JEE Main Physics',
    timestamp: '2 minutes ago',
    user: 'Rahul Sharma'
  },
  {
    id: '2',
    type: 'payment',
    message: 'Payment received for NEET Biology course',
    timestamp: '5 minutes ago',
    user: 'Priya Patel'
  },
  {
    id: '3',
    type: 'completion',
    message: 'Student completed Chapter 5 - Thermodynamics',
    timestamp: '12 minutes ago',
    user: 'Amit Kumar'
  },
  {
    id: '4',
    type: 'test',
    message: 'Mock test submitted for JEE Advanced Math',
    timestamp: '18 minutes ago',
    user: 'Sneha Gupta'
  },
  {
    id: '5',
    type: 'enrollment',
    message: 'New student enrolled in Class 12 Physics',
    timestamp: '25 minutes ago',
    user: 'Vikash Singh'
  }
]
