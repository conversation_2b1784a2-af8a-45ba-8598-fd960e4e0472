# 🚀 Utkrishta Coaching Admin Panel - API Requirements (Part 2)

## 8️⃣ PAYMENT MANAGEMENT APIs

### 8.1 Payment CRUD APIs

#### GET /payments
**Purpose**: Get paginated list of payments with filters
**Query Parameters**:
```
?page=1&limit=25&search=rahul&status=completed&payment_method=online&date_from=2024-01-01&date_to=2024-01-31&amount_min=10000&amount_max=50000&sort_by=created_at&sort_order=desc
```
**Response**:
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "id": "uuid",
        "payment_id": "PAY_123456789",
        "transaction_id": "TXN_987654321",
        "student": {
          "id": "uuid",
          "name": "<PERSON><PERSON>",
          "email": "<EMAIL>",
          "phone": "+************"
        },
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics",
          "instructor": "<PERSON><PERSON><PERSON>"
        },
        "amount_details": {
          "original_amount": 20000,
          "discount_amount": 5000,
          "final_amount": 15000,
          "tax_amount": 2700,
          "total_amount": 17700,
          "currency": "INR"
        },
        "payment_method": {
          "type": "online",
          "provider": "razorpay",
          "card_last4": "1234",
          "card_type": "visa"
        },
        "status": "completed",
        "payment_date": "2024-01-15T10:30:00Z",
        "due_date": "2024-01-15T23:59:59Z",
        "description": "JEE Main Physics Course Fee",
        "invoice_number": "INV-2024-001",
        "receipt_url": "https://cdn.utkrishta.com/receipts/receipt_123.pdf",
        "refund_info": {
          "is_refundable": true,
          "refund_amount": 0,
          "refund_reason": null,
          "refund_date": null
        },
        "subscription_info": {
          "is_subscription": false,
          "subscription_id": null,
          "billing_cycle": null,
          "next_billing_date": null
        },
        "created_at": "2024-01-15T10:25:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 2456,
      "total_pages": 99,
      "has_next": true,
      "has_prev": false
    },
    "summary": {
      "total_amount": 43650000,
      "completed_payments": 2234,
      "pending_payments": 156,
      "failed_payments": 66,
      "refunded_payments": 23
    }
  }
}
```

#### GET /payments/{id}
**Purpose**: Get detailed payment information

#### POST /payments
**Purpose**: Create new payment/initiate payment
**Request Body**:
```json
{
  "student_id": "uuid",
  "course_id": "uuid",
  "amount": 15000,
  "payment_method": "online",
  "discount_code": "EARLY20",
  "description": "JEE Main Physics Course Fee",
  "due_date": "2024-02-15T23:59:59Z",
  "send_invoice": true,
  "auto_capture": true
}
```

#### PUT /payments/{id}
**Purpose**: Update payment status/information

#### POST /payments/{id}/capture
**Purpose**: Capture authorized payment

#### POST /payments/{id}/refund
**Purpose**: Process payment refund
**Request Body**:
```json
{
  "amount": 15000,
  "reason": "Course cancellation",
  "refund_type": "full",
  "notify_student": true,
  "internal_notes": "Student requested cancellation within 7 days"
}
```

#### POST /payments/{id}/retry
**Purpose**: Retry failed payment

### 8.2 Subscription Management APIs

#### GET /subscriptions
**Purpose**: Get list of subscriptions
**Response**:
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "id": "uuid",
        "subscription_id": "SUB_123456",
        "student": {
          "id": "uuid",
          "name": "Priya Patel",
          "email": "<EMAIL>"
        },
        "plan": {
          "id": "uuid",
          "name": "Premium Monthly",
          "amount": 2999,
          "billing_cycle": "monthly",
          "features": ["All courses", "Live classes", "Mock tests"]
        },
        "status": "active",
        "current_period_start": "2024-01-15T00:00:00Z",
        "current_period_end": "2024-02-15T23:59:59Z",
        "next_billing_date": "2024-02-15T00:00:00Z",
        "auto_renewal": true,
        "payment_method": {
          "type": "card",
          "card_last4": "5678",
          "card_type": "mastercard"
        },
        "billing_history": [
          {
            "payment_id": "uuid",
            "amount": 2999,
            "status": "completed",
            "billing_date": "2024-01-15T00:00:00Z"
          }
        ],
        "created_at": "2024-01-15T00:00:00Z",
        "updated_at": "2024-01-15T00:00:00Z"
      }
    ]
  }
}
```

#### POST /subscriptions
**Purpose**: Create new subscription

#### PUT /subscriptions/{id}
**Purpose**: Update subscription

#### POST /subscriptions/{id}/cancel
**Purpose**: Cancel subscription

#### POST /subscriptions/{id}/pause
**Purpose**: Pause subscription

#### POST /subscriptions/{id}/resume
**Purpose**: Resume paused subscription

### 8.3 Payment Analytics APIs

#### GET /payments/stats
**Purpose**: Get payment statistics
**Response**:
```json
{
  "success": true,
  "data": {
    "revenue_metrics": {
      "total_revenue": 43650000,
      "monthly_revenue": 3450000,
      "daily_revenue": 125000,
      "average_transaction_value": 17800,
      "revenue_growth": 23.5
    },
    "payment_metrics": {
      "total_payments": 2456,
      "successful_payments": 2234,
      "failed_payments": 66,
      "pending_payments": 156,
      "success_rate": 90.9,
      "failure_rate": 2.7
    },
    "method_distribution": [
      {
        "method": "online",
        "count": 2100,
        "percentage": 85.5,
        "amount": 37302500
      },
      {
        "method": "offline",
        "count": 356,
        "percentage": 14.5,
        "amount": 6347500
      }
    ],
    "subscription_metrics": {
      "active_subscriptions": 456,
      "cancelled_subscriptions": 23,
      "paused_subscriptions": 12,
      "subscription_revenue": 1367544,
      "churn_rate": 4.8,
      "retention_rate": 95.2
    },
    "refund_metrics": {
      "total_refunds": 23,
      "refund_amount": 345000,
      "refund_rate": 0.9,
      "average_refund_amount": 15000
    },
    "revenue_chart": [
      {
        "date": "2024-01-01",
        "revenue": 125000,
        "transactions": 45,
        "average_value": 2777
      }
    ],
    "top_courses_by_revenue": [
      {
        "course_id": "uuid",
        "course_name": "JEE Main Physics",
        "revenue": 8505000,
        "transactions": 567,
        "average_value": 15000
      }
    ]
  }
}
```

#### GET /payments/analytics
**Purpose**: Get detailed payment analytics
**Query Parameters**: `?period=30d&group_by=day&include_comparisons=true`

---

## 9️⃣ LIVE CLASS MANAGEMENT APIs

### 9.1 Live Class CRUD APIs

#### GET /live-classes
**Purpose**: Get list of live classes with filters
**Query Parameters**: `?page=1&limit=25&search=physics&status=scheduled&date_from=2024-01-01&instructor=uuid&course=uuid`
**Response**:
```json
{
  "success": true,
  "data": {
    "classes": [
      {
        "id": "uuid",
        "title": "JEE Main Physics - Laws of Motion",
        "description": "Comprehensive discussion on Newton's laws with problem solving",
        "course": {
          "id": "uuid",
          "title": "JEE Main Physics Complete",
          "subject": "Physics"
        },
        "instructor": {
          "id": "uuid",
          "name": "Dr. Rajesh Kumar",
          "avatar": "https://cdn.utkrishta.com/instructors/rajesh.jpg",
          "qualification": "Ph.D. in Physics"
        },
        "schedule": {
          "start_time": "2024-01-25T16:00:00Z",
          "end_time": "2024-01-25T17:30:00Z",
          "duration": 90,
          "timezone": "Asia/Kolkata",
          "is_recurring": false,
          "recurrence_pattern": null
        },
        "meeting_details": {
          "platform": "zoom",
          "meeting_id": "123-456-789",
          "meeting_url": "https://zoom.us/j/123456789",
          "passcode": "physics123",
          "waiting_room_enabled": true,
          "recording_enabled": true
        },
        "enrollment": {
          "max_capacity": 100,
          "enrolled_students": 67,
          "waitlist_count": 5,
          "registration_required": true,
          "registration_deadline": "2024-01-25T15:00:00Z"
        },
        "content": {
          "topics_covered": ["Newton's First Law", "Newton's Second Law", "Applications"],
          "materials": [
            {
              "id": "uuid",
              "title": "Laws of Motion Notes",
              "type": "pdf",
              "url": "https://cdn.utkrishta.com/materials/laws-of-motion.pdf"
            }
          ],
          "assignments": [
            {
              "id": "uuid",
              "title": "Practice Problems - Laws of Motion",
              "due_date": "2024-01-27T23:59:59Z"
            }
          ]
        },
        "status": "scheduled",
        "is_public": false,
        "tags": ["jee_main", "physics", "mechanics"],
        "created_at": "2024-01-20T10:00:00Z",
        "updated_at": "2024-01-24T15:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 156,
      "total_pages": 7,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### GET /live-classes/{id}
**Purpose**: Get detailed live class information

#### POST /live-classes
**Purpose**: Create new live class
**Request Body**:
```json
{
  "title": "NEET Biology - Human Physiology",
  "description": "Detailed study of human organ systems",
  "course_id": "uuid",
  "instructor_id": "uuid",
  "schedule": {
    "start_time": "2024-02-01T16:00:00Z",
    "duration": 90,
    "timezone": "Asia/Kolkata",
    "is_recurring": true,
    "recurrence_pattern": {
      "type": "weekly",
      "days": ["monday", "wednesday", "friday"],
      "end_date": "2024-06-30"
    }
  },
  "meeting_details": {
    "platform": "zoom",
    "waiting_room_enabled": true,
    "recording_enabled": true,
    "auto_generate_meeting": true
  },
  "enrollment": {
    "max_capacity": 150,
    "registration_required": true,
    "registration_deadline_hours": 2
  },
  "content": {
    "topics_covered": ["Circulatory System", "Respiratory System"],
    "materials": ["uuid1", "uuid2"],
    "assignments": ["uuid3"]
  },
  "is_public": false,
  "tags": ["neet", "biology", "physiology"],
  "send_notifications": true
}
```

#### PUT /live-classes/{id}
**Purpose**: Update live class

#### DELETE /live-classes/{id}
**Purpose**: Delete live class

#### POST /live-classes/{id}/start
**Purpose**: Start live class session

#### POST /live-classes/{id}/end
**Purpose**: End live class session

#### POST /live-classes/{id}/record
**Purpose**: Start/stop recording

### 9.2 Live Class Enrollment APIs

#### GET /live-classes/{id}/enrollments
**Purpose**: Get class enrollment list
**Response**:
```json
{
  "success": true,
  "data": {
    "enrollment_summary": {
      "total_enrolled": 67,
      "max_capacity": 100,
      "waitlist_count": 5,
      "attendance_rate": 85.5
    },
    "enrollments": [
      {
        "student_id": "uuid",
        "student_name": "Rahul Sharma",
        "student_email": "<EMAIL>",
        "enrollment_date": "2024-01-20T10:30:00Z",
        "attendance_status": "present",
        "join_time": "2024-01-25T16:02:00Z",
        "leave_time": "2024-01-25T17:28:00Z",
        "duration_attended": 86,
        "participation_score": 8.5
      }
    ]
  }
}
```

#### POST /live-classes/{id}/enroll
**Purpose**: Enroll student in live class
**Request Body**:
```json
{
  "student_id": "uuid",
  "enrollment_type": "direct",
  "send_confirmation": true
}
```

#### DELETE /live-classes/{id}/enrollments/{student_id}
**Purpose**: Remove student from class

#### GET /live-classes/{id}/attendance
**Purpose**: Get class attendance report

#### POST /live-classes/{id}/attendance
**Purpose**: Mark attendance for class

### 9.3 Live Class Analytics APIs

#### GET /live-classes/{id}/analytics
**Purpose**: Get class performance analytics
**Response**:
```json
{
  "success": true,
  "data": {
    "attendance_analytics": {
      "total_enrolled": 67,
      "students_attended": 58,
      "attendance_rate": 86.6,
      "average_join_time": "16:03:00",
      "average_duration": 82,
      "early_leavers": 5
    },
    "engagement_analytics": {
      "questions_asked": 23,
      "chat_messages": 156,
      "polls_conducted": 3,
      "average_participation": 7.8,
      "most_active_students": [
        {
          "student_id": "uuid",
          "name": "Rahul Sharma",
          "participation_score": 9.5,
          "questions_asked": 5,
          "chat_messages": 12
        }
      ]
    },
    "content_analytics": {
      "topics_covered": 3,
      "materials_shared": 5,
      "assignments_given": 2,
      "recording_duration": 88,
      "recording_views": 234
    },
    "feedback_summary": {
      "average_rating": 4.7,
      "total_feedback": 45,
      "positive_feedback": 89,
      "improvement_suggestions": [
        "More interactive sessions",
        "Better audio quality"
      ]
    }
  }
}
```

#### GET /live-classes/stats
**Purpose**: Get overall live class statistics

---

## 🔟 NOTIFICATION MANAGEMENT APIs

### 10.1 Notification CRUD APIs

#### GET /notifications
**Purpose**: Get list of notifications with filters
**Query Parameters**: `?page=1&limit=25&search=physics&type=info&category=class&status=sent&recipient_type=students`
**Response**:
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "title": "JEE Main Physics Class Tomorrow",
        "message": "Don't forget about your JEE Main Physics class on Laws of Motion scheduled for tomorrow at 10:00 AM.",
        "type": "info",
        "category": "class",
        "priority": "medium",
        "recipient_type": "group",
        "recipients": {
          "groups": ["uuid1"],
          "users": [],
          "total_count": 245
        },
        "channels": [
          {
            "type": "in_app",
            "enabled": true,
            "config": {}
          },
          {
            "type": "email",
            "enabled": true,
            "config": {
              "subject": "Class Reminder - JEE Main Physics"
            }
          },
          {
            "type": "sms",
            "enabled": false,
            "config": {}
          }
        ],
        "content": {
          "short_text": "JEE Main Physics class tomorrow at 10:00 AM",
          "full_text": "Don't forget about your JEE Main Physics class on Laws of Motion scheduled for tomorrow at 10:00 AM. The class will cover Newton's laws with practical examples.",
          "action_buttons": [
            {
              "id": "join",
              "label": "Join Class",
              "type": "button",
              "action": "/classes/join/uuid",
              "style": "primary"
            }
          ],
          "attachments": []
        },
        "schedule": {
          "send_immediately": false,
          "scheduled_at": "2024-01-24T18:00:00Z",
          "timezone": "Asia/Kolkata",
          "expires_at": "2024-01-25T12:00:00Z"
        },
        "delivery_status": {
          "total": 245,
          "sent": 245,
          "delivered": 238,
          "read": 189,
          "failed": 7,
          "clicked": 156
        },
        "analytics": {
          "open_rate": 77.1,
          "click_rate": 63.7,
          "dismissal_rate": 12.3,
          "avg_read_time": 45
        },
        "status": "sent",
        "tags": ["class-reminder", "jee-main", "physics"],
        "template_id": "uuid",
        "campaign_id": null,
        "created_by": "admin",
        "created_at": "2024-01-23T15:30:00Z",
        "sent_at": "2024-01-24T18:00:00Z",
        "updated_at": "2024-01-24T18:05:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 25,
      "total": 156,
      "total_pages": 7,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### GET /notifications/{id}
**Purpose**: Get detailed notification information

#### POST /notifications
**Purpose**: Create and send notification
**Request Body**:
```json
{
  "title": "Test Results Published",
  "message": "Your JEE Main Mock Test #5 results are now available.",
  "type": "success",
  "category": "exam",
  "priority": "high",
  "recipient_type": "specific",
  "recipients": {
    "users": ["uuid1", "uuid2", "uuid3"]
  },
  "channels": [
    {
      "type": "in_app",
      "enabled": true
    },
    {
      "type": "email",
      "enabled": true,
      "config": {
        "subject": "Test Results - JEE Main Mock Test #5"
      }
    }
  ],
  "content": {
    "short_text": "JEE Main Mock Test #5 results available",
    "full_text": "Your JEE Main Mock Test #5 results are now available. Check your detailed performance analysis in your dashboard.",
    "action_buttons": [
      {
        "id": "view_results",
        "label": "View Results",
        "type": "button",
        "action": "/tests/results/uuid",
        "style": "primary"
      }
    ]
  },
  "schedule": {
    "send_immediately": true,
    "timezone": "Asia/Kolkata"
  },
  "tags": ["test-results", "jee-main", "mock-test"],
  "template_id": "uuid"
}
```

#### PUT /notifications/{id}
**Purpose**: Update notification

#### DELETE /notifications/{id}
**Purpose**: Delete notification

#### POST /notifications/{id}/send
**Purpose**: Send scheduled notification immediately

#### POST /notifications/{id}/cancel
**Purpose**: Cancel scheduled notification

### 10.2 Notification Templates APIs

#### GET /notification-templates
**Purpose**: Get notification templates
**Response**:
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "uuid",
        "name": "Class Reminder",
        "description": "Remind students about upcoming classes",
        "category": "class",
        "type": "info",
        "title": "Class Reminder: {{className}}",
        "message": "Your class \"{{className}}\" is scheduled for {{classDate}} at {{classTime}}. Don't miss it!",
        "variables": [
          {
            "key": "className",
            "label": "Class Name",
            "type": "text",
            "required": true
          },
          {
            "key": "classDate",
            "label": "Class Date",
            "type": "date",
            "required": true
          },
          {
            "key": "classTime",
            "label": "Class Time",
            "type": "text",
            "required": true
          }
        ],
        "default_channels": [
          {
            "type": "in_app",
            "enabled": true
          },
          {
            "type": "email",
            "enabled": true
          }
        ],
        "default_priority": "medium",
        "default_recipient_type": "students",
        "usage_count": 45,
        "last_used": "2024-01-20T10:00:00Z",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T12:00:00Z"
      }
    ]
  }
}
```

#### POST /notification-templates
**Purpose**: Create notification template

#### PUT /notification-templates/{id}
**Purpose**: Update notification template

#### DELETE /notification-templates/{id}
**Purpose**: Delete notification template

### 10.3 Notification Analytics APIs

#### GET /notifications/stats
**Purpose**: Get notification statistics
**Response**:
```json
{
  "success": true,
  "data": {
    "overview_metrics": {
      "total_notifications": 156,
      "sent_today": 12,
      "sent_this_week": 45,
      "sent_this_month": 156,
      "avg_open_rate": 80.9,
      "avg_click_rate": 47.0,
      "avg_delivery_rate": 97.5,
      "total_engagement": 2567
    },
    "status_distribution": [
      {
        "status": "sent",
        "count": 134,
        "percentage": 85.9
      },
      {
        "status": "scheduled",
        "count": 12,
        "percentage": 7.7
      },
      {
        "status": "draft",
        "count": 10,
        "percentage": 6.4
      }
    ],
    "channel_performance": [
      {
        "channel": "in_app",
        "sent": 1584,
        "delivered": 1547,
        "opened": 1234,
        "clicked": 789,
        "delivery_rate": 97.7,
        "open_rate": 79.8,
        "click_rate": 49.8
      },
      {
        "channel": "email",
        "sent": 1247,
        "delivered": 1198,
        "opened": 856,
        "clicked": 423,
        "delivery_rate": 96.1,
        "open_rate": 71.5,
        "click_rate": 33.9
      }
    ],
    "category_performance": [
      {
        "category": "class",
        "count": 45,
        "open_rate": 77.1,
        "click_rate": 63.7
      },
      {
        "category": "payment",
        "count": 23,
        "open_rate": 66.7,
        "click_rate": 33.3
      }
    ],
    "recent_activity": [
      {
        "date": "2024-01-25",
        "sent": 12,
        "delivered": 11,
        "opened": 9,
        "clicked": 6
      }
    ],
    "top_notifications": [
      {
        "id": "uuid",
        "title": "Test Results Published",
        "open_rate": 100,
        "click_rate": 100,
        "sent_count": 234
      }
    ]
  }
}
```

---

## 1️⃣1️⃣ FILE MANAGEMENT APIs

### 11.1 File Upload APIs

#### POST /files/upload
**Purpose**: Upload files (images, PDFs, videos)
**Request**: Multipart form data
**Response**:
```json
{
  "success": true,
  "data": {
    "file": {
      "id": "uuid",
      "filename": "physics-notes.pdf",
      "original_name": "JEE Main Physics Notes.pdf",
      "mime_type": "application/pdf",
      "size": 2048000,
      "url": "https://cdn.utkrishta.com/files/physics-notes.pdf",
      "thumbnail_url": "https://cdn.utkrishta.com/thumbnails/physics-notes.jpg",
      "category": "document",
      "uploaded_by": "admin",
      "uploaded_at": "2024-01-25T10:30:00Z"
    }
  }
}
```

#### GET /files
**Purpose**: Get list of uploaded files
**Query Parameters**: `?page=1&limit=25&type=pdf&category=document&search=physics`

#### DELETE /files/{id}
**Purpose**: Delete file

### 11.2 Media Processing APIs

#### POST /files/{id}/process
**Purpose**: Process uploaded media (video transcoding, image optimization)

#### GET /files/{id}/status
**Purpose**: Get file processing status

---

## 1️⃣2️⃣ REPORTING & ANALYTICS APIs

### 12.1 Report Generation APIs

#### GET /reports/students
**Purpose**: Generate student reports
**Query Parameters**: `?format=pdf&date_from=2024-01-01&date_to=2024-01-31&include_performance=true`

#### GET /reports/revenue
**Purpose**: Generate revenue reports

#### GET /reports/courses
**Purpose**: Generate course performance reports

#### GET /reports/attendance
**Purpose**: Generate attendance reports

### 12.2 Export APIs

#### POST /exports/students
**Purpose**: Export student data
**Request Body**:
```json
{
  "format": "csv",
  "filters": {
    "exam": "jee_main",
    "status": "active",
    "date_from": "2024-01-01"
  },
  "fields": ["name", "email", "phone", "performance", "enrollment_date"],
  "email_to": "<EMAIL>"
}
```

#### GET /exports/{id}/status
**Purpose**: Get export job status

#### GET /exports/{id}/download
**Purpose**: Download exported file

---

## 🔧 SYSTEM & CONFIGURATION APIs

### System Health APIs

#### GET /health
**Purpose**: System health check

#### GET /health/detailed
**Purpose**: Detailed system status

### Configuration APIs

#### GET /config/app
**Purpose**: Get application configuration

#### PUT /config/app
**Purpose**: Update application settings

### Audit APIs

#### GET /audit/logs
**Purpose**: Get audit logs
**Query Parameters**: `?page=1&limit=50&user=uuid&action=login&date_from=2024-01-01`

---

## 📱 MOBILE APP APIs

### Mobile Authentication APIs

#### POST /mobile/auth/login
**Purpose**: Mobile app login

#### POST /mobile/auth/register
**Purpose**: Mobile app registration

### Mobile-Specific APIs

#### GET /mobile/dashboard
**Purpose**: Mobile dashboard data

#### GET /mobile/notifications
**Purpose**: Mobile notifications

#### POST /mobile/push-token
**Purpose**: Register push notification token

---

## 🔄 WEBHOOK APIs

### Webhook Management APIs

#### GET /webhooks
**Purpose**: Get configured webhooks

#### POST /webhooks
**Purpose**: Create webhook

#### PUT /webhooks/{id}
**Purpose**: Update webhook

#### DELETE /webhooks/{id}
**Purpose**: Delete webhook

### Webhook Events

#### Payment Events
- `payment.completed`
- `payment.failed`
- `subscription.created`
- `subscription.cancelled`

#### Student Events
- `student.enrolled`
- `student.completed_course`
- `student.test_submitted`

#### Class Events
- `class.started`
- `class.ended`
- `class.attendance_marked`

---

## 🚀 IMPLEMENTATION PRIORITY

### Phase 1 (Core APIs)
1. Authentication & Admin Management
2. Dashboard & Analytics
3. Student Management
4. Course Management

### Phase 2 (Extended Features)
5. Exam Management
6. Subject Management
7. Test Management
8. Payment Management

### Phase 3 (Advanced Features)
9. Live Class Management
10. Notification Management
11. File Management
12. Reporting & Analytics

### Phase 4 (System APIs)
13. System & Configuration
14. Mobile App APIs
15. Webhook APIs

---

## 📋 TECHNICAL REQUIREMENTS

### Database Requirements
- PostgreSQL 13+ for main database
- Redis for caching and sessions
- MongoDB for logs and analytics (optional)

### External Services
- Payment Gateway (Razorpay/Stripe)
- Video Conferencing (Zoom/Google Meet)
- Email Service (SendGrid/AWS SES)
- SMS Service (Twilio/AWS SNS)
- File Storage (AWS S3/CloudFlare R2)
- CDN (CloudFlare/AWS CloudFront)

### Security Requirements
- JWT-based authentication
- Role-based access control (RBAC)
- API rate limiting
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration

### Performance Requirements
- Response time < 200ms for most APIs
- Pagination for large datasets
- Database indexing
- Query optimization
- Caching strategy
- Background job processing

---

## 📚 API DOCUMENTATION

### OpenAPI Specification
- Complete OpenAPI 3.0 specification
- Interactive API documentation (Swagger UI)
- Postman collection
- Code examples in multiple languages

### Testing
- Unit tests for all endpoints
- Integration tests
- Load testing
- Security testing

This comprehensive API specification covers all the functionality needed for the Utkrishta Coaching Admin Panel. Each API endpoint includes detailed request/response formats, authentication requirements, and business logic specifications.
