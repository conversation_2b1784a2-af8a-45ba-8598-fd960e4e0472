import { PDFNote, CreatePDFNoteRequest, UpdatePDFNoteRequest, PDFNoteFilters, PDFNoteStats } from '@/types/pdf-note'

// Mock data - In real app, this would come from your API
const mockPDFNotes: PDFNote[] = [
  {
    id: '1',
    title: 'JEE Main Physics Formula Sheet',
    description: 'Complete formula sheet covering all physics topics for JEE Main preparation',
    fileName: 'jee-main-physics-formulas.pdf',
    fileUrl: 'https://example.com/pdfs/jee-main-physics-formulas.pdf',
    fileSize: 2048000, // 2MB
    thumbnailUrl: 'https://example.com/thumbnails/physics-formulas.jpg',
    examId: '1',
    examName: 'JEE Main',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    subjectId: '1',
    subjectName: 'Mechanics',
    category: 'FORMULA_SHEET',
    tags: ['Physics', 'Formulas', 'JEE Main', 'Quick Reference'],
    isPublic: true,
    isFree: false,
    downloadCount: 1250,
    viewCount: 3400,
    rating: 4.8,
    totalRatings: 156,
    uploadedBy: 'Dr. <PERSON>',
    uploadedAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'MEDIUM',
    pageCount: 12
  },
  {
    id: '2',
    title: 'NEET Biology Chapter Notes - Cell Biology',
    description: 'Comprehensive notes on cell biology covering plant and animal cells',
    fileName: 'neet-biology-cell-notes.pdf',
    fileUrl: 'https://example.com/pdfs/neet-biology-cell-notes.pdf',
    fileSize: 3072000, // 3MB
    thumbnailUrl: 'https://example.com/thumbnails/cell-biology.jpg',
    examId: '3',
    examName: 'NEET UG',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    subjectId: '3',
    subjectName: 'Botany',
    category: 'NOTES',
    tags: ['Biology', 'Cell Biology', 'NEET', 'Botany'],
    isPublic: true,
    isFree: false,
    downloadCount: 890,
    viewCount: 2100,
    rating: 4.9,
    totalRatings: 89,
    uploadedBy: 'Dr. Priya Sharma',
    uploadedAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    version: 2,
    language: 'ENGLISH',
    difficulty: 'MEDIUM',
    pageCount: 24
  },
  {
    id: '3',
    title: 'Calculus Practice Problems with Solutions',
    description: 'Extensive collection of calculus problems with detailed step-by-step solutions',
    fileName: 'calculus-practice-problems.pdf',
    fileUrl: 'https://example.com/pdfs/calculus-practice-problems.pdf',
    fileSize: 4096000, // 4MB
    thumbnailUrl: 'https://example.com/thumbnails/calculus-problems.jpg',
    examId: '2',
    examName: 'JEE Advanced',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    subjectId: '5',
    subjectName: 'Calculus',
    category: 'PRACTICE_PROBLEMS',
    tags: ['Mathematics', 'Calculus', 'JEE Advanced', 'Practice'],
    isPublic: true,
    isFree: false,
    downloadCount: 670,
    viewCount: 1800,
    rating: 4.6,
    totalRatings: 67,
    uploadedBy: 'Prof. Amit Singh',
    uploadedAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'HARD',
    pageCount: 45
  },
  {
    id: '4',
    title: 'Class 12 Physics NCERT Summary',
    description: 'Concise summary of all NCERT Physics chapters for Class 12 CBSE',
    fileName: 'class12-physics-ncert-summary.pdf',
    fileUrl: 'https://example.com/pdfs/class12-physics-ncert-summary.pdf',
    fileSize: 1536000, // 1.5MB
    thumbnailUrl: 'https://example.com/thumbnails/ncert-summary.jpg',
    examId: '4',
    examName: 'Class 12 CBSE',
    courseId: '4',
    courseName: 'Class 12 Physics CBSE',
    subjectId: '7',
    subjectName: 'Optics',
    category: 'SUMMARY',
    tags: ['Physics', 'NCERT', 'Class 12', 'CBSE', 'Summary'],
    isPublic: true,
    isFree: true,
    downloadCount: 2100,
    viewCount: 4500,
    rating: 4.7,
    totalRatings: 234,
    uploadedBy: 'Mr. Suresh Gupta',
    uploadedAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'EASY',
    pageCount: 18
  },
  {
    id: '5',
    title: 'Organic Chemistry Quick Reference (Hindi)',
    description: 'Quick reference guide for organic chemistry concepts in Hindi',
    fileName: 'organic-chemistry-hindi-reference.pdf',
    fileUrl: 'https://example.com/pdfs/organic-chemistry-hindi-reference.pdf',
    fileSize: 1024000, // 1MB
    thumbnailUrl: 'https://example.com/thumbnails/organic-hindi.jpg',
    examId: '3',
    examName: 'NEET UG',
    courseId: '5',
    courseName: 'Free NEET Chemistry Basics',
    subjectId: '8',
    subjectName: 'Organic Chemistry Basics',
    category: 'REFERENCE',
    tags: ['Chemistry', 'Organic', 'Hindi', 'NEET', 'Reference'],
    isPublic: true,
    isFree: true,
    downloadCount: 1800,
    viewCount: 3200,
    rating: 4.5,
    totalRatings: 145,
    uploadedBy: 'Dr. Neha Agarwal',
    uploadedAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    version: 1,
    language: 'HINDI',
    difficulty: 'EASY',
    pageCount: 8
  },
  {
    id: '6',
    title: 'JEE Main Mathematics Previous Year Solutions',
    description: 'Detailed solutions to JEE Main mathematics questions from last 5 years',
    fileName: 'jee-main-math-previous-years.pdf',
    fileUrl: 'https://example.com/pdfs/jee-main-math-previous-years.pdf',
    fileSize: 5120000, // 5MB
    thumbnailUrl: 'https://example.com/thumbnails/previous-years.jpg',
    examId: '1',
    examName: 'JEE Main',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    category: 'PRACTICE_PROBLEMS',
    tags: ['Mathematics', 'JEE Main', 'Previous Year', 'Solutions'],
    isPublic: true,
    isFree: false,
    downloadCount: 950,
    viewCount: 2300,
    rating: 4.8,
    totalRatings: 98,
    uploadedBy: 'Prof. Mathematics Team',
    uploadedAt: '2024-01-03T10:00:00Z',
    updatedAt: '2024-01-03T10:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'HARD',
    pageCount: 67
  }
]

export const getPDFNotes = (filters?: PDFNoteFilters): Promise<PDFNote[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredNotes = [...mockPDFNotes]
      
      if (filters) {
        if (filters.examId) {
          filteredNotes = filteredNotes.filter(n => n.examId === filters.examId)
        }
        if (filters.courseId) {
          filteredNotes = filteredNotes.filter(n => n.courseId === filters.courseId)
        }
        if (filters.subjectId) {
          filteredNotes = filteredNotes.filter(n => n.subjectId === filters.subjectId)
        }
        if (filters.classId) {
          filteredNotes = filteredNotes.filter(n => n.classId === filters.classId)
        }
        if (filters.category) {
          filteredNotes = filteredNotes.filter(n => n.category === filters.category)
        }
        if (filters.isPublic !== undefined) {
          filteredNotes = filteredNotes.filter(n => n.isPublic === filters.isPublic)
        }
        if (filters.isFree !== undefined) {
          filteredNotes = filteredNotes.filter(n => n.isFree === filters.isFree)
        }
        if (filters.language) {
          filteredNotes = filteredNotes.filter(n => n.language === filters.language)
        }
        if (filters.difficulty) {
          filteredNotes = filteredNotes.filter(n => n.difficulty === filters.difficulty)
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredNotes = filteredNotes.filter(n => 
            n.title.toLowerCase().includes(searchLower) ||
            n.description?.toLowerCase().includes(searchLower) ||
            n.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
            n.uploadedBy.toLowerCase().includes(searchLower)
          )
        }
        if (filters.tags && filters.tags.length > 0) {
          filteredNotes = filteredNotes.filter(n => 
            filters.tags!.some(tag => n.tags.includes(tag))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredNotes.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredNotes)
    }, 100)
  })
}

export const getPDFNoteById = (id: string): Promise<PDFNote | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const note = mockPDFNotes.find(n => n.id === id) || null
      resolve(note)
    }, 100)
  })
}

export const getPDFNoteStats = (): Promise<PDFNoteStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: PDFNoteStats = {
        totalNotes: mockPDFNotes.length,
        publicNotes: mockPDFNotes.filter(n => n.isPublic).length,
        freeNotes: mockPDFNotes.filter(n => n.isFree).length,
        totalDownloads: mockPDFNotes.reduce((sum, n) => sum + n.downloadCount, 0),
        totalViews: mockPDFNotes.reduce((sum, n) => sum + n.viewCount, 0),
        averageRating: mockPDFNotes.reduce((sum, n) => sum + n.rating, 0) / mockPDFNotes.length,
        totalFileSize: mockPDFNotes.reduce((sum, n) => sum + n.fileSize, 0)
      }
      resolve(stats)
    }, 100)
  })
}

export const createPDFNote = (data: CreatePDFNoteRequest): Promise<PDFNote> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newNote: PDFNote = {
        id: Date.now().toString(),
        ...data,
        downloadCount: 0,
        viewCount: 0,
        rating: 0,
        totalRatings: 0,
        uploadedBy: 'Admin User',
        uploadedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1
      }
      mockPDFNotes.push(newNote)
      resolve(newNote)
    }, 200)
  })
}

export const updatePDFNote = (data: UpdatePDFNoteRequest): Promise<PDFNote> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockPDFNotes.findIndex(n => n.id === data.id)
      if (index === -1) {
        reject(new Error('PDF Note not found'))
        return
      }
      
      mockPDFNotes[index] = {
        ...mockPDFNotes[index],
        ...data,
        updatedAt: new Date().toISOString(),
        version: mockPDFNotes[index].version + 1
      }
      resolve(mockPDFNotes[index])
    }, 200)
  })
}

export const deletePDFNote = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockPDFNotes.findIndex(n => n.id === id)
      if (index === -1) {
        reject(new Error('PDF Note not found'))
        return
      }
      
      mockPDFNotes.splice(index, 1)
      resolve()
    }, 200)
  })
}
