export interface Subject {
  id: string
  name: string
  description: string
  courseId: string
  courseName: string
  examId: string
  examName: string
  order: number
  isActive: boolean
  totalClasses: number
  totalDuration: number // in minutes
  createdAt: string
  updatedAt: string
  color?: string
  icon?: string
}

export interface CreateSubjectRequest {
  name: string
  description: string
  courseId: string
  order: number
  isActive: boolean
  color?: string
  icon?: string
}

export interface UpdateSubjectRequest extends Partial<CreateSubjectRequest> {
  id: string
}

export interface SubjectFilters {
  courseId?: string
  examId?: string
  isActive?: boolean
  search?: string
  sortBy?: 'name' | 'order' | 'totalClasses' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}
