import { 
  Notification, 
  CreateNotificationRequest, 
  UpdateNotificationRequest, 
  NotificationFilters, 
  NotificationStats,
  NotificationTemplate,
  NotificationGroup,
  NotificationCampaign
} from '@/types/notification'

// Mock notification templates
const mockTemplates: NotificationTemplate[] = [
  {
    id: '1',
    name: 'Class Reminder',
    description: 'Remind students about upcoming classes',
    category: 'CLASS',
    type: 'INFO',
    title: 'Class Reminder: {{className}}',
    message: 'Your class "{{className}}" is scheduled for {{classDate}} at {{classTime}}. Don\'t miss it!',
    variables: [
      { key: 'className', label: 'Class Name', type: 'TEXT', required: true },
      { key: 'classDate', label: 'Class Date', type: 'DATE', required: true },
      { key: 'classTime', label: 'Class Time', type: 'TEXT', required: true }
    ],
    defaultChannels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true },
      { type: 'SMS', enabled: false }
    ],
    defaultPriority: 'MEDIUM',
    defaultRecipientType: 'STUDENTS',
    usageCount: 45,
    lastUsed: '2024-01-20T10:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T12:00:00Z',
    createdBy: 'admin',
    isActive: true
  },
  {
    id: '2',
    name: 'Payment Due',
    description: 'Notify students about pending payments',
    category: 'PAYMENT',
    type: 'WARNING',
    title: 'Payment Due: {{courseName}}',
    message: 'Your payment of {{amount}} for "{{courseName}}" is due on {{dueDate}}. Please complete the payment to continue accessing the course.',
    variables: [
      { key: 'courseName', label: 'Course Name', type: 'TEXT', required: true },
      { key: 'amount', label: 'Amount', type: 'TEXT', required: true },
      { key: 'dueDate', label: 'Due Date', type: 'DATE', required: true }
    ],
    defaultChannels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true },
      { type: 'SMS', enabled: true }
    ],
    defaultPriority: 'HIGH',
    defaultRecipientType: 'STUDENTS',
    usageCount: 23,
    lastUsed: '2024-01-18T14:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T16:45:00Z',
    createdBy: 'admin',
    isActive: true
  },
  {
    id: '3',
    name: 'Exam Results',
    description: 'Announce exam results to students',
    category: 'EXAM',
    type: 'SUCCESS',
    title: 'Exam Results: {{examName}}',
    message: 'Your results for "{{examName}}" are now available. You scored {{score}}% ({{grade}}). Check your detailed report in the dashboard.',
    variables: [
      { key: 'examName', label: 'Exam Name', type: 'TEXT', required: true },
      { key: 'score', label: 'Score', type: 'NUMBER', required: true },
      { key: 'grade', label: 'Grade', type: 'TEXT', required: true }
    ],
    defaultChannels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true },
      { type: 'PUSH', enabled: true }
    ],
    defaultPriority: 'HIGH',
    defaultRecipientType: 'STUDENTS',
    usageCount: 67,
    lastUsed: '2024-01-19T09:15:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-12T11:20:00Z',
    createdBy: 'admin',
    isActive: true
  }
]

// Mock notification groups
const mockGroups: NotificationGroup[] = [
  {
    id: '1',
    name: 'JEE Main Students',
    description: 'All students enrolled in JEE Main courses',
    type: 'DYNAMIC',
    members: [],
    criteria: {
      userType: 'STUDENT',
      examIds: ['1'], // JEE Main
      subscriptionStatus: 'ACTIVE'
    },
    memberCount: 245,
    isActive: true,
    allowSelfSubscribe: false,
    allowSelfUnsubscribe: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    createdBy: 'admin',
    tags: ['students', 'jee-main', 'active']
  },
  {
    id: '2',
    name: 'Premium Subscribers',
    description: 'Students with premium subscription',
    type: 'DYNAMIC',
    members: [],
    criteria: {
      userType: 'STUDENT',
      subscriptionStatus: 'ACTIVE',
      tags: ['premium']
    },
    memberCount: 89,
    isActive: true,
    allowSelfSubscribe: false,
    allowSelfUnsubscribe: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-18T14:20:00Z',
    createdBy: 'admin',
    tags: ['premium', 'subscribers']
  },
  {
    id: '3',
    name: 'All Instructors',
    description: 'All teaching staff and instructors',
    type: 'DYNAMIC',
    members: [],
    criteria: {
      userType: 'INSTRUCTOR'
    },
    memberCount: 12,
    isActive: true,
    allowSelfSubscribe: false,
    allowSelfUnsubscribe: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T09:45:00Z',
    createdBy: 'admin',
    tags: ['instructors', 'staff']
  }
]

// Mock notifications
const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'JEE Main Physics Class Tomorrow',
    message: 'Don\'t forget about your JEE Main Physics class on Laws of Motion scheduled for tomorrow at 10:00 AM.',
    type: 'INFO',
    category: 'CLASS',
    priority: 'MEDIUM',
    recipientType: 'GROUP',
    recipients: ['1'], // JEE Main Students group
    recipientCount: 245,
    channels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true, config: { subject: 'Class Reminder - JEE Main Physics' } },
      { type: 'SMS', enabled: false }
    ],
    content: {
      shortText: 'JEE Main Physics class tomorrow at 10:00 AM',
      fullText: 'Don\'t forget about your JEE Main Physics class on Laws of Motion scheduled for tomorrow at 10:00 AM. The class will cover Newton\'s laws with practical examples and problem-solving techniques.',
      actionButtons: [
        {
          id: 'join',
          label: 'Join Class',
          type: 'BUTTON',
          action: '/admin/live-classes/1',
          style: 'PRIMARY',
          trackClicks: true
        },
        {
          id: 'reschedule',
          label: 'Request Reschedule',
          type: 'BUTTON',
          action: '/admin/classes/reschedule/1',
          style: 'SECONDARY',
          trackClicks: true
        }
      ]
    },
    scheduledAt: '2024-01-24T18:00:00Z',
    sendImmediately: false,
    timezone: 'Asia/Kolkata',
    status: 'SENT',
    deliveryStatus: {
      total: 245,
      sent: 245,
      delivered: 238,
      read: 189,
      failed: 7,
      clicked: 156
    },
    readBy: [], // Would contain user IDs
    clickedBy: [], // Would contain user IDs
    dismissedBy: [], // Would contain user IDs
    createdAt: '2024-01-23T15:30:00Z',
    updatedAt: '2024-01-24T18:05:00Z',
    createdBy: 'admin',
    sentAt: '2024-01-24T18:00:00Z',
    analytics: {
      openRate: 77.1,
      clickRate: 63.7,
      dismissalRate: 12.3,
      avgReadTime: 45
    },
    tags: ['class-reminder', 'jee-main', 'physics'],
    template: '1'
  },
  {
    id: '2',
    title: 'Payment Due: JEE Advanced Mathematics',
    message: 'Your payment of ₹2,999 for JEE Advanced Mathematics course is due on January 28, 2024.',
    type: 'WARNING',
    category: 'PAYMENT',
    priority: 'HIGH',
    recipientType: 'SPECIFIC',
    recipients: ['student_123', 'student_456', 'student_789'],
    recipientCount: 3,
    channels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true, config: { subject: 'Payment Due - JEE Advanced Mathematics' } },
      { type: 'SMS', enabled: true }
    ],
    content: {
      shortText: 'Payment of ₹2,999 due on Jan 28',
      fullText: 'Your payment of ₹2,999 for JEE Advanced Mathematics course is due on January 28, 2024. Please complete the payment to continue accessing the course materials and live classes.',
      actionButtons: [
        {
          id: 'pay_now',
          label: 'Pay Now',
          type: 'BUTTON',
          action: '/admin/payments/pay/course_123',
          style: 'PRIMARY',
          trackClicks: true
        },
        {
          id: 'view_details',
          label: 'View Details',
          type: 'BUTTON',
          action: '/admin/payments/details/course_123',
          style: 'SECONDARY',
          trackClicks: true
        }
      ]
    },
    sendImmediately: true,
    timezone: 'Asia/Kolkata',
    status: 'SENT',
    deliveryStatus: {
      total: 3,
      sent: 3,
      delivered: 3,
      read: 2,
      failed: 0,
      clicked: 1
    },
    readBy: [],
    clickedBy: [],
    dismissedBy: [],
    createdAt: '2024-01-25T09:15:00Z',
    updatedAt: '2024-01-25T09:20:00Z',
    createdBy: 'admin',
    sentAt: '2024-01-25T09:16:00Z',
    analytics: {
      openRate: 66.7,
      clickRate: 33.3,
      dismissalRate: 0,
      avgReadTime: 62
    },
    tags: ['payment-due', 'jee-advanced', 'mathematics'],
    template: '2'
  },
  {
    id: '3',
    title: 'System Maintenance Tonight',
    message: 'Scheduled maintenance from 11:00 PM to 2:00 AM IST. Services may be temporarily unavailable.',
    type: 'WARNING',
    category: 'SYSTEM',
    priority: 'HIGH',
    recipientType: 'ALL',
    recipients: [],
    recipientCount: 1247,
    channels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true, config: { subject: 'Scheduled Maintenance Notice' } },
      { type: 'PUSH', enabled: true }
    ],
    content: {
      shortText: 'Maintenance tonight 11 PM - 2 AM',
      fullText: 'We will be performing scheduled maintenance tonight from 11:00 PM to 2:00 AM IST. During this time, some services may be temporarily unavailable. We apologize for any inconvenience.',
      actionButtons: [
        {
          id: 'learn_more',
          label: 'Learn More',
          type: 'LINK',
          action: 'https://status.utkrishta.com',
          style: 'SECONDARY',
          trackClicks: true
        }
      ]
    },
    scheduledAt: '2024-01-25T15:00:00Z',
    sendImmediately: false,
    timezone: 'Asia/Kolkata',
    status: 'SCHEDULED',
    deliveryStatus: {
      total: 1247,
      sent: 0,
      delivered: 0,
      read: 0,
      failed: 0,
      clicked: 0
    },
    readBy: [],
    clickedBy: [],
    dismissedBy: [],
    createdAt: '2024-01-25T10:30:00Z',
    updatedAt: '2024-01-25T10:30:00Z',
    createdBy: 'admin',
    analytics: {
      openRate: 0,
      clickRate: 0,
      dismissalRate: 0,
      avgReadTime: 0
    },
    tags: ['system', 'maintenance', 'announcement']
  },
  {
    id: '4',
    title: 'New Course Available: NEET Biology Masterclass',
    message: 'Enroll now in our comprehensive NEET Biology Masterclass with expert instructors.',
    type: 'ANNOUNCEMENT',
    category: 'MARKETING',
    priority: 'MEDIUM',
    recipientType: 'GROUP',
    recipients: ['2'], // Premium Subscribers
    recipientCount: 89,
    channels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true, config: { subject: 'New Course Launch - NEET Biology Masterclass' } },
      { type: 'PUSH', enabled: true }
    ],
    content: {
      shortText: 'New NEET Biology Masterclass available',
      fullText: 'We\'re excited to announce our new NEET Biology Masterclass! This comprehensive course covers all NEET Biology topics with expert instructors, interactive sessions, and extensive practice materials.',
      attachments: [
        {
          id: 'course_brochure',
          name: 'Course Brochure.pdf',
          type: 'PDF',
          url: '/attachments/neet-biology-brochure.pdf',
          size: 2048000
        }
      ],
      actionButtons: [
        {
          id: 'enroll_now',
          label: 'Enroll Now',
          type: 'BUTTON',
          action: '/admin/courses/enroll/neet-biology',
          style: 'PRIMARY',
          trackClicks: true
        },
        {
          id: 'view_syllabus',
          label: 'View Syllabus',
          type: 'BUTTON',
          action: '/admin/courses/syllabus/neet-biology',
          style: 'SECONDARY',
          trackClicks: true
        }
      ]
    },
    sendImmediately: true,
    timezone: 'Asia/Kolkata',
    status: 'SENT',
    deliveryStatus: {
      total: 89,
      sent: 89,
      delivered: 87,
      read: 72,
      failed: 2,
      clicked: 34
    },
    readBy: [],
    clickedBy: [],
    dismissedBy: [],
    createdAt: '2024-01-24T11:00:00Z',
    updatedAt: '2024-01-24T11:05:00Z',
    createdBy: 'admin',
    sentAt: '2024-01-24T11:01:00Z',
    analytics: {
      openRate: 80.9,
      clickRate: 38.2,
      dismissalRate: 8.9,
      avgReadTime: 78
    },
    tags: ['course-launch', 'neet', 'biology', 'marketing']
  },
  {
    id: '5',
    title: 'Test Results Published',
    message: 'Your JEE Main Mock Test #5 results are now available.',
    type: 'SUCCESS',
    category: 'EXAM',
    priority: 'HIGH',
    recipientType: 'SPECIFIC',
    recipients: ['student_101', 'student_102', 'student_103'],
    recipientCount: 3,
    channels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: true, config: { subject: 'Test Results - JEE Main Mock Test #5' } }
    ],
    content: {
      shortText: 'JEE Main Mock Test #5 results available',
      fullText: 'Your JEE Main Mock Test #5 results are now available. Check your detailed performance analysis and recommendations in your dashboard.',
      actionButtons: [
        {
          id: 'view_results',
          label: 'View Results',
          type: 'BUTTON',
          action: '/admin/tests/results/mock-test-5',
          style: 'PRIMARY',
          trackClicks: true
        },
        {
          id: 'download_report',
          label: 'Download Report',
          type: 'BUTTON',
          action: '/admin/tests/download/mock-test-5',
          style: 'SECONDARY',
          trackClicks: true
        }
      ]
    },
    sendImmediately: true,
    timezone: 'Asia/Kolkata',
    status: 'SENT',
    deliveryStatus: {
      total: 3,
      sent: 3,
      delivered: 3,
      read: 3,
      failed: 0,
      clicked: 3
    },
    readBy: [],
    clickedBy: [],
    dismissedBy: [],
    createdAt: '2024-01-25T16:30:00Z',
    updatedAt: '2024-01-25T16:35:00Z',
    createdBy: 'admin',
    sentAt: '2024-01-25T16:31:00Z',
    analytics: {
      openRate: 100,
      clickRate: 100,
      dismissalRate: 0,
      avgReadTime: 95
    },
    tags: ['test-results', 'jee-main', 'mock-test'],
    template: '3'
  }
]

export const getNotifications = (filters?: NotificationFilters): Promise<Notification[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredNotifications = [...mockNotifications]
      
      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredNotifications = filteredNotifications.filter(n => 
            n.title.toLowerCase().includes(searchLower) ||
            n.message.toLowerCase().includes(searchLower) ||
            n.tags.some(tag => tag.toLowerCase().includes(searchLower))
          )
        }
        
        if (filters.type) {
          filteredNotifications = filteredNotifications.filter(n => n.type === filters.type)
        }
        
        if (filters.category) {
          filteredNotifications = filteredNotifications.filter(n => n.category === filters.category)
        }
        
        if (filters.priority) {
          filteredNotifications = filteredNotifications.filter(n => n.priority === filters.priority)
        }
        
        if (filters.status) {
          filteredNotifications = filteredNotifications.filter(n => n.status === filters.status)
        }
        
        if (filters.recipientType) {
          filteredNotifications = filteredNotifications.filter(n => n.recipientType === filters.recipientType)
        }
        
        if (filters.createdBy) {
          filteredNotifications = filteredNotifications.filter(n => n.createdBy === filters.createdBy)
        }
        
        if (filters.dateFrom) {
          filteredNotifications = filteredNotifications.filter(n => 
            new Date(n.createdAt) >= new Date(filters.dateFrom!)
          )
        }
        
        if (filters.dateTo) {
          filteredNotifications = filteredNotifications.filter(n => 
            new Date(n.createdAt) <= new Date(filters.dateTo!)
          )
        }
        
        if (filters.tags && filters.tags.length > 0) {
          filteredNotifications = filteredNotifications.filter(n => 
            filters.tags!.some(tag => n.tags.includes(tag))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredNotifications.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredNotifications)
    }, 100)
  })
}

export const getNotificationById = (id: string): Promise<Notification | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const notification = mockNotifications.find(n => n.id === id) || null
      resolve(notification)
    }, 100)
  })
}

export const getNotificationStats = (): Promise<NotificationStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: NotificationStats = {
        totalNotifications: mockNotifications.length,
        sentToday: 2,
        sentThisWeek: 4,
        sentThisMonth: mockNotifications.length,
        statusDistribution: [
          { status: 'SENT', count: 3, percentage: 60 },
          { status: 'SCHEDULED', count: 1, percentage: 20 },
          { status: 'DRAFT', count: 1, percentage: 20 }
        ],
        channelPerformance: [
          { 
            channel: 'IN_APP', 
            sent: 1584, 
            delivered: 1547, 
            opened: 1234, 
            clicked: 789,
            deliveryRate: 97.7,
            openRate: 79.8,
            clickRate: 49.8
          },
          { 
            channel: 'EMAIL', 
            sent: 1247, 
            delivered: 1198, 
            opened: 856, 
            clicked: 423,
            deliveryRate: 96.1,
            openRate: 71.5,
            clickRate: 33.9
          },
          { 
            channel: 'SMS', 
            sent: 248, 
            delivered: 245, 
            opened: 198, 
            clicked: 67,
            deliveryRate: 98.8,
            openRate: 80.8,
            clickRate: 27.0
          }
        ],
        categoryPerformance: [
          { category: 'CLASS', count: 1, openRate: 77.1, clickRate: 63.7 },
          { category: 'PAYMENT', count: 1, openRate: 66.7, clickRate: 33.3 },
          { category: 'SYSTEM', count: 1, openRate: 0, clickRate: 0 },
          { category: 'MARKETING', count: 1, openRate: 80.9, clickRate: 38.2 },
          { category: 'EXAM', count: 1, openRate: 100, clickRate: 100 }
        ],
        recentActivity: [
          { date: '2024-01-25', sent: 2, delivered: 2, opened: 2, clicked: 1 },
          { date: '2024-01-24', sent: 2, delivered: 2, opened: 2, clicked: 2 },
          { date: '2024-01-23', sent: 1, delivered: 1, opened: 1, clicked: 1 }
        ],
        topNotifications: [
          { id: '5', title: 'Test Results Published', openRate: 100, clickRate: 100, sentCount: 3 },
          { id: '4', title: 'New Course Available', openRate: 80.9, clickRate: 38.2, sentCount: 89 },
          { id: '1', title: 'JEE Main Physics Class', openRate: 77.1, clickRate: 63.7, sentCount: 245 }
        ],
        avgOpenRate: 80.9,
        avgClickRate: 47.0,
        avgDeliveryRate: 97.5,
        totalEngagement: 2567,
        monthlyGrowth: 15.3,
        weeklyGrowth: 8.7
      }
      resolve(stats)
    }, 100)
  })
}

export const getNotificationTemplates = (): Promise<NotificationTemplate[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockTemplates)
    }, 100)
  })
}

export const getNotificationGroups = (): Promise<NotificationGroup[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockGroups)
    }, 100)
  })
}

export const createNotification = (data: CreateNotificationRequest): Promise<Notification> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newNotification: Notification = {
        id: Date.now().toString(),
        ...data,
        recipientCount: data.recipients?.length || 0,
        deliveryStatus: {
          total: data.recipients?.length || 0,
          sent: 0,
          delivered: 0,
          read: 0,
          failed: 0,
          clicked: 0
        },
        readBy: [],
        clickedBy: [],
        dismissedBy: [],
        status: data.sendImmediately ? 'SENDING' : 'SCHEDULED',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin',
        analytics: {
          openRate: 0,
          clickRate: 0,
          dismissalRate: 0,
          avgReadTime: 0
        }
      }
      mockNotifications.push(newNotification)
      resolve(newNotification)
    }, 200)
  })
}

export const updateNotification = (data: UpdateNotificationRequest): Promise<Notification> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockNotifications.findIndex(n => n.id === data.id)
      if (index === -1) {
        reject(new Error('Notification not found'))
        return
      }
      
      mockNotifications[index] = {
        ...mockNotifications[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockNotifications[index])
    }, 200)
  })
}

export const deleteNotification = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockNotifications.findIndex(n => n.id === id)
      if (index === -1) {
        reject(new Error('Notification not found'))
        return
      }
      
      mockNotifications.splice(index, 1)
      resolve()
    }, 200)
  })
}
