const jwt = require('jsonwebtoken');
const db = require('../config/database');
const { AuthenticationError, AuthorizationError } = require('../utils/errorHandler');
const { errorResponse } = require('../utils/responseHelper');

/**
 * Authentication Middleware
 * Handles JWT token verification and user authentication
 */
class AuthMiddleware {
  /**
   * Authenticate user using JWT token
   */
  async authenticate(req, res, next) {
    try {
      // Get token from header
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new AuthenticationError('Access token is required');
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      if (!token) {
        throw new AuthenticationError('Access token is required');
      }

      // Verify JWT token
      let decoded;
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET);
      } catch (error) {
        if (error.name === 'TokenExpiredError') {
          throw new AuthenticationError('Token has expired');
        } else if (error.name === 'JsonWebTokenError') {
          throw new AuthenticationError('Invalid token');
        } else {
          throw new AuthenticationError('Token verification failed');
        }
      }

      // Get user from database
      const user = await this.getUserById(decoded.id);
      
      if (!user) {
        throw new AuthenticationError('User not found');
      }

      if (user.status !== 'active') {
        throw new AuthenticationError('User account is not active');
      }

      // Check if token is blacklisted (optional - implement if needed)
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new AuthenticationError('Token has been revoked');
      }

      // Add user to request object
      req.user = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions || [],
        status: user.status,
        last_login: user.last_login
      };

      // Update last activity
      await this.updateLastActivity(user.id);

      next();
    } catch (error) {
      if (error instanceof AuthenticationError) {
        return errorResponse(res, error.message, 401);
      }
      
      console.error('Authentication error:', error);
      return errorResponse(res, 'Authentication failed', 401);
    }
  }

  /**
   * Optional authentication - doesn't fail if no token provided
   */
  async optionalAuth(req, res, next) {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(); // Continue without authentication
      }

      // Use regular authentication if token is provided
      return this.authenticate(req, res, next);
    } catch (error) {
      // Continue without authentication on error
      next();
    }
  }

  /**
   * Get user by ID from database
   */
  async getUserById(userId) {
    try {
      const query = `
        SELECT 
          au.id,
          au.username,
          au.email,
          au.first_name,
          au.last_name,
          au.status,
          au.last_login,
          r.name as role_name,
          r.code as role_code,
          r.level as role_level,
          GROUP_CONCAT(p.code) as permissions
        FROM admin_users au
        LEFT JOIN roles r ON au.role_id = r.id
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN permissions p ON rp.permission_id = p.id
        WHERE au.id = ? AND au.deleted_at IS NULL
        GROUP BY au.id
      `;

      const result = await db.query(query, [userId]);
      
      if (!result.length) {
        return null;
      }

      const user = result[0];
      
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        status: user.status,
        last_login: user.last_login,
        role: {
          name: user.role_name,
          code: user.role_code,
          level: user.role_level
        },
        permissions: user.permissions ? user.permissions.split(',') : []
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  /**
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(token) {
    try {
      // This is optional - implement if you need token blacklisting
      const query = 'SELECT id FROM blacklisted_tokens WHERE token = ?';
      const result = await db.query(query, [token]);
      return result.length > 0;
    } catch (error) {
      console.error('Error checking token blacklist:', error);
      return false; // Assume not blacklisted on error
    }
  }

  /**
   * Update user's last activity
   */
  async updateLastActivity(userId) {
    try {
      const query = `
        UPDATE admin_users 
        SET last_activity = NOW() 
        WHERE id = ?
      `;
      await db.query(query, [userId]);
    } catch (error) {
      console.error('Error updating last activity:', error);
      // Don't throw error - this is not critical
    }
  }

  /**
   * Generate JWT token
   */
  generateToken(user, expiresIn = '24h') {
    const payload = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };

    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn,
      issuer: 'utkrishta-admin',
      audience: 'utkrishta-admin-panel'
    });
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(user) {
    const payload = {
      id: user.id,
      type: 'refresh'
    };

    return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
      expiresIn: '7d',
      issuer: 'utkrishta-admin',
      audience: 'utkrishta-admin-panel'
    });
  }

  /**
   * Verify refresh token
   */
  async verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET);
      
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      const user = await this.getUserById(decoded.id);
      return user;
    } catch (error) {
      throw new AuthenticationError('Invalid refresh token');
    }
  }

  /**
   * Blacklist token (logout)
   */
  async blacklistToken(token) {
    try {
      const decoded = jwt.decode(token);
      const expiresAt = new Date(decoded.exp * 1000);

      const query = `
        INSERT INTO blacklisted_tokens (token, expires_at, created_at)
        VALUES (?, ?, NOW())
      `;
      
      await db.query(query, [token, expiresAt]);
      return true;
    } catch (error) {
      console.error('Error blacklisting token:', error);
      return false;
    }
  }

  /**
   * Clean up expired blacklisted tokens
   */
  async cleanupExpiredTokens() {
    try {
      const query = 'DELETE FROM blacklisted_tokens WHERE expires_at < NOW()';
      await db.query(query);
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId) {
    try {
      const query = `
        SELECT DISTINCT p.code
        FROM admin_users au
        JOIN roles r ON au.role_id = r.id
        JOIN role_permissions rp ON r.id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE au.id = ? AND au.deleted_at IS NULL
      `;

      const result = await db.query(query, [userId]);
      return result.map(row => row.code);
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(user, permission) {
    if (!user || !user.permissions) {
      return false;
    }

    // Super admin has all permissions
    if (user.role && user.role.code === 'super_admin') {
      return true;
    }

    return user.permissions.includes(permission);
  }

  /**
   * Check if user has any of the specified permissions
   */
  hasAnyPermission(user, permissions) {
    if (!Array.isArray(permissions)) {
      permissions = [permissions];
    }

    return permissions.some(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if user has all specified permissions
   */
  hasAllPermissions(user, permissions) {
    if (!Array.isArray(permissions)) {
      permissions = [permissions];
    }

    return permissions.every(permission => this.hasPermission(user, permission));
  }

  /**
   * Middleware to require specific permission
   */
  requirePermission(permission) {
    return (req, res, next) => {
      if (!req.user) {
        return errorResponse(res, 'Authentication required', 401);
      }

      if (!this.hasPermission(req.user, permission)) {
        return errorResponse(res, 'Insufficient permissions', 403);
      }

      next();
    };
  }

  /**
   * Middleware to require any of the specified permissions
   */
  requireAnyPermission(permissions) {
    return (req, res, next) => {
      if (!req.user) {
        return errorResponse(res, 'Authentication required', 401);
      }

      if (!this.hasAnyPermission(req.user, permissions)) {
        return errorResponse(res, 'Insufficient permissions', 403);
      }

      next();
    };
  }
}

module.exports = new AuthMiddleware();
