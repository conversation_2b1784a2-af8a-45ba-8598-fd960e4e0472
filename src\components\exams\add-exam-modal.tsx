'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X, Plus, CheckCircle, AlertCircle, Calendar, Users, Clock, Trophy } from 'lucide-react'
import { CreateExamRequest } from '@/types/exam'

interface AddExamModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (data: CreateExamRequest) => Promise<void>
}

export function AddExamModal({ isOpen, onClose, onAdd }: AddExamModalProps) {
  const [formData, setFormData] = useState<Partial<CreateExamRequest>>({
    category: 'JEE',
    level: 'INTERMEDIATE',
    duration: 180, // 3 hours default
    totalMarks: 300,
    isActive: true,
    subjects: []
  })
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [error, setError] = useState<string>()
  const [subjectInput, setSubjectInput] = useState('')

  const examCategories = [
    { value: 'JEE', label: 'JEE (Joint Entrance Examination)' },
    { value: 'NEET', label: 'NEET (National Eligibility cum Entrance Test)' },
    { value: 'CBSE', label: 'CBSE (Central Board of Secondary Education)' },
    { value: 'ICSE', label: 'ICSE (Indian Certificate of Secondary Education)' },
    { value: 'STATE_BOARD', label: 'State Board' },
    { value: 'COMPETITIVE', label: 'Competitive Exam' },
    { value: 'OTHER', label: 'Other' }
  ]

  const examLevels = [
    { value: 'BEGINNER', label: 'Beginner', description: 'Basic level concepts' },
    { value: 'INTERMEDIATE', label: 'Intermediate', description: 'Standard level preparation' },
    { value: 'ADVANCED', label: 'Advanced', description: 'Advanced level mastery' }
  ]

  const handleAddSubject = () => {
    if (subjectInput.trim() && !formData.subjects?.includes(subjectInput.trim())) {
      setFormData(prev => ({
        ...prev,
        subjects: [...(prev.subjects || []), subjectInput.trim()]
      }))
      setSubjectInput('')
    }
  }

  const handleRemoveSubject = (subjectToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      subjects: prev.subjects?.filter(subject => subject !== subjectToRemove) || []
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.description || !formData.category) {
      setError('Please fill in all required fields')
      return
    }

    setLoading(true)
    setError(undefined)

    try {
      const examData: CreateExamRequest = {
        name: formData.name!,
        description: formData.description!,
        category: formData.category!,
        level: formData.level!,
        duration: formData.duration!,
        totalMarks: formData.totalMarks!,
        passingMarks: formData.passingMarks,
        examDate: formData.examDate,
        registrationDeadline: formData.registrationDeadline,
        syllabus: formData.syllabus,
        eligibilityCriteria: formData.eligibilityCriteria,
        examPattern: formData.examPattern,
        subjects: formData.subjects || [],
        isActive: formData.isActive!
      }

      await onAdd(examData)
      setCompleted(true)
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add exam')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      category: 'JEE',
      level: 'INTERMEDIATE',
      duration: 180,
      totalMarks: 300,
      isActive: true,
      subjects: []
    })
    setLoading(false)
    setCompleted(false)
    setError(undefined)
    setSubjectInput('')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold flex items-center space-x-2">
            <Trophy className="w-5 h-5 text-blue-600" />
            <span>Add New Exam</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Exam Added Successfully!</h3>
              <p className="text-gray-600">The exam has been created and is now available in the system.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exam Name *
                    </label>
                    <Input
                      value={formData.name || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., JEE Main 2024"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <Select 
                      value={formData.category} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {examCategories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.description || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter exam description..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Level *
                  </label>
                  <Select 
                    value={formData.level} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, level: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {examLevels.map((level) => (
                        <SelectItem key={level.value} value={level.value}>
                          <div>
                            <div className="font-medium">{level.label}</div>
                            <div className="text-xs text-gray-500">{level.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Exam Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Exam Details</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Duration (minutes) *
                    </label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        type="number"
                        value={formData.duration || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                        placeholder="180"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Total Marks *
                    </label>
                    <Input
                      type="number"
                      value={formData.totalMarks || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, totalMarks: parseInt(e.target.value) || 0 }))}
                      placeholder="300"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Passing Marks
                    </label>
                    <Input
                      type="number"
                      value={formData.passingMarks || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, passingMarks: parseInt(e.target.value) || undefined }))}
                      placeholder="120"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exam Date
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        type="date"
                        value={formData.examDate || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, examDate: e.target.value }))}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Registration Deadline
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        type="date"
                        value={formData.registrationDeadline || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, registrationDeadline: e.target.value }))}
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Syllabus
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.syllabus || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, syllabus: e.target.value }))}
                    placeholder="Enter syllabus details..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Eligibility Criteria
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    value={formData.eligibilityCriteria || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, eligibilityCriteria: e.target.value }))}
                    placeholder="Enter eligibility criteria..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Exam Pattern
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    value={formData.examPattern || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, examPattern: e.target.value }))}
                    placeholder="Enter exam pattern details..."
                  />
                </div>

                {/* Subjects */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subjects
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <Input
                      value={subjectInput}
                      onChange={(e) => setSubjectInput(e.target.value)}
                      placeholder="Add a subject"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddSubject())}
                    />
                    <Button type="button" onClick={handleAddSubject} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.subjects?.map((subject, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                        <span>{subject}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveSubject(subject)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <label htmlFor="isActive" className="text-sm text-gray-700">
                  Make this exam active
                </label>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>{loading ? 'Adding...' : 'Add Exam'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
