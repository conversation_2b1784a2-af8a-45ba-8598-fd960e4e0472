import { StatsCards } from '@/components/dashboard/stats-cards'
import { RevenueChart } from '@/components/dashboard/revenue-chart'
import { EngagementChart } from '@/components/dashboard/engagement-chart'
import { PopularCourses } from '@/components/dashboard/popular-courses'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import {
  getDashboardStats,
  getRevenueData,
  getEngagementData,
  getPopularCourses,
  getRecentActivity
} from '@/lib/dashboard-data'

export default function DashboardPage() {
  const stats = getDashboardStats()
  const revenueData = getRevenueData()
  const engagementData = getEngagementData()
  const popularCourses = getPopularCourses()
  const recentActivity = getRecentActivity()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's what's happening at Utkrishta Classes.</p>
        </div>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleString('en-IN', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>

      {/* Stats Cards */}
      <StatsCards stats={stats} />

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <RevenueChart data={revenueData} />
        <EngagementChart data={engagementData} />
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PopularCourses courses={popularCourses} />
        <RecentActivity activities={recentActivity} />
      </div>
    </div>
  )
}
