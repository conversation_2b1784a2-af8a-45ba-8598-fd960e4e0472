'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Upload, X, FileText, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FileUploadProps {
  accept?: string
  maxSize?: number // in bytes
  multiple?: boolean
  onFileSelect?: (files: File[]) => void
  onFileRemove?: (index: number) => void
  className?: string
  disabled?: boolean
}

interface UploadedFile {
  file: File
  preview?: string
  error?: string
}

export function FileUpload({
  accept = '.pdf',
  maxSize = 10 * 1024 * 1024, // 10MB default
  multiple = false,
  onFileSelect,
  onFileRemove,
  className,
  disabled = false
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    if (maxSize && file.size > maxSize) {
      return `File size must be less than ${formatFileSize(maxSize)}`
    }
    
    if (accept && !accept.split(',').some(type => 
      file.type.includes(type.replace('.', '').trim()) || 
      file.name.toLowerCase().endsWith(type.trim())
    )) {
      return `File type not supported. Accepted: ${accept}`
    }
    
    return null
  }

  const handleFiles = (fileList: FileList) => {
    const newFiles: UploadedFile[] = []
    
    Array.from(fileList).forEach(file => {
      const error = validateFile(file)
      newFiles.push({ file, error: error || undefined })
    })

    if (multiple) {
      setFiles(prev => [...prev, ...newFiles])
      onFileSelect?.(newFiles.map(f => f.file))
    } else {
      setFiles(newFiles.slice(0, 1))
      onFileSelect?.(newFiles.slice(0, 1).map(f => f.file))
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files)
    }
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
    onFileRemove?.(index)
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className={cn('w-full', className)}>
      <Card
        className={cn(
          'border-2 border-dashed transition-colors',
          dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Upload className={cn(
            'w-12 h-12 mb-4',
            dragActive ? 'text-blue-500' : 'text-gray-400'
          )} />
          
          <div className="text-center mb-4">
            <p className="text-lg font-medium text-gray-900 mb-1">
              Drop files here or click to upload
            </p>
            <p className="text-sm text-gray-500">
              {accept} files up to {formatFileSize(maxSize)}
            </p>
          </div>

          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
          >
            Select Files
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={handleChange}
            className="hidden"
            disabled={disabled}
          />
        </CardContent>
      </Card>

      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          {files.map((uploadedFile, index) => (
            <Card key={index} className={cn(
              'p-3',
              uploadedFile.error && 'border-red-200 bg-red-50'
            )}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <FileText className={cn(
                    'w-5 h-5',
                    uploadedFile.error ? 'text-red-500' : 'text-blue-500'
                  )} />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {uploadedFile.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(uploadedFile.file.size)}
                    </p>
                    {uploadedFile.error && (
                      <div className="flex items-center space-x-1 mt-1">
                        <AlertCircle className="w-3 h-3 text-red-500" />
                        <p className="text-xs text-red-600">{uploadedFile.error}</p>
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(index)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
