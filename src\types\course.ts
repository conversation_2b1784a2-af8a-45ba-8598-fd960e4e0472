export interface Course {
  id: string
  name: string
  description: string
  examId: string
  examName: string
  price: number
  isFree: boolean
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  thumbnail?: string
  instructorName: string
  instructorBio?: string
  duration?: number // in hours
  totalLessons: number
  totalStudents: number
  rating: number
  totalRatings: number
  createdAt: string
  updatedAt: string
  tags: string[]
  features: string[]
}

export interface CreateCourseRequest {
  name: string
  description: string
  examId: string
  price: number
  isFree: boolean
  status: Course['status']
  thumbnail?: string
  instructorName: string
  instructorBio?: string
  duration?: number
  tags: string[]
  features: string[]
}

export interface UpdateCourseRequest extends Partial<CreateCourseRequest> {
  id: string
}

export interface CourseFilters {
  examId?: string
  status?: Course['status']
  isFree?: boolean
  search?: string
  sortBy?: 'name' | 'price' | 'createdAt' | 'totalStudents' | 'rating'
  sortOrder?: 'asc' | 'desc'
}
