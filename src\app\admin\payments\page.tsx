'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Eye, 
  RefreshCw,
  CreditCard,
  DollarSign,
  TrendingUp,
  Users,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Download,
  Filter,
  MoreHorizontal,
  Receipt,
  Banknote,
  Wallet,
  Building,
  Smartphone,
  Globe,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import { Payment, PaymentFilters, PaymentStats, CreatePaymentRequest } from '@/types/payment'
import { Student } from '@/types/student'
import { getPayments, getPaymentStats, createPayment } from '@/lib/payment-data'
import { getStudents } from '@/lib/student-data'
import { AddPaymentModal } from '@/components/payments/add-payment-modal'

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [stats, setStats] = useState<PaymentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedPayments, setSelectedPayments] = useState<string[]>([])
  const [filters, setFilters] = useState<PaymentFilters>({
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadPayments()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [paymentsData, studentsData, statsData] = await Promise.all([
        getPayments(),
        getStudents(),
        getPaymentStats()
      ])
      setPayments(paymentsData)
      setStudents(studentsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadPayments = async () => {
    try {
      const data = await getPayments(filters)
      setPayments(data)
    } catch (error) {
      console.error('Failed to load payments:', error)
    }
  }

  const handleFilterChange = (key: keyof PaymentFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleAddPayment = async (data: CreatePaymentRequest) => {
    try {
      await createPayment(data)
      await loadPayments()
      const statsData = await getPaymentStats()
      setStats(statsData)
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add payment:', error)
      throw error
    }
  }

  const handleSelectPayment = (paymentId: string) => {
    setSelectedPayments(prev => 
      prev.includes(paymentId) 
        ? prev.filter(id => id !== paymentId)
        : [...prev, paymentId]
    )
  }

  const handleSelectAll = () => {
    setSelectedPayments(
      selectedPayments.length === payments.length 
        ? [] 
        : payments.map(p => p.id)
    )
  }

  const formatCurrency = (amount: number, currency: string = 'INR'): string => {
    const symbol = currency === 'INR' ? '₹' : currency === 'USD' ? '$' : '€'
    return `${symbol}${amount.toLocaleString()}`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS': return 'bg-green-100 text-green-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'PROCESSING': return 'bg-blue-100 text-blue-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'CANCELLED': return 'bg-gray-100 text-gray-800'
      case 'REFUNDED': return 'bg-purple-100 text-purple-800'
      case 'PARTIALLY_REFUNDED': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS': return <CheckCircle className="w-4 h-4" />
      case 'PENDING': return <Clock className="w-4 h-4" />
      case 'PROCESSING': return <RefreshCw className="w-4 h-4" />
      case 'FAILED': return <XCircle className="w-4 h-4" />
      case 'CANCELLED': return <XCircle className="w-4 h-4" />
      case 'REFUNDED': return <ArrowDownRight className="w-4 h-4" />
      case 'PARTIALLY_REFUNDED': return <ArrowDownRight className="w-4 h-4" />
      default: return <AlertTriangle className="w-4 h-4" />
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'CREDIT_CARD':
      case 'DEBIT_CARD': return <CreditCard className="w-4 h-4" />
      case 'UPI': return <Smartphone className="w-4 h-4" />
      case 'NET_BANKING': return <Building className="w-4 h-4" />
      case 'WALLET': return <Wallet className="w-4 h-4" />
      case 'CASH': return <Banknote className="w-4 h-4" />
      default: return <DollarSign className="w-4 h-4" />
    }
  }

  const getItemTypeColor = (type: string) => {
    switch (type) {
      case 'COURSE': return 'bg-blue-100 text-blue-800'
      case 'SUBSCRIPTION': return 'bg-purple-100 text-purple-800'
      case 'TEST_SERIES': return 'bg-green-100 text-green-800'
      case 'BOOK': return 'bg-orange-100 text-orange-800'
      case 'LIVE_CLASS': return 'bg-red-100 text-red-800'
      case 'CONSULTATION': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading payments...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Payments Management</h1>
          <p className="text-gray-600 mt-1">Track transactions, subscriptions, and financial analytics</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </Button>
          <Button variant="outline" className="flex items-center space-x-2">
            <RefreshCw className="w-4 h-4" />
            <span>Sync</span>
          </Button>
          <Button 
            className="flex items-center space-x-2"
            onClick={() => setShowAddModal(true)}
          >
            <Plus className="w-4 h-4" />
            <span>Manual Payment</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-8 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +{stats.monthlyGrowth}%
                </span>
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transactions</CardTitle>
              <Receipt className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTransactions}</div>
              <p className="text-xs text-muted-foreground">Total processed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.successRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">{stats.successfulTransactions} successful</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.failedTransactions}</div>
              <p className="text-xs text-muted-foreground">{stats.failureRate.toFixed(1)}% failure rate</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Order</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(Math.round(stats.averageOrderValue))}</div>
              <p className="text-xs text-muted-foreground">Average value</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Subscriptions</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeSubscriptions}</div>
              <p className="text-xs text-muted-foreground">Active plans</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Refunds</CardTitle>
              <ArrowDownRight className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalRefunds}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-600">{formatCurrency(stats.refundAmount)}</span>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.monthlyRevenue)}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search by student, transaction ID, or item name..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="SUCCESS">Success</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="PROCESSING">Processing</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="REFUNDED">Refunded</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.paymentMethod || 'all'} onValueChange={(value) => handleFilterChange('paymentMethod', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Methods</SelectItem>
                <SelectItem value="UPI">UPI</SelectItem>
                <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                <SelectItem value="DEBIT_CARD">Debit Card</SelectItem>
                <SelectItem value="NET_BANKING">Net Banking</SelectItem>
                <SelectItem value="WALLET">Wallet</SelectItem>
                <SelectItem value="CASH">Cash</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>More Filters</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Bulk Actions */}
          {selectedPayments.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  {selectedPayments.length} payment{selectedPayments.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline">
                    <Receipt className="w-4 h-4 mr-1" />
                    Generate Receipt
                  </Button>
                  <Button size="sm" variant="outline">
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Retry Failed
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
            </div>
          )}

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedPayments.length === payments.length && payments.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300"
                  />
                </TableHead>
                <TableHead>Transaction Details</TableHead>
                <TableHead>Student & Item</TableHead>
                <TableHead>Payment Info</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedPayments.includes(payment.id)}
                      onChange={() => handleSelectPayment(payment.id)}
                      className="rounded border-gray-300"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium text-sm">{payment.transactionId}</div>
                      <div className="text-xs text-gray-500">Order: {payment.orderId}</div>
                      {payment.gatewayTransactionId && (
                        <div className="text-xs text-gray-400">
                          Gateway: {payment.gatewayTransactionId.substring(0, 20)}...
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{payment.studentName}</div>
                      <div className="text-sm text-gray-500">{payment.studentEmail}</div>
                      <div className="flex items-center space-x-1">
                        <Badge className={getItemTypeColor(payment.itemType)} variant="outline">
                          {payment.itemType.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-600 line-clamp-1">{payment.itemName}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        {getPaymentMethodIcon(payment.paymentMethod)}
                        <span className="text-sm font-medium">
                          {payment.paymentMethod.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">{payment.paymentGateway}</div>
                      {payment.discountCode && (
                        <div className="text-xs text-green-600">
                          Discount: {payment.discountCode}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-semibold">{formatCurrency(payment.finalAmount, payment.currency)}</div>
                      {payment.originalAmount !== payment.finalAmount && (
                        <div className="text-xs text-gray-500 line-through">
                          {formatCurrency(payment.originalAmount, payment.currency)}
                        </div>
                      )}
                      {payment.discountAmount && (
                        <div className="text-xs text-green-600">
                          -{formatCurrency(payment.discountAmount, payment.currency)}
                        </div>
                      )}
                      {payment.refundAmount && (
                        <div className="text-xs text-red-600">
                          Refunded: {formatCurrency(payment.refundAmount, payment.currency)}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(payment.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(payment.status)}
                          <span>{payment.status}</span>
                        </div>
                      </Badge>
                    </div>
                    {payment.failureReason && (
                      <div className="text-xs text-red-600 mt-1">
                        {payment.failureReason}
                      </div>
                    )}
                    {payment.refundReason && (
                      <div className="text-xs text-purple-600 mt-1">
                        {payment.refundReason}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {new Date(payment.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(payment.createdAt).toLocaleTimeString()}
                      </div>
                      {payment.paidAt && payment.status === 'SUCCESS' && (
                        <div className="text-xs text-green-600">
                          Paid: {new Date(payment.paidAt).toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button variant="outline" size="sm" title="View Details">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Receipt">
                        <Receipt className="w-4 h-4" />
                      </Button>
                      {payment.status === 'FAILED' && (
                        <Button variant="outline" size="sm" title="Retry Payment">
                          <RefreshCw className="w-4 h-4" />
                        </Button>
                      )}
                      {payment.status === 'SUCCESS' && (
                        <Button variant="outline" size="sm" title="Refund">
                          <ArrowDownRight className="w-4 h-4" />
                        </Button>
                      )}
                      <Button variant="outline" size="sm" title="More Options">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {payments.length === 0 && (
            <div className="text-center py-8">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No payments found matching your filters.</p>
              <Button onClick={() => setShowAddModal(true)}>
                Add manual payment
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Payment Modal */}
      <AddPaymentModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddPayment}
        students={students}
      />
    </div>
  )
}
