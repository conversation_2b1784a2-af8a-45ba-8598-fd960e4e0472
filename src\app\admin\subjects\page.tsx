'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Plus, Search, Edit, Trash2, BookOpen, Clock, Play, Users } from 'lucide-react'
import { Subject, SubjectFilters, CreateSubjectRequest } from '@/types/subject'
import { Course } from '@/types/course'
import { Exam } from '@/types/exam'
import { getSubjects, deleteSubject, createSubject } from '@/lib/subject-data'
import { getCourses } from '@/lib/course-data'
import { getExams } from '@/lib/exam-data'
import { AddSubjectModal } from '@/components/subjects/add-subject-modal'

export default function SubjectsPage() {
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [filters, setFilters] = useState<SubjectFilters>({
    search: '',
    sortBy: 'order',
    sortOrder: 'asc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadSubjects()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [subjectsData, coursesData, examsData] = await Promise.all([
        getSubjects(),
        getCourses(),
        getExams()
      ])
      setSubjects(subjectsData)
      setCourses(coursesData)
      setExams(examsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSubjects = async () => {
    try {
      const data = await getSubjects(filters)
      setSubjects(data)
    } catch (error) {
      console.error('Failed to load subjects:', error)
    }
  }

  const handleDeleteSubject = async (id: string) => {
    if (confirm('Are you sure you want to delete this subject? This will also affect all associated classes.')) {
      try {
        await deleteSubject(id)
        await loadSubjects()
      } catch (error) {
        console.error('Failed to delete subject:', error)
      }
    }
  }

  const handleAddSubject = async (data: CreateSubjectRequest) => {
    try {
      await createSubject(data)
      await loadSubjects()
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add subject:', error)
      throw error
    }
  }

  const handleFilterChange = (key: keyof SubjectFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading subjects...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Subject Management</h1>
          <p className="text-gray-600 mt-1">Organize course content into subjects and topics</p>
        </div>
        <Button
          className="flex items-center space-x-2"
          onClick={() => setShowAddModal(true)}
        >
          <Plus className="w-4 h-4" />
          <span>Add New Subject</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subjects</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subjects.length}</div>
            <p className="text-xs text-muted-foreground">Across all courses</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subjects</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subjects.filter(s => s.isActive).length}</div>
            <p className="text-xs text-muted-foreground">Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subjects.reduce((sum, subject) => sum + subject.totalClasses, 0)}</div>
            <p className="text-xs text-muted-foreground">All classes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(subjects.reduce((sum, subject) => sum + subject.totalDuration, 0))}
            </div>
            <p className="text-xs text-muted-foreground">Content duration</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search subjects by name, description, or course..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.courseId || 'all'} onValueChange={(value) => handleFilterChange('courseId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Filter by Course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {courses.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.examId || 'all'} onValueChange={(value) => handleFilterChange('examId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by Exam" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Exams</SelectItem>
                {exams.map((exam) => (
                  <SelectItem key={exam.id} value={exam.id}>
                    {exam.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.isActive?.toString() || 'all'} onValueChange={(value) => handleFilterChange('isActive', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Subject Details</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Order</TableHead>
                <TableHead>Classes</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subjects.map((subject) => (
                <TableRow key={subject.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold"
                        style={{ backgroundColor: subject.color }}
                      >
                        {subject.icon || subject.name.charAt(0)}
                      </div>
                      <div>
                        <div className="font-medium">{subject.name}</div>
                        <div className="text-sm text-gray-500 mt-1">{subject.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-sm">{subject.courseName}</div>
                      <Badge variant="outline" className="mt-1">{subject.examName}</Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">#{subject.order}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Play className="w-3 h-3 text-gray-400" />
                      <span>{subject.totalClasses}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <span>{formatDuration(subject.totalDuration)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={subject.isActive ? 'success' : 'secondary'}>
                      {subject.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDeleteSubject(subject.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {subjects.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No subjects found matching your filters.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Subject Modal */}
      <AddSubjectModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddSubject}
        courses={courses}
        exams={exams}
      />
    </div>
  )
}
