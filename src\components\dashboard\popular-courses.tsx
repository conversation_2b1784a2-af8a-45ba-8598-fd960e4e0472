'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Star, Users, IndianRupee } from 'lucide-react'
import { PopularCourse } from '@/types/dashboard'

interface PopularCoursesProps {
  courses: PopularCourse[]
}

export function PopularCourses({ courses }: PopularCoursesProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">Popular Courses</CardTitle>
        <p className="text-sm text-gray-500">Top performing courses by enrollment and revenue</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {courses.map((course, index) => (
            <div key={course.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold text-sm">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{course.name}</h4>
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Users className="w-3 h-3" />
                      <span>{course.students} students</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <IndianRupee className="w-3 h-3" />
                      <span>₹{(course.revenue / 1000).toFixed(0)}K</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm font-medium text-gray-900">{course.rating}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
