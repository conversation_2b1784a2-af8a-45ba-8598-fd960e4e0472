'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  X, 
  Plus, 
  CheckCircle, 
  AlertCircle, 
  Bell, 
  Mail, 
  Smartphone, 
  BellRing,
  Users,
  Calendar,
  Clock,
  Send,
  Eye,
  Target,
  Globe,
  MessageSquare
} from 'lucide-react'
import { CreateNotificationRequest, NotificationTemplate, NotificationGroup } from '@/types/notification'

interface AddNotificationModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (data: CreateNotificationRequest) => Promise<void>
  templates: NotificationTemplate[]
  groups: NotificationGroup[]
}

export function AddNotificationModal({ 
  isOpen, 
  onClose, 
  onAdd, 
  templates, 
  groups 
}: AddNotificationModalProps) {
  const [formData, setFormData] = useState<Partial<CreateNotificationRequest>>({
    type: 'INFO',
    category: 'GENERAL',
    priority: 'MEDIUM',
    recipientType: 'ALL',
    channels: [
      { type: 'IN_APP', enabled: true },
      { type: 'EMAIL', enabled: false },
      { type: 'SMS', enabled: false },
      { type: 'PUSH', enabled: false }
    ],
    content: {
      shortText: '',
      actionButtons: []
    },
    sendImmediately: true,
    timezone: 'Asia/Kolkata',
    tags: []
  })
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [error, setError] = useState<string>()
  const [tagInput, setTagInput] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')

  const notificationTypes = [
    { value: 'INFO', label: 'Information', color: 'bg-blue-100 text-blue-800' },
    { value: 'SUCCESS', label: 'Success', color: 'bg-green-100 text-green-800' },
    { value: 'WARNING', label: 'Warning', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'ERROR', label: 'Error', color: 'bg-red-100 text-red-800' },
    { value: 'ANNOUNCEMENT', label: 'Announcement', color: 'bg-purple-100 text-purple-800' }
  ]

  const categories = [
    { value: 'SYSTEM', label: 'System' },
    { value: 'ACADEMIC', label: 'Academic' },
    { value: 'PAYMENT', label: 'Payment' },
    { value: 'CLASS', label: 'Class' },
    { value: 'EXAM', label: 'Exam' },
    { value: 'GENERAL', label: 'General' },
    { value: 'MARKETING', label: 'Marketing' }
  ]

  const priorities = [
    { value: 'LOW', label: 'Low Priority', color: 'bg-gray-100 text-gray-800' },
    { value: 'MEDIUM', label: 'Medium Priority', color: 'bg-blue-100 text-blue-800' },
    { value: 'HIGH', label: 'High Priority', color: 'bg-orange-100 text-orange-800' },
    { value: 'URGENT', label: 'Urgent', color: 'bg-red-100 text-red-800' }
  ]

  const recipientTypes = [
    { value: 'ALL', label: 'All Users', description: 'Send to all students, instructors, and admins' },
    { value: 'STUDENTS', label: 'All Students', description: 'Send to all registered students' },
    { value: 'INSTRUCTORS', label: 'All Instructors', description: 'Send to all instructors and teachers' },
    { value: 'ADMINS', label: 'All Admins', description: 'Send to all admin users' },
    { value: 'SPECIFIC', label: 'Specific Users', description: 'Select individual users' },
    { value: 'GROUP', label: 'User Groups', description: 'Send to predefined user groups' }
  ]

  const channelOptions = [
    { type: 'IN_APP', label: 'In-App Notification', icon: Bell, description: 'Show in application' },
    { type: 'EMAIL', label: 'Email', icon: Mail, description: 'Send via email' },
    { type: 'SMS', label: 'SMS', icon: Smartphone, description: 'Send text message' },
    { type: 'PUSH', label: 'Push Notification', icon: BellRing, description: 'Mobile push notification' },
    { type: 'WHATSAPP', label: 'WhatsApp', icon: MessageSquare, description: 'Send via WhatsApp' }
  ]

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      setFormData(prev => ({
        ...prev,
        title: template.title,
        message: template.message,
        type: template.type,
        category: template.category,
        priority: template.defaultPriority,
        recipientType: template.defaultRecipientType,
        channels: template.defaultChannels,
        template: templateId
      }))
      setSelectedTemplate(templateId)
    }
  }

  const handleChannelToggle = (channelType: string, enabled: boolean) => {
    setFormData(prev => ({
      ...prev,
      channels: prev.channels?.map(channel => 
        channel.type === channelType 
          ? { ...channel, enabled }
          : channel
      ) || []
    }))
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title || !formData.message) {
      setError('Please fill in title and message')
      return
    }

    if (!formData.channels?.some(c => c.enabled)) {
      setError('Please select at least one delivery channel')
      return
    }

    if (formData.recipientType === 'SPECIFIC' && (!formData.recipients || formData.recipients.length === 0)) {
      setError('Please select recipients for specific user targeting')
      return
    }

    if (formData.recipientType === 'GROUP' && (!formData.recipients || formData.recipients.length === 0)) {
      setError('Please select user groups')
      return
    }

    if (!formData.sendImmediately && !formData.scheduledAt) {
      setError('Please set a schedule time or choose to send immediately')
      return
    }

    setLoading(true)
    setError(undefined)

    try {
      const notificationData: CreateNotificationRequest = {
        title: formData.title!,
        message: formData.message!,
        type: formData.type!,
        category: formData.category!,
        priority: formData.priority!,
        recipientType: formData.recipientType!,
        recipients: formData.recipients,
        channels: formData.channels!,
        content: {
          shortText: formData.content?.shortText || formData.message!.substring(0, 100),
          fullText: formData.message,
          actionButtons: formData.content?.actionButtons || []
        },
        scheduledAt: formData.scheduledAt,
        sendImmediately: formData.sendImmediately!,
        timezone: formData.timezone,
        expiresAt: formData.expiresAt,
        tags: formData.tags,
        campaign: formData.campaign,
        template: formData.template
      }

      await onAdd(notificationData)
      setCompleted(true)
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create notification')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      type: 'INFO',
      category: 'GENERAL',
      priority: 'MEDIUM',
      recipientType: 'ALL',
      channels: [
        { type: 'IN_APP', enabled: true },
        { type: 'EMAIL', enabled: false },
        { type: 'SMS', enabled: false },
        { type: 'PUSH', enabled: false }
      ],
      content: {
        shortText: '',
        actionButtons: []
      },
      sendImmediately: true,
      timezone: 'Asia/Kolkata',
      tags: []
    })
    setLoading(false)
    setCompleted(false)
    setError(undefined)
    setTagInput('')
    setSelectedTemplate('')
  }

  const enabledChannels = formData.channels?.filter(c => c.enabled) || []
  const estimatedReach = formData.recipientType === 'ALL' ? 1247 : 
                        formData.recipientType === 'STUDENTS' ? 1089 : 
                        formData.recipientType === 'INSTRUCTORS' ? 12 : 
                        formData.recipientType === 'ADMINS' ? 5 : 
                        formData.recipients?.length || 0

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold flex items-center space-x-2">
            <Bell className="w-5 h-5 text-blue-600" />
            <span>Create Notification</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Notification Created Successfully!</h3>
              <p className="text-gray-600">Your notification has been {formData.sendImmediately ? 'sent' : 'scheduled'} successfully.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              {/* Template Selection */}
              {templates.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Quick Start</h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Use Template (Optional)
                    </label>
                    <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a template to get started quickly" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Start from scratch</SelectItem>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            <div>
                              <div className="font-medium">{template.name}</div>
                              <div className="text-xs text-gray-500">{template.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notification Title *
                  </label>
                  <Input
                    value={formData.title || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="e.g., Class Reminder: JEE Main Physics"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message *
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    value={formData.message || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Write your notification message here..."
                    required
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {formData.message?.length || 0} characters
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type *
                    </label>
                    <Select 
                      value={formData.type} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, type: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {notificationTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center space-x-2">
                              <Badge className={type.color} variant="outline">
                                {type.label}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <Select 
                      value={formData.category} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority *
                    </label>
                    <Select 
                      value={formData.priority} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {priorities.map((priority) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            <div className="flex items-center space-x-2">
                              <Badge className={priority.color} variant="outline">
                                {priority.label}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <Send className="w-4 h-4" />
                  <span>{loading ? 'Creating...' : formData.sendImmediately ? 'Send Now' : 'Schedule'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
