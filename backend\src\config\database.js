const mysql = require('mysql2/promise');
const { AppError } = require('../utils/errorHandler');

/**
 * Database Configuration and Connection Pool
 */
class Database {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }

  /**
   * Initialize database connection pool
   */
  async initialize() {
    try {
      const config = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'utkrishta_admin',
        charset: 'utf8mb4',
        timezone: '+00:00',
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true,
        connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
        queueLimit: 0,
        ssl: process.env.DB_SSL === 'true' ? {
          rejectUnauthorized: false
        } : false
      };

      this.pool = mysql.createPool(config);

      // Test the connection
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      this.isConnected = true;
      console.log('✅ Database connected successfully');

      // Set up connection event handlers
      this.pool.on('connection', (connection) => {
        console.log(`New database connection established as id ${connection.threadId}`);
      });

      this.pool.on('error', (err) => {
        console.error('Database pool error:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          this.handleDisconnect();
        } else {
          throw err;
        }
      });

    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw new AppError('Database connection failed', 500);
    }
  }

  /**
   * Handle database disconnection
   */
  handleDisconnect() {
    console.log('Database connection lost. Attempting to reconnect...');
    this.isConnected = false;
    
    setTimeout(async () => {
      try {
        await this.initialize();
      } catch (error) {
        console.error('Failed to reconnect to database:', error);
        this.handleDisconnect();
      }
    }, 2000);
  }

  /**
   * Execute a query
   * @param {string} sql - SQL query
   * @param {array} params - Query parameters
   * @returns {array} Query results
   */
  async query(sql, params = []) {
    if (!this.pool) {
      throw new AppError('Database not initialized', 500);
    }

    try {
      const [results] = await this.pool.execute(sql, params);
      return results;
    } catch (error) {
      console.error('Database query error:', {
        sql: sql.substring(0, 100) + '...',
        params,
        error: error.message
      });
      
      // Handle specific MySQL errors
      if (error.code === 'ER_DUP_ENTRY') {
        throw new AppError('Duplicate entry found', 409);
      } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
        throw new AppError('Referenced record does not exist', 400);
      } else if (error.code === 'ER_ROW_IS_REFERENCED_2') {
        throw new AppError('Cannot delete record as it is referenced by other records', 400);
      }
      
      throw new AppError(`Database query failed: ${error.message}`, 500);
    }
  }

  /**
   * Execute a transaction
   * @param {function} callback - Transaction callback function
   * @returns {any} Transaction result
   */
  async transaction(callback) {
    if (!this.pool) {
      throw new AppError('Database not initialized', 500);
    }

    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // Create a query function for the transaction
      const transactionQuery = async (sql, params = []) => {
        const [results] = await connection.execute(sql, params);
        return results;
      };
      
      const result = await callback(transactionQuery);
      
      await connection.commit();
      return result;
      
    } catch (error) {
      await connection.rollback();
      console.error('Transaction error:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Get database statistics
   * @returns {object} Database statistics
   */
  async getStats() {
    if (!this.pool) {
      return {
        connected: false,
        active_connections: 0,
        total_connections: 0
      };
    }

    try {
      const [processListResults] = await this.pool.execute('SHOW PROCESSLIST');
      const [statusResults] = await this.pool.execute("SHOW STATUS LIKE 'Threads_%'");
      
      const stats = {
        connected: this.isConnected,
        active_connections: processListResults.length,
        pool_config: {
          connection_limit: this.pool.config.connectionLimit,
          queue_limit: this.pool.config.queueLimit
        }
      };

      // Parse status results
      statusResults.forEach(row => {
        if (row.Variable_name === 'Threads_connected') {
          stats.total_connections = parseInt(row.Value);
        } else if (row.Variable_name === 'Threads_running') {
          stats.running_threads = parseInt(row.Value);
        }
      });

      return stats;
    } catch (error) {
      console.error('Error getting database stats:', error);
      return {
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * Check database health
   * @returns {object} Health status
   */
  async healthCheck() {
    try {
      const startTime = Date.now();
      await this.query('SELECT 1 as health_check');
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        response_time: responseTime,
        connected: this.isConnected,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        connected: false,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Execute raw SQL (for migrations and seeds)
   * @param {string} sql - Raw SQL
   * @returns {any} Query result
   */
  async raw(sql) {
    if (!this.pool) {
      throw new AppError('Database not initialized', 500);
    }

    try {
      const [results] = await this.pool.query(sql);
      return results;
    } catch (error) {
      console.error('Raw SQL error:', error);
      throw error;
    }
  }

  /**
   * Close database connection pool
   */
  async close() {
    if (this.pool) {
      await this.pool.end();
      this.isConnected = false;
      console.log('Database connection pool closed');
    }
  }

  /**
   * Get connection from pool (for advanced usage)
   * @returns {object} Database connection
   */
  async getConnection() {
    if (!this.pool) {
      throw new AppError('Database not initialized', 500);
    }
    
    return await this.pool.getConnection();
  }

  /**
   * Format SQL query for logging
   * @param {string} sql - SQL query
   * @param {array} params - Query parameters
   * @returns {string} Formatted query
   */
  formatQuery(sql, params = []) {
    let formattedSql = sql;
    params.forEach((param, index) => {
      const placeholder = '?';
      const value = typeof param === 'string' ? `'${param}'` : param;
      formattedSql = formattedSql.replace(placeholder, value);
    });
    return formattedSql;
  }

  /**
   * Build WHERE clause from filters
   * @param {object} filters - Filter object
   * @param {string} tableAlias - Table alias
   * @returns {object} WHERE clause and parameters
   */
  buildWhereClause(filters, tableAlias = '') {
    const conditions = [];
    const params = [];
    const prefix = tableAlias ? `${tableAlias}.` : '';

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          conditions.push(`${prefix}${key} IN (${value.map(() => '?').join(', ')})`);
          params.push(...value);
        } else if (typeof value === 'object' && value.operator) {
          conditions.push(`${prefix}${key} ${value.operator} ?`);
          params.push(value.value);
        } else {
          conditions.push(`${prefix}${key} = ?`);
          params.push(value);
        }
      }
    });

    return {
      whereClause: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
      params
    };
  }

  /**
   * Build pagination clause
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {object} LIMIT clause and offset
   */
  buildPaginationClause(page = 1, limit = 25) {
    const offset = (page - 1) * limit;
    return {
      limitClause: `LIMIT ${limit} OFFSET ${offset}`,
      offset,
      limit
    };
  }
}

// Create and export database instance
const database = new Database();

module.exports = database;
