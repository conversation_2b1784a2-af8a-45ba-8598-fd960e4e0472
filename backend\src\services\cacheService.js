const Redis = require('redis');
const { AppError } = require('../utils/errorHandler');

/**
 * Cache Service
 * Handles Redis caching operations
 */
class CacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.defaultTTL = 300; // 5 minutes default TTL
  }

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      this.client = Redis.createClient({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: process.env.REDIS_DB || 0,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3
      });

      this.client.on('connect', () => {
        console.log('Redis client connected');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        console.error('Redis client error:', err);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('Redis client disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      console.error('Failed to initialize Redis:', error);
      // Don't throw error, allow app to continue without cache
    }
  }

  /**
   * Check if cache is available
   */
  isAvailable() {
    return this.client && this.isConnected;
  }

  /**
   * Get value from cache
   * @param {string} key - Cache key
   * @returns {any} Cached value or null
   */
  async get(key) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set value in cache
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} Success status
   */
  async set(key, value, ttl = null) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      const expiration = ttl || this.defaultTTL;
      
      await this.client.setEx(key, expiration, serializedValue);
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete value from cache
   * @param {string} key - Cache key
   * @returns {boolean} Success status
   */
  async del(key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple keys from cache
   * @param {array} keys - Array of cache keys
   * @returns {boolean} Success status
   */
  async delMultiple(keys) {
    if (!this.isAvailable() || !keys.length) {
      return false;
    }

    try {
      await this.client.del(keys);
      return true;
    } catch (error) {
      console.error('Cache delete multiple error:', error);
      return false;
    }
  }

  /**
   * Check if key exists in cache
   * @param {string} key - Cache key
   * @returns {boolean} Existence status
   */
  async exists(key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * Set expiration for a key
   * @param {string} key - Cache key
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} Success status
   */
  async expire(key, ttl) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.expire(key, ttl);
      return true;
    } catch (error) {
      console.error('Cache expire error:', error);
      return false;
    }
  }

  /**
   * Get keys matching pattern
   * @param {string} pattern - Key pattern
   * @returns {array} Array of matching keys
   */
  async keys(pattern) {
    if (!this.isAvailable()) {
      return [];
    }

    try {
      return await this.client.keys(pattern);
    } catch (error) {
      console.error('Cache keys error:', error);
      return [];
    }
  }

  /**
   * Clear all cache
   * @returns {boolean} Success status
   */
  async flushAll() {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.flushAll();
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   * @returns {object} Cache statistics
   */
  async getStats() {
    if (!this.isAvailable()) {
      return {
        connected: false,
        keys: 0,
        memory_usage: 0
      };
    }

    try {
      const info = await this.client.info('memory');
      const dbSize = await this.client.dbSize();
      
      return {
        connected: true,
        keys: dbSize,
        memory_usage: this.parseMemoryUsage(info),
        uptime: this.parseUptime(info)
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        connected: false,
        keys: 0,
        memory_usage: 0
      };
    }
  }

  /**
   * Parse memory usage from Redis info
   */
  parseMemoryUsage(info) {
    const match = info.match(/used_memory:(\d+)/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * Parse uptime from Redis info
   */
  parseUptime(info) {
    const match = info.match(/uptime_in_seconds:(\d+)/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * Generate cache key with prefix
   * @param {string} type - Cache type
   * @param {string} identifier - Unique identifier
   * @returns {string} Formatted cache key
   */
  generateKey(type, identifier) {
    const prefix = process.env.CACHE_PREFIX || 'utkrishta';
    return `${prefix}:${type}:${identifier}`;
  }

  /**
   * Cache with automatic key generation
   * @param {string} type - Cache type
   * @param {string} identifier - Unique identifier
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} Success status
   */
  async cacheWithKey(type, identifier, value, ttl = null) {
    const key = this.generateKey(type, identifier);
    return await this.set(key, value, ttl);
  }

  /**
   * Get cached value with automatic key generation
   * @param {string} type - Cache type
   * @param {string} identifier - Unique identifier
   * @returns {any} Cached value or null
   */
  async getCached(type, identifier) {
    const key = this.generateKey(type, identifier);
    return await this.get(key);
  }

  /**
   * Invalidate cache by pattern
   * @param {string} pattern - Key pattern to invalidate
   * @returns {boolean} Success status
   */
  async invalidatePattern(pattern) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const keys = await this.keys(pattern);
      if (keys.length > 0) {
        await this.delMultiple(keys);
      }
      return true;
    } catch (error) {
      console.error('Cache invalidate pattern error:', error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
    }
  }
}

module.exports = new CacheService();
