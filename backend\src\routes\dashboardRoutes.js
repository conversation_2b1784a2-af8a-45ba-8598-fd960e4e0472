const express = require('express');
const { body, query } = require('express-validator');
const dashboardController = require('../controllers/dashboardController');
const authMiddleware = require('../middleware/authMiddleware');
const roleMiddleware = require('../middleware/roleMiddleware');

const router = express.Router();

/**
 * Dashboard Routes
 * All routes require authentication and appropriate role permissions
 */

// Apply authentication middleware to all dashboard routes
router.use(authMiddleware.authenticate);
router.use(roleMiddleware.requireRole(['admin', 'super_admin']));

/**
 * GET /dashboard/overview
 * Get comprehensive dashboard statistics and metrics
 */
router.get('/overview', [
  query('period')
    .optional()
    .isIn(['1d', '7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 1d, 7d, 30d, 90d, 1y'),
  query('compare_previous')
    .optional()
    .isBoolean()
    .withMessage('Compare previous must be a boolean'),
  query('timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a valid string')
], dashboardController.getOverview);

/**
 * GET /dashboard/analytics
 * Get detailed analytics data with period comparisons
 */
router.get('/analytics', [
  query('period')
    .notEmpty()
    .isIn(['1d', '7d', '30d', '90d', '1y'])
    .withMessage('Period is required and must be one of: 1d, 7d, 30d, 90d, 1y'),
  query('metrics')
    .optional()
    .isString()
    .withMessage('Metrics must be a comma-separated string'),
  query('compare_previous')
    .optional()
    .isBoolean()
    .withMessage('Compare previous must be a boolean'),
  query('granularity')
    .optional()
    .isIn(['day', 'week', 'month'])
    .withMessage('Granularity must be one of: day, week, month'),
  query('timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a valid string')
], dashboardController.getAnalytics);

/**
 * GET /dashboard/widgets
 * Get customizable dashboard widgets with chart data
 */
router.get('/widgets', [
  query('widget_types')
    .optional()
    .isString()
    .withMessage('Widget types must be a comma-separated string'),
  query('period')
    .optional()
    .isIn(['1d', '7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 1d, 7d, 30d, 90d, 1y')
], dashboardController.getWidgets);

/**
 * POST /dashboard/widgets/layout
 * Save custom dashboard widget layout
 */
router.post('/widgets/layout', [
  body('layout')
    .isArray()
    .withMessage('Layout must be an array')
    .custom((layout) => {
      // Validate layout structure
      for (const widget of layout) {
        if (!widget.id || !widget.type || typeof widget.position !== 'object') {
          throw new Error('Each widget must have id, type, and position properties');
        }
        if (typeof widget.position.x !== 'number' || typeof widget.position.y !== 'number') {
          throw new Error('Widget position must have numeric x and y coordinates');
        }
      }
      return true;
    })
], dashboardController.saveWidgetLayout);

/**
 * GET /dashboard/reports
 * Get comprehensive dashboard reports with insights
 */
router.get('/reports', [
  query('report_type')
    .notEmpty()
    .isIn(['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])
    .withMessage('Report type is required and must be one of: daily, weekly, monthly, quarterly, yearly'),
  query('date')
    .optional()
    .isISO8601()
    .withMessage('Date must be in ISO 8601 format (YYYY-MM-DD)'),
  query('include_recommendations')
    .optional()
    .isBoolean()
    .withMessage('Include recommendations must be a boolean')
], dashboardController.getReports);

/**
 * GET /dashboard/export
 * Export dashboard data in various formats
 */
router.get('/export', [
  query('format')
    .notEmpty()
    .isIn(['csv', 'excel', 'pdf'])
    .withMessage('Format is required and must be one of: csv, excel, pdf'),
  query('data_type')
    .notEmpty()
    .isIn(['overview', 'analytics', 'reports', 'students', 'revenue'])
    .withMessage('Data type is required and must be one of: overview, analytics, reports, students, revenue'),
  query('period')
    .optional()
    .isIn(['1d', '7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 1d, 7d, 30d, 90d, 1y'),
  query('include_charts')
    .optional()
    .isBoolean()
    .withMessage('Include charts must be a boolean')
], dashboardController.exportData);

module.exports = router;
