'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, BookOpen, Video, UserPlus, TrendingUp, IndianRupee } from 'lucide-react'
import { DashboardStats } from '@/types/dashboard'

interface StatsCardsProps {
  stats: DashboardStats
}

export function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      title: 'Total Students',
      value: stats.totalUsers.toLocaleString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: 'Total Courses',
      value: stats.totalCourses.toString(),
      icon: BookOpen,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+3',
      changeType: 'positive' as const
    },
    {
      title: 'Total Videos',
      value: stats.totalVideos.toLocaleString(),
      icon: Video,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+24',
      changeType: 'positive' as const
    },
    {
      title: 'New Signups',
      value: stats.newSignupsThisWeek.toString(),
      icon: UserPlus,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+18%',
      changeType: 'positive' as const,
      subtitle: 'This Week'
    },
    {
      title: 'Total Revenue',
      value: `₹${(stats.totalRevenue / 1000).toFixed(0)}K`,
      icon: IndianRupee,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      change: `+${stats.revenueGrowth}%`,
      changeType: 'positive' as const
    },
    {
      title: 'Growth Rate',
      value: `${stats.revenueGrowth}%`,
      icon: TrendingUp,
      color: 'text-rose-600',
      bgColor: 'bg-rose-50',
      change: '+2.1%',
      changeType: 'positive' as const,
      subtitle: 'Monthly'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cards.map((card, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {card.title}
              {card.subtitle && (
                <span className="block text-xs text-gray-400 mt-1">{card.subtitle}</span>
              )}
            </CardTitle>
            <div className={`p-2 rounded-lg ${card.bgColor}`}>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold text-gray-900">{card.value}</div>
              <div className={`flex items-center text-sm ${
                card.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp className="h-3 w-3 mr-1" />
                {card.change}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
