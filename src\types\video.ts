export interface Video {
  id: string
  title: string
  description?: string
  fileName: string
  originalFileName: string
  fileUrl: string
  streamingUrl?: string
  thumbnailUrl?: string
  previewUrl?: string
  fileSize: number // in bytes
  duration: number // in seconds
  resolution: string // e.g., "1920x1080"
  quality: 'SD' | 'HD' | 'FHD' | '4K'
  format: string // e.g., "mp4", "webm", "avi"
  examId?: string
  examName?: string
  courseId?: string
  courseName?: string
  subjectId?: string
  subjectName?: string
  classId?: string
  className?: string
  category: 'LECTURE' | 'TUTORIAL' | 'DEMO' | 'EXPLANATION' | 'PRACTICE' | 'REVIEW' | 'OTHER'
  tags: string[]
  isPublic: boolean
  isFree: boolean
  isProcessed: boolean
  processingStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  viewCount: number
  likeCount: number
  dislikeCount: number
  rating: number
  totalRatings: number
  uploadedBy: string
  uploadedAt: string
  updatedAt: string
  publishedAt?: string
  version: number
  language: 'ENGLISH' | 'HINDI' | 'BOTH'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  hasSubtitles: boolean
  subtitleLanguages: string[]
  chapters: VideoChapter[]
  transcription?: string
}

export interface VideoChapter {
  id: string
  title: string
  startTime: number // in seconds
  endTime: number // in seconds
  description?: string
  thumbnailUrl?: string
}

export interface CreateVideoRequest {
  title: string
  description?: string
  fileName: string
  originalFileName: string
  fileUrl: string
  fileSize: number
  duration: number
  resolution: string
  quality: Video['quality']
  format: string
  examId?: string
  courseId?: string
  subjectId?: string
  classId?: string
  category: Video['category']
  tags: string[]
  isPublic: boolean
  isFree: boolean
  language: Video['language']
  difficulty: Video['difficulty']
  chapters?: Omit<VideoChapter, 'id'>[]
}

export interface UpdateVideoRequest extends Partial<CreateVideoRequest> {
  id: string
}

export interface VideoFilters {
  examId?: string
  courseId?: string
  subjectId?: string
  classId?: string
  category?: Video['category']
  isPublic?: boolean
  isFree?: boolean
  isProcessed?: boolean
  processingStatus?: Video['processingStatus']
  language?: Video['language']
  difficulty?: Video['difficulty']
  quality?: Video['quality']
  hasSubtitles?: boolean
  search?: string
  tags?: string[]
  sortBy?: 'title' | 'uploadedAt' | 'viewCount' | 'rating' | 'duration' | 'fileSize'
  sortOrder?: 'asc' | 'desc'
}

export interface VideoStats {
  totalVideos: number
  publicVideos: number
  freeVideos: number
  processedVideos: number
  totalViews: number
  totalLikes: number
  averageRating: number
  totalDuration: number // in seconds
  totalFileSize: number // in bytes
}

export interface VideoUploadProgress {
  id: string
  fileName: string
  progress: number
  status: 'uploading' | 'processing' | 'generating_thumbnail' | 'completed' | 'error'
  error?: string
  estimatedTimeRemaining?: number
}

export interface VideoProcessingJob {
  id: string
  videoId: string
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  progress: number
  currentStep: 'UPLOAD' | 'TRANSCODE' | 'THUMBNAIL' | 'CHAPTERS' | 'SUBTITLES' | 'COMPLETE'
  error?: string
  startedAt: string
  completedAt?: string
}

export interface VideoQuality {
  quality: Video['quality']
  resolution: string
  bitrate: string
  fileSize: number
  url: string
}

export interface VideoAnalytics {
  videoId: string
  totalViews: number
  uniqueViews: number
  averageWatchTime: number // in seconds
  completionRate: number // percentage
  engagementRate: number // percentage
  viewsByDay: { date: string; views: number }[]
  viewsByRegion: { region: string; views: number }[]
  deviceStats: { device: string; views: number }[]
}
