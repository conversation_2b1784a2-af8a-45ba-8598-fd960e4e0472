const WebSocket = require('ws');
const { AppError } = require('../utils/errorHandler');

/**
 * Real-time Service
 * Handles WebSocket connections and real-time updates
 */
class RealTimeService {
  constructor() {
    this.connections = new Map(); // userId -> Set of WebSocket connections
    this.connectionMetadata = new Map(); // ws -> { userId, connectedAt, lastActivity }
    this.updateInterval = null;
    this.isInitialized = false;
  }

  /**
   * Initialize real-time service
   */
  initialize() {
    if (this.isInitialized) {
      return;
    }

    // Start periodic updates every 30 seconds
    this.updateInterval = setInterval(() => {
      this.broadcastPeriodicUpdates();
    }, 30000);

    // Cleanup inactive connections every 5 minutes
    setInterval(() => {
      this.cleanupInactiveConnections();
    }, 300000);

    this.isInitialized = true;
    console.log('Real-time service initialized');
  }

  /**
   * Add WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   */
  addConnection(ws, userId) {
    try {
      // Initialize user connections set if not exists
      if (!this.connections.has(userId)) {
        this.connections.set(userId, new Set());
      }

      // Add connection to user's set
      this.connections.get(userId).add(ws);

      // Store connection metadata
      this.connectionMetadata.set(ws, {
        userId,
        connectedAt: new Date(),
        lastActivity: new Date()
      });

      // Set up connection event handlers
      this.setupConnectionHandlers(ws, userId);

      console.log(`WebSocket connection added for user ${userId}`);
    } catch (error) {
      console.error('Error adding WebSocket connection:', error);
    }
  }

  /**
   * Remove WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   */
  removeConnection(ws, userId) {
    try {
      // Remove from user's connections
      if (this.connections.has(userId)) {
        this.connections.get(userId).delete(ws);
        
        // Remove user entry if no connections left
        if (this.connections.get(userId).size === 0) {
          this.connections.delete(userId);
        }
      }

      // Remove connection metadata
      this.connectionMetadata.delete(ws);

      console.log(`WebSocket connection removed for user ${userId}`);
    } catch (error) {
      console.error('Error removing WebSocket connection:', error);
    }
  }

  /**
   * Set up connection event handlers
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   */
  setupConnectionHandlers(ws, userId) {
    // Handle incoming messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleIncomingMessage(ws, userId, message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
        this.sendError(ws, 'Invalid message format');
      }
    });

    // Handle connection close
    ws.on('close', () => {
      this.removeConnection(ws, userId);
    });

    // Handle connection error
    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.removeConnection(ws, userId);
    });

    // Send ping every 30 seconds to keep connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      } else {
        clearInterval(pingInterval);
      }
    }, 30000);
  }

  /**
   * Handle incoming WebSocket message
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {object} message - Parsed message
   */
  handleIncomingMessage(ws, userId, message) {
    // Update last activity
    const metadata = this.connectionMetadata.get(ws);
    if (metadata) {
      metadata.lastActivity = new Date();
    }

    switch (message.type) {
      case 'ping':
        this.sendMessage(ws, { type: 'pong', timestamp: new Date().toISOString() });
        break;
      
      case 'subscribe':
        this.handleSubscription(ws, userId, message.channels || []);
        break;
      
      case 'unsubscribe':
        this.handleUnsubscription(ws, userId, message.channels || []);
        break;
      
      case 'request_update':
        this.handleUpdateRequest(ws, userId, message.data_type);
        break;
      
      default:
        this.sendError(ws, 'Unknown message type');
    }
  }

  /**
   * Handle subscription to channels
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {array} channels - Channels to subscribe to
   */
  handleSubscription(ws, userId, channels) {
    const metadata = this.connectionMetadata.get(ws);
    if (metadata) {
      metadata.subscriptions = metadata.subscriptions || new Set();
      channels.forEach(channel => metadata.subscriptions.add(channel));
    }

    this.sendMessage(ws, {
      type: 'subscription_confirmed',
      channels,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle unsubscription from channels
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {array} channels - Channels to unsubscribe from
   */
  handleUnsubscription(ws, userId, channels) {
    const metadata = this.connectionMetadata.get(ws);
    if (metadata && metadata.subscriptions) {
      channels.forEach(channel => metadata.subscriptions.delete(channel));
    }

    this.sendMessage(ws, {
      type: 'unsubscription_confirmed',
      channels,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle update request
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {string} dataType - Type of data requested
   */
  async handleUpdateRequest(ws, userId, dataType) {
    try {
      let data = null;

      switch (dataType) {
        case 'dashboard_overview':
          const dashboardService = require('./dashboardService');
          data = await dashboardService.getOverviewData({
            period: '1d',
            comparePrevious: false,
            timezone: 'Asia/Kolkata',
            userId
          });
          break;
        
        case 'notifications':
          // TODO: Implement notification service
          data = { unread_count: 0, recent_notifications: [] };
          break;
        
        default:
          this.sendError(ws, 'Unknown data type requested');
          return;
      }

      this.sendMessage(ws, {
        type: 'update_response',
        data_type: dataType,
        data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error handling update request:', error);
      this.sendError(ws, 'Failed to fetch requested data');
    }
  }

  /**
   * Send message to WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {object} message - Message to send
   */
  sendMessage(ws, message) {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
    }
  }

  /**
   * Send error message to WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} errorMessage - Error message
   */
  sendError(ws, errorMessage) {
    this.sendMessage(ws, {
      type: 'error',
      message: errorMessage,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Broadcast message to all connections of a user
   * @param {string} userId - User ID
   * @param {object} message - Message to broadcast
   */
  broadcastToUser(userId, message) {
    const userConnections = this.connections.get(userId);
    if (userConnections) {
      userConnections.forEach(ws => {
        this.sendMessage(ws, message);
      });
    }
  }

  /**
   * Broadcast message to all connected users
   * @param {object} message - Message to broadcast
   * @param {array} excludeUsers - User IDs to exclude from broadcast
   */
  broadcastToAll(message, excludeUsers = []) {
    this.connections.forEach((connections, userId) => {
      if (!excludeUsers.includes(userId)) {
        connections.forEach(ws => {
          this.sendMessage(ws, message);
        });
      }
    });
  }

  /**
   * Broadcast periodic updates
   */
  async broadcastPeriodicUpdates() {
    try {
      // Get current metrics for all users
      const dashboardService = require('./dashboardService');
      
      // Broadcast to all connected users
      for (const [userId, connections] of this.connections) {
        try {
          const overviewData = await dashboardService.getOverviewData({
            period: '1d',
            comparePrevious: false,
            timezone: 'Asia/Kolkata',
            userId
          });

          const updateMessage = {
            type: 'periodic_update',
            data: {
              dashboard_overview: overviewData
            },
            timestamp: new Date().toISOString()
          };

          connections.forEach(ws => {
            this.sendMessage(ws, updateMessage);
          });
        } catch (error) {
          console.error(`Error getting periodic update for user ${userId}:`, error);
        }
      }
    } catch (error) {
      console.error('Error broadcasting periodic updates:', error);
    }
  }

  /**
   * Clean up inactive connections
   */
  cleanupInactiveConnections() {
    const now = new Date();
    const inactiveThreshold = 10 * 60 * 1000; // 10 minutes

    this.connectionMetadata.forEach((metadata, ws) => {
      const timeSinceLastActivity = now - metadata.lastActivity;
      
      if (timeSinceLastActivity > inactiveThreshold || ws.readyState !== WebSocket.OPEN) {
        this.removeConnection(ws, metadata.userId);
        
        if (ws.readyState === WebSocket.OPEN) {
          ws.close(1000, 'Connection inactive');
        }
      }
    });
  }

  /**
   * Get connection statistics
   * @returns {object} Connection statistics
   */
  getStats() {
    const totalConnections = Array.from(this.connections.values())
      .reduce((sum, connections) => sum + connections.size, 0);

    return {
      total_connections: totalConnections,
      unique_users: this.connections.size,
      connections_by_user: Array.from(this.connections.entries()).map(([userId, connections]) => ({
        user_id: userId,
        connection_count: connections.size
      }))
    };
  }

  /**
   * Shutdown real-time service
   */
  shutdown() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    // Close all connections
    this.connections.forEach((connections) => {
      connections.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close(1000, 'Server shutting down');
        }
      });
    });

    this.connections.clear();
    this.connectionMetadata.clear();
    this.isInitialized = false;

    console.log('Real-time service shutdown');
  }
}

module.exports = new RealTimeService();
