'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Play, 
  Eye, 
  Video as VideoIcon, 
  ThumbsUp,
  Clock,
  Upload,
  Filter,
  MoreHorizontal,
  Download,
  Star,
  Users,
  TrendingUp,
  Zap,
  HardDrive
} from 'lucide-react'
import { Video, VideoFilters, VideoStats, CreateVideoRequest } from '@/types/video'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'
import { Exam } from '@/types/exam'
import { getVideos, deleteVideo, getVideoStats, createVideo } from '@/lib/video-data'
import { getCourses } from '@/lib/course-data'
import { getSubjects } from '@/lib/subject-data'
import { getExams } from '@/lib/exam-data'
import { VideoUploadModal } from '@/components/videos/upload-modal'

export default function VideosPage() {
  const [videos, setVideos] = useState<Video[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [stats, setStats] = useState<VideoStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [filters, setFilters] = useState<VideoFilters>({
    search: '',
    sortBy: 'uploadedAt',
    sortOrder: 'desc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadVideos()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [videosData, coursesData, subjectsData, examsData, statsData] = await Promise.all([
        getVideos(),
        getCourses(),
        getSubjects(),
        getExams(),
        getVideoStats()
      ])
      setVideos(videosData)
      setCourses(coursesData)
      setSubjects(subjectsData)
      setExams(examsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadVideos = async () => {
    try {
      const data = await getVideos(filters)
      setVideos(data)
    } catch (error) {
      console.error('Failed to load videos:', error)
    }
  }

  const handleDeleteVideo = async (id: string) => {
    if (confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      try {
        await deleteVideo(id)
        await loadVideos()
        // Reload stats
        const statsData = await getVideoStats()
        setStats(statsData)
      } catch (error) {
        console.error('Failed to delete video:', error)
      }
    }
  }

  const handleFilterChange = (key: keyof VideoFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleUpload = async (data: CreateVideoRequest) => {
    try {
      await createVideo(data)
      await loadVideos()
      // Reload stats
      const statsData = await getVideoStats()
      setStats(statsData)
      setShowUploadModal(false)
    } catch (error) {
      console.error('Failed to upload video:', error)
      throw error
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'LECTURE': return 'bg-blue-100 text-blue-800'
      case 'TUTORIAL': return 'bg-green-100 text-green-800'
      case 'DEMO': return 'bg-purple-100 text-purple-800'
      case 'EXPLANATION': return 'bg-orange-100 text-orange-800'
      case 'PRACTICE': return 'bg-yellow-100 text-yellow-800'
      case 'REVIEW': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case '4K': return 'bg-red-100 text-red-800'
      case 'FHD': return 'bg-green-100 text-green-800'
      case 'HD': return 'bg-blue-100 text-blue-800'
      case 'SD': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getProcessingStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'bg-green-100 text-green-800'
      case 'PROCESSING': return 'bg-yellow-100 text-yellow-800'
      case 'PENDING': return 'bg-blue-100 text-blue-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading videos...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Video Management</h1>
          <p className="text-gray-600 mt-1">Upload, process, and manage educational videos</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Bulk Upload</span>
          </Button>
          <Button 
            className="flex items-center space-x-2"
            onClick={() => setShowUploadModal(true)}
          >
            <Plus className="w-4 h-4" />
            <span>Upload Video</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-7 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Videos</CardTitle>
              <VideoIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalVideos}</div>
              <p className="text-xs text-muted-foreground">All videos</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Public</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.publicVideos}</div>
              <p className="text-xs text-muted-foreground">Publicly available</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processed</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.processedVideos}</div>
              <p className="text-xs text-muted-foreground">Ready to stream</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">All time views</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Likes</CardTitle>
              <ThumbsUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalLikes.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">User engagement</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">User rating</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Storage</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatFileSize(stats.totalFileSize)}</div>
              <p className="text-xs text-muted-foreground">Total storage</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search videos by title, description, tags, or instructor..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filters.examId || 'all'} onValueChange={(value) => handleFilterChange('examId', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by Exam" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Exams</SelectItem>
                {exams.map((exam) => (
                  <SelectItem key={exam.id} value={exam.id}>
                    {exam.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="LECTURE">Lecture</SelectItem>
                <SelectItem value="TUTORIAL">Tutorial</SelectItem>
                <SelectItem value="DEMO">Demo</SelectItem>
                <SelectItem value="EXPLANATION">Explanation</SelectItem>
                <SelectItem value="PRACTICE">Practice</SelectItem>
                <SelectItem value="REVIEW">Review</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.processingStatus || 'all'} onValueChange={(value) => handleFilterChange('processingStatus', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="PROCESSING">Processing</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.quality || 'all'} onValueChange={(value) => handleFilterChange('quality', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-24">
                <SelectValue placeholder="Quality" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Quality</SelectItem>
                <SelectItem value="4K">4K</SelectItem>
                <SelectItem value="FHD">FHD</SelectItem>
                <SelectItem value="HD">HD</SelectItem>
                <SelectItem value="SD">SD</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Video Details</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Course/Subject</TableHead>
                <TableHead>Technical Info</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {videos.map((video) => (
                <TableRow key={video.id}>
                  <TableCell>
                    <div className="flex items-start space-x-3">
                      <div className="relative">
                        <div className="w-20 h-12 bg-gray-200 rounded border flex items-center justify-center">
                          {video.thumbnailUrl ? (
                            <img
                              src={video.thumbnailUrl}
                              alt={video.title}
                              className="w-20 h-12 object-cover rounded border"
                            />
                          ) : (
                            <VideoIcon className="w-6 h-6 text-gray-400" />
                          )}
                        </div>
                        <div className="absolute bottom-0 right-0 bg-black bg-opacity-75 text-white text-xs px-1 rounded-tl">
                          {formatDuration(video.duration)}
                        </div>
                      </div>
                      <div className="space-y-1 flex-1">
                        <div className="font-medium line-clamp-2">{video.title}</div>
                        <div className="text-sm text-gray-500 line-clamp-2">{video.description}</div>
                        <div className="flex items-center space-x-2">
                          {video.tags.slice(0, 2).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {video.tags.length > 2 && (
                            <span className="text-xs text-gray-400">+{video.tags.length - 2}</span>
                          )}
                        </div>
                        <div className="text-xs text-gray-400">
                          By {video.uploadedBy} • {new Date(video.uploadedAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getCategoryColor(video.category)}>
                        {video.category}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <Badge variant="outline" className="text-xs">
                          {video.difficulty}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {video.language}
                        </Badge>
                      </div>
                      {video.hasSubtitles && (
                        <Badge variant="outline" className="text-xs">
                          Subtitles
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {video.examName && (
                        <div className="text-sm font-medium">{video.examName}</div>
                      )}
                      {video.courseName && (
                        <div className="text-xs text-gray-500 line-clamp-1">{video.courseName}</div>
                      )}
                      {video.subjectName && (
                        <div className="text-xs text-blue-600">{video.subjectName}</div>
                      )}
                      {video.className && (
                        <div className="text-xs text-purple-600">{video.className}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Badge className={getQualityColor(video.quality)}>
                          {video.quality}
                        </Badge>
                        <span className="text-xs text-gray-500">{video.resolution}</span>
                      </div>
                      <div className="text-xs text-gray-500">{formatFileSize(video.fileSize)}</div>
                      <div className="text-xs text-gray-500">{video.format.toUpperCase()}</div>
                      {video.chapters.length > 0 && (
                        <div className="text-xs text-blue-600">{video.chapters.length} chapters</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{video.viewCount.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ThumbsUp className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{video.likeCount}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                        <span className="text-sm">{video.rating}</span>
                        <span className="text-xs text-gray-400">({video.totalRatings})</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getProcessingStatusColor(video.processingStatus)}>
                        {video.processingStatus}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <Badge variant={video.isPublic ? 'success' : 'secondary'}>
                          {video.isPublic ? 'Public' : 'Private'}
                        </Badge>
                        {video.isFree && (
                          <Badge variant="success" className="text-xs">Free</Badge>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" title="Preview">
                        <Play className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Analytics">
                        <TrendingUp className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Edit">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        title="Delete"
                        onClick={() => handleDeleteVideo(video.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {videos.length === 0 && (
            <div className="text-center py-8">
              <VideoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No videos found matching your filters.</p>
              <Button onClick={() => setShowUploadModal(true)}>
                Upload your first video
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Upload className="w-4 h-4" />
              <span>Bulk Upload Videos</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <Zap className="w-4 h-4" />
              <span>Process Queue</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <TrendingUp className="w-4 h-4" />
              <span>Analytics Report</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2 h-12">
              <MoreHorizontal className="w-4 h-4" />
              <span>More Actions</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Upload Modal */}
      <VideoUploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUpload}
        courses={courses}
        subjects={subjects}
        exams={exams}
      />
    </div>
  )
}
