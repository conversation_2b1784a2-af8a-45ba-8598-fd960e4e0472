'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X, Plus, CheckCircle, AlertCircle, Video, Clock, Hash, FileText, TestTube, Link } from 'lucide-react'
import { CreateClassRequest } from '@/types/class'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'
import { Exam } from '@/types/exam'

interface AddClassModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (data: CreateClassRequest) => Promise<void>
  courses: Course[]
  subjects: Subject[]
  exams: Exam[]
}

export function AddClassModal({ isOpen, onClose, onAdd, courses, subjects, exams }: AddClassModalProps) {
  const [formData, setFormData] = useState<Partial<CreateClassRequest>>({
    order: 1,
    duration: 60, // 1 hour default
    isPublished: false,
    isFree: false,
    resources: []
  })
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [error, setError] = useState<string>()
  const [resourceInput, setResourceInput] = useState('')

  const handleAddResource = () => {
    if (resourceInput.trim() && !formData.resources?.includes(resourceInput.trim())) {
      setFormData(prev => ({
        ...prev,
        resources: [...(prev.resources || []), resourceInput.trim()]
      }))
      setResourceInput('')
    }
  }

  const handleRemoveResource = (resourceToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      resources: prev.resources?.filter(resource => resource !== resourceToRemove) || []
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title || !formData.description || !formData.subjectId) {
      setError('Please fill in all required fields')
      return
    }

    setLoading(true)
    setError(undefined)

    try {
      const classData: CreateClassRequest = {
        title: formData.title!,
        description: formData.description!,
        subjectId: formData.subjectId!,
        order: formData.order!,
        duration: formData.duration!,
        videoUrl: formData.videoUrl,
        pdfUrl: formData.pdfUrl,
        testId: formData.testId,
        notes: formData.notes,
        resources: formData.resources || [],
        isPublished: formData.isPublished!,
        isFree: formData.isFree!
      }

      await onAdd(classData)
      setCompleted(true)
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add class')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      order: 1,
      duration: 60,
      isPublished: false,
      isFree: false,
      resources: []
    })
    setLoading(false)
    setCompleted(false)
    setError(undefined)
    setResourceInput('')
  }

  // Filter subjects based on selected exam and course
  const filteredSubjects = subjects.filter(subject => {
    if (formData.examId && subject.examId !== formData.examId) return false
    if (formData.courseId && subject.courseId !== formData.courseId) return false
    return true
  })

  // Filter courses based on selected exam
  const filteredCourses = formData.examId 
    ? courses.filter(course => course.examId === formData.examId)
    : courses

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold flex items-center space-x-2">
            <Video className="w-5 h-5 text-blue-600" />
            <span>Add New Class</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Class Added Successfully!</h3>
              <p className="text-gray-600">The class has been created and is now available in the system.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Class Title *
                    </label>
                    <Input
                      value={formData.title || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="e.g., Introduction to Motion"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Order *
                    </label>
                    <div className="relative">
                      <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        type="number"
                        value={formData.order || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, order: parseInt(e.target.value) || 1 }))}
                        placeholder="1"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.description || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter class description..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Duration (minutes) *
                  </label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      type="number"
                      value={formData.duration || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                      placeholder="60"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Organization */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Organization</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exam
                    </label>
                    <Select 
                      value={formData.examId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ 
                        ...prev, 
                        examId: value || undefined,
                        courseId: '', // Reset course when exam changes
                        subjectId: '' // Reset subject when exam changes
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select exam" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific exam</SelectItem>
                        {exams.map((exam) => (
                          <SelectItem key={exam.id} value={exam.id}>
                            {exam.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Course
                    </label>
                    <Select 
                      value={formData.courseId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ 
                        ...prev, 
                        courseId: value || undefined,
                        subjectId: '' // Reset subject when course changes
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select course" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific course</SelectItem>
                        {filteredCourses.map((course) => (
                          <SelectItem key={course.id} value={course.id}>
                            {course.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Subject *
                    </label>
                    <Select 
                      value={formData.subjectId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredSubjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Content Links */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Content Links</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Video URL
                    </label>
                    <div className="relative">
                      <Video className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        value={formData.videoUrl || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, videoUrl: e.target.value }))}
                        placeholder="https://example.com/video.mp4"
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      PDF URL
                    </label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        value={formData.pdfUrl || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, pdfUrl: e.target.value }))}
                        placeholder="https://example.com/notes.pdf"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test ID
                  </label>
                  <div className="relative">
                    <TestTube className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      value={formData.testId || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, testId: e.target.value }))}
                      placeholder="test-123"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    value={formData.notes || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Additional notes for this class..."
                  />
                </div>

                {/* Resources */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Resources
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="relative flex-1">
                      <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        value={resourceInput}
                        onChange={(e) => setResourceInput(e.target.value)}
                        placeholder="Add a resource URL"
                        className="pl-10"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddResource())}
                      />
                    </div>
                    <Button type="button" onClick={handleAddResource} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.resources?.map((resource, index) => (
                      <Badge key={index} variant="outline" className="flex items-center space-x-1">
                        <span className="truncate max-w-xs">{resource}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveResource(resource)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPublished"
                    checked={formData.isPublished}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPublished: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="isPublished" className="text-sm text-gray-700">
                    Publish this class immediately
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isFree"
                    checked={formData.isFree}
                    onChange={(e) => setFormData(prev => ({ ...prev, isFree: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="isFree" className="text-sm text-gray-700">
                    This is a free class
                  </label>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>{loading ? 'Adding...' : 'Add Class'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
