import { Video, CreateVideoRequest, UpdateVideoRequest, VideoFilters, VideoStats, VideoChapter } from '@/types/video'

// Mock data - In real app, this would come from your API
const mockVideos: Video[] = [
  {
    id: '1',
    title: 'Introduction to Mechanics - Laws of Motion',
    description: 'Comprehensive introduction to Newton\'s laws of motion with practical examples and problem-solving techniques',
    fileName: 'mechanics-laws-motion-v1.mp4',
    originalFileName: 'Mechanics Laws of Motion.mp4',
    fileUrl: 'https://example.com/videos/mechanics-laws-motion-v1.mp4',
    streamingUrl: 'https://stream.example.com/mechanics-laws-motion-v1/playlist.m3u8',
    thumbnailUrl: 'https://example.com/thumbnails/mechanics-laws-motion.jpg',
    previewUrl: 'https://example.com/previews/mechanics-laws-motion-preview.mp4',
    fileSize: 524288000, // 500MB
    duration: 3600, // 1 hour
    resolution: '1920x1080',
    quality: 'FHD',
    format: 'mp4',
    examId: '1',
    examName: 'JEE Main',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    subjectId: '1',
    subjectName: 'Mechanics',
    classId: '1',
    className: 'Introduction to Motion',
    category: 'LECTURE',
    tags: ['Physics', 'Mechanics', 'Newton Laws', 'JEE Main', 'Motion'],
    isPublic: true,
    isFree: false,
    isProcessed: true,
    processingStatus: 'COMPLETED',
    viewCount: 2450,
    likeCount: 189,
    dislikeCount: 12,
    rating: 4.8,
    totalRatings: 201,
    uploadedBy: 'Dr. Rajesh Kumar',
    uploadedAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    publishedAt: '2024-01-15T12:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'MEDIUM',
    hasSubtitles: true,
    subtitleLanguages: ['en', 'hi'],
    chapters: [
      {
        id: 'ch1',
        title: 'Introduction to Motion',
        startTime: 0,
        endTime: 600,
        description: 'Basic concepts of motion and displacement'
      },
      {
        id: 'ch2',
        title: 'Newton\'s First Law',
        startTime: 600,
        endTime: 1200,
        description: 'Law of inertia with examples'
      },
      {
        id: 'ch3',
        title: 'Newton\'s Second Law',
        startTime: 1200,
        endTime: 2400,
        description: 'F = ma and its applications'
      },
      {
        id: 'ch4',
        title: 'Newton\'s Third Law',
        startTime: 2400,
        endTime: 3000,
        description: 'Action and reaction pairs'
      },
      {
        id: 'ch5',
        title: 'Problem Solving',
        startTime: 3000,
        endTime: 3600,
        description: 'Worked examples and practice problems'
      }
    ],
    transcription: 'Welcome to this comprehensive lecture on Newton\'s laws of motion...'
  },
  {
    id: '2',
    title: 'Cell Biology - Plant Cell Structure',
    description: 'Detailed exploration of plant cell components, organelles, and their functions',
    fileName: 'cell-biology-plant-structure-v1.mp4',
    originalFileName: 'Plant Cell Structure.mp4',
    fileUrl: 'https://example.com/videos/cell-biology-plant-structure-v1.mp4',
    streamingUrl: 'https://stream.example.com/cell-biology-plant-structure-v1/playlist.m3u8',
    thumbnailUrl: 'https://example.com/thumbnails/plant-cell-structure.jpg',
    fileSize: 367001600, // 350MB
    duration: 2700, // 45 minutes
    resolution: '1920x1080',
    quality: 'FHD',
    format: 'mp4',
    examId: '3',
    examName: 'NEET UG',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    subjectId: '3',
    subjectName: 'Botany',
    classId: '3',
    className: 'Plant Cell Structure',
    category: 'LECTURE',
    tags: ['Biology', 'Cell Biology', 'Plant Cell', 'NEET', 'Botany'],
    isPublic: true,
    isFree: false,
    isProcessed: true,
    processingStatus: 'COMPLETED',
    viewCount: 1890,
    likeCount: 156,
    dislikeCount: 8,
    rating: 4.9,
    totalRatings: 164,
    uploadedBy: 'Dr. Priya Sharma',
    uploadedAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
    publishedAt: '2024-01-12T14:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'MEDIUM',
    hasSubtitles: true,
    subtitleLanguages: ['en'],
    chapters: [
      {
        id: 'ch1',
        title: 'Cell Wall Structure',
        startTime: 0,
        endTime: 540,
        description: 'Composition and function of plant cell wall'
      },
      {
        id: 'ch2',
        title: 'Chloroplasts',
        startTime: 540,
        endTime: 1080,
        description: 'Structure and function of chloroplasts'
      },
      {
        id: 'ch3',
        title: 'Vacuoles',
        startTime: 1080,
        endTime: 1620,
        description: 'Central vacuole and its importance'
      },
      {
        id: 'ch4',
        title: 'Other Organelles',
        startTime: 1620,
        endTime: 2700,
        description: 'Nucleus, mitochondria, and other components'
      }
    ]
  },
  {
    id: '3',
    title: 'Calculus Tutorial - Limits and Continuity',
    description: 'Step-by-step tutorial on limits, continuity, and differentiability concepts',
    fileName: 'calculus-limits-continuity-v1.mp4',
    originalFileName: 'Limits and Continuity Tutorial.mp4',
    fileUrl: 'https://example.com/videos/calculus-limits-continuity-v1.mp4',
    streamingUrl: 'https://stream.example.com/calculus-limits-continuity-v1/playlist.m3u8',
    thumbnailUrl: 'https://example.com/thumbnails/calculus-limits.jpg',
    fileSize: 419430400, // 400MB
    duration: 4200, // 70 minutes
    resolution: '1920x1080',
    quality: 'FHD',
    format: 'mp4',
    examId: '2',
    examName: 'JEE Advanced',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    subjectId: '5',
    subjectName: 'Calculus',
    classId: '5',
    className: 'Limits and Continuity',
    category: 'TUTORIAL',
    tags: ['Mathematics', 'Calculus', 'Limits', 'JEE Advanced', 'Tutorial'],
    isPublic: true,
    isFree: false,
    isProcessed: true,
    processingStatus: 'COMPLETED',
    viewCount: 1234,
    likeCount: 98,
    dislikeCount: 5,
    rating: 4.7,
    totalRatings: 103,
    uploadedBy: 'Prof. Amit Singh',
    uploadedAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
    publishedAt: '2024-01-10T16:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'HARD',
    hasSubtitles: false,
    subtitleLanguages: [],
    chapters: [
      {
        id: 'ch1',
        title: 'Introduction to Limits',
        startTime: 0,
        endTime: 840,
        description: 'Basic concept of limits'
      },
      {
        id: 'ch2',
        title: 'Limit Theorems',
        startTime: 840,
        endTime: 1680,
        description: 'Important limit theorems and properties'
      },
      {
        id: 'ch3',
        title: 'Continuity',
        startTime: 1680,
        endTime: 2520,
        description: 'Definition and types of continuity'
      },
      {
        id: 'ch4',
        title: 'L\'Hospital\'s Rule',
        startTime: 2520,
        endTime: 3360,
        description: 'Application of L\'Hospital\'s rule'
      },
      {
        id: 'ch5',
        title: 'Practice Problems',
        startTime: 3360,
        endTime: 4200,
        description: 'Solved examples and practice'
      }
    ]
  },
  {
    id: '4',
    title: 'Organic Chemistry Basics (Hindi)',
    description: 'बुनियादी कार्बनिक रसायन की अवधारणाएं और नामकरण',
    fileName: 'organic-chemistry-basics-hindi-v1.mp4',
    originalFileName: 'Organic Chemistry Basics Hindi.mp4',
    fileUrl: 'https://example.com/videos/organic-chemistry-basics-hindi-v1.mp4',
    streamingUrl: 'https://stream.example.com/organic-chemistry-basics-hindi-v1/playlist.m3u8',
    thumbnailUrl: 'https://example.com/thumbnails/organic-chemistry-hindi.jpg',
    fileSize: 314572800, // 300MB
    duration: 2400, // 40 minutes
    resolution: '1280x720',
    quality: 'HD',
    format: 'mp4',
    examId: '3',
    examName: 'NEET UG',
    courseId: '5',
    courseName: 'Free NEET Chemistry Basics',
    subjectId: '8',
    subjectName: 'Organic Chemistry Basics',
    classId: '6',
    className: 'Organic Chemistry Introduction',
    category: 'EXPLANATION',
    tags: ['Chemistry', 'Organic', 'Hindi', 'NEET', 'Basics'],
    isPublic: true,
    isFree: true,
    isProcessed: true,
    processingStatus: 'COMPLETED',
    viewCount: 3456,
    likeCount: 287,
    dislikeCount: 15,
    rating: 4.6,
    totalRatings: 302,
    uploadedBy: 'Dr. Neha Agarwal',
    uploadedAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z',
    publishedAt: '2024-01-05T15:00:00Z',
    version: 1,
    language: 'HINDI',
    difficulty: 'EASY',
    hasSubtitles: true,
    subtitleLanguages: ['hi', 'en'],
    chapters: [
      {
        id: 'ch1',
        title: 'कार्बनिक रसायन परिचय',
        startTime: 0,
        endTime: 600,
        description: 'कार्बनिक रसायन की मूल बातें'
      },
      {
        id: 'ch2',
        title: 'IUPAC नामकरण',
        startTime: 600,
        endTime: 1200,
        description: 'IUPAC नामकरण के नियम'
      },
      {
        id: 'ch3',
        title: 'कार्यात्मक समूह',
        startTime: 1200,
        endTime: 1800,
        description: 'विभिन्न कार्यात्मक समूहों का परिचय'
      },
      {
        id: 'ch4',
        title: 'उदाहरण और अभ्यास',
        startTime: 1800,
        endTime: 2400,
        description: 'हल किए गए उदाहरण और अभ्यास'
      }
    ]
  },
  {
    id: '5',
    title: 'Physics Demo - Optics Experiments',
    description: 'Live demonstration of optics experiments including reflection, refraction, and interference',
    fileName: 'physics-optics-demo-v1.mp4',
    originalFileName: 'Optics Experiments Demo.mp4',
    fileUrl: 'https://example.com/videos/physics-optics-demo-v1.mp4',
    streamingUrl: 'https://stream.example.com/physics-optics-demo-v1/playlist.m3u8',
    thumbnailUrl: 'https://example.com/thumbnails/optics-demo.jpg',
    fileSize: 209715200, // 200MB
    duration: 1800, // 30 minutes
    resolution: '1920x1080',
    quality: 'FHD',
    format: 'mp4',
    examId: '4',
    examName: 'Class 12 CBSE',
    courseId: '4',
    courseName: 'Class 12 Physics CBSE',
    subjectId: '7',
    subjectName: 'Optics',
    category: 'DEMO',
    tags: ['Physics', 'Optics', 'Experiments', 'Class 12', 'CBSE', 'Demo'],
    isPublic: true,
    isFree: true,
    isProcessed: true,
    processingStatus: 'COMPLETED',
    viewCount: 1567,
    likeCount: 134,
    dislikeCount: 7,
    rating: 4.5,
    totalRatings: 141,
    uploadedBy: 'Mr. Suresh Gupta',
    uploadedAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
    publishedAt: '2024-01-08T13:00:00Z',
    version: 1,
    language: 'ENGLISH',
    difficulty: 'EASY',
    hasSubtitles: false,
    subtitleLanguages: [],
    chapters: [
      {
        id: 'ch1',
        title: 'Reflection Experiments',
        startTime: 0,
        endTime: 600,
        description: 'Mirror reflection demonstrations'
      },
      {
        id: 'ch2',
        title: 'Refraction Experiments',
        startTime: 600,
        endTime: 1200,
        description: 'Light refraction through different media'
      },
      {
        id: 'ch3',
        title: 'Interference Patterns',
        startTime: 1200,
        endTime: 1800,
        description: 'Young\'s double slit experiment'
      }
    ]
  }
]

export const getVideos = (filters?: VideoFilters): Promise<Video[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredVideos = [...mockVideos]
      
      if (filters) {
        if (filters.examId) {
          filteredVideos = filteredVideos.filter(v => v.examId === filters.examId)
        }
        if (filters.courseId) {
          filteredVideos = filteredVideos.filter(v => v.courseId === filters.courseId)
        }
        if (filters.subjectId) {
          filteredVideos = filteredVideos.filter(v => v.subjectId === filters.subjectId)
        }
        if (filters.classId) {
          filteredVideos = filteredVideos.filter(v => v.classId === filters.classId)
        }
        if (filters.category) {
          filteredVideos = filteredVideos.filter(v => v.category === filters.category)
        }
        if (filters.isPublic !== undefined) {
          filteredVideos = filteredVideos.filter(v => v.isPublic === filters.isPublic)
        }
        if (filters.isFree !== undefined) {
          filteredVideos = filteredVideos.filter(v => v.isFree === filters.isFree)
        }
        if (filters.isProcessed !== undefined) {
          filteredVideos = filteredVideos.filter(v => v.isProcessed === filters.isProcessed)
        }
        if (filters.processingStatus) {
          filteredVideos = filteredVideos.filter(v => v.processingStatus === filters.processingStatus)
        }
        if (filters.language) {
          filteredVideos = filteredVideos.filter(v => v.language === filters.language)
        }
        if (filters.difficulty) {
          filteredVideos = filteredVideos.filter(v => v.difficulty === filters.difficulty)
        }
        if (filters.quality) {
          filteredVideos = filteredVideos.filter(v => v.quality === filters.quality)
        }
        if (filters.hasSubtitles !== undefined) {
          filteredVideos = filteredVideos.filter(v => v.hasSubtitles === filters.hasSubtitles)
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredVideos = filteredVideos.filter(v => 
            v.title.toLowerCase().includes(searchLower) ||
            v.description?.toLowerCase().includes(searchLower) ||
            v.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
            v.uploadedBy.toLowerCase().includes(searchLower)
          )
        }
        if (filters.tags && filters.tags.length > 0) {
          filteredVideos = filteredVideos.filter(v => 
            filters.tags!.some(tag => v.tags.includes(tag))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredVideos.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredVideos)
    }, 100)
  })
}

export const getVideoById = (id: string): Promise<Video | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const video = mockVideos.find(v => v.id === id) || null
      resolve(video)
    }, 100)
  })
}

export const getVideoStats = (): Promise<VideoStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: VideoStats = {
        totalVideos: mockVideos.length,
        publicVideos: mockVideos.filter(v => v.isPublic).length,
        freeVideos: mockVideos.filter(v => v.isFree).length,
        processedVideos: mockVideos.filter(v => v.isProcessed).length,
        totalViews: mockVideos.reduce((sum, v) => sum + v.viewCount, 0),
        totalLikes: mockVideos.reduce((sum, v) => sum + v.likeCount, 0),
        averageRating: mockVideos.reduce((sum, v) => sum + v.rating, 0) / mockVideos.length,
        totalDuration: mockVideos.reduce((sum, v) => sum + v.duration, 0),
        totalFileSize: mockVideos.reduce((sum, v) => sum + v.fileSize, 0)
      }
      resolve(stats)
    }, 100)
  })
}

export const createVideo = (data: CreateVideoRequest): Promise<Video> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newVideo: Video = {
        id: Date.now().toString(),
        ...data,
        streamingUrl: `https://stream.example.com/${data.fileName}/playlist.m3u8`,
        thumbnailUrl: `https://example.com/thumbnails/${data.fileName}.jpg`,
        isProcessed: false,
        processingStatus: 'PENDING',
        viewCount: 0,
        likeCount: 0,
        dislikeCount: 0,
        rating: 0,
        totalRatings: 0,
        uploadedBy: 'Admin User',
        uploadedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1,
        hasSubtitles: false,
        subtitleLanguages: [],
        chapters: data.chapters?.map(ch => ({ ...ch, id: Math.random().toString(36).substr(2, 9) })) || []
      }
      mockVideos.push(newVideo)
      resolve(newVideo)
    }, 200)
  })
}

export const updateVideo = (data: UpdateVideoRequest): Promise<Video> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockVideos.findIndex(v => v.id === data.id)
      if (index === -1) {
        reject(new Error('Video not found'))
        return
      }
      
      mockVideos[index] = {
        ...mockVideos[index],
        ...data,
        updatedAt: new Date().toISOString(),
        version: mockVideos[index].version + 1
      }
      resolve(mockVideos[index])
    }, 200)
  })
}

export const deleteVideo = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockVideos.findIndex(v => v.id === id)
      if (index === -1) {
        reject(new Error('Video not found'))
        return
      }
      
      mockVideos.splice(index, 1)
      resolve()
    }, 200)
  })
}
