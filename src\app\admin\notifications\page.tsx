'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Send,
  Bell,
  BellRing,
  MessageSquare,
  Mail,
  Smartphone,
  Globe,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Star,
  TrendingUp,
  BarChart3,
  Filter,
  MoreHorizontal,
  Copy,
  Pause,
  Play,
  Download,
  Settings,
  Zap,
  Target,
  Activity
} from 'lucide-react'
import { 
  Notification, 
  NotificationFilters, 
  NotificationStats, 
  CreateNotificationRequest,
  NotificationTemplate,
  NotificationGroup
} from '@/types/notification'
import { 
  getNotifications, 
  deleteNotification, 
  getNotificationStats, 
  createNotification,
  getNotificationTemplates,
  getNotificationGroups
} from '@/lib/notification-data'
import { AddNotificationModal } from '@/components/notifications/add-notification-modal'

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [templates, setTemplates] = useState<NotificationTemplate[]>([])
  const [groups, setGroups] = useState<NotificationGroup[]>([])
  const [stats, setStats] = useState<NotificationStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [filters, setFilters] = useState<NotificationFilters>({
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadNotifications()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [notificationsData, templatesData, groupsData, statsData] = await Promise.all([
        getNotifications(),
        getNotificationTemplates(),
        getNotificationGroups(),
        getNotificationStats()
      ])
      setNotifications(notificationsData)
      setTemplates(templatesData)
      setGroups(groupsData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadNotifications = async () => {
    try {
      const data = await getNotifications(filters)
      setNotifications(data)
    } catch (error) {
      console.error('Failed to load notifications:', error)
    }
  }

  const handleDeleteNotification = async (id: string) => {
    if (confirm('Are you sure you want to delete this notification? This action cannot be undone.')) {
      try {
        await deleteNotification(id)
        await loadNotifications()
        // Reload stats
        const statsData = await getNotificationStats()
        setStats(statsData)
      } catch (error) {
        console.error('Failed to delete notification:', error)
      }
    }
  }

  const handleFilterChange = (key: keyof NotificationFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleAddNotification = async (data: CreateNotificationRequest) => {
    try {
      await createNotification(data)
      await loadNotifications()
      // Reload stats
      const statsData = await getNotificationStats()
      setStats(statsData)
      setShowAddModal(false)
    } catch (error) {
      console.error('Failed to add notification:', error)
      throw error
    }
  }

  const handleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId) 
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    )
  }

  const handleSelectAll = () => {
    setSelectedNotifications(
      selectedNotifications.length === notifications.length 
        ? [] 
        : notifications.map(n => n.id)
    )
  }

  const formatDateTime = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'INFO': return 'bg-blue-100 text-blue-800'
      case 'SUCCESS': return 'bg-green-100 text-green-800'
      case 'WARNING': return 'bg-yellow-100 text-yellow-800'
      case 'ERROR': return 'bg-red-100 text-red-800'
      case 'ANNOUNCEMENT': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INFO': return <Info className="w-4 h-4" />
      case 'SUCCESS': return <CheckCircle className="w-4 h-4" />
      case 'WARNING': return <AlertTriangle className="w-4 h-4" />
      case 'ERROR': return <XCircle className="w-4 h-4" />
      case 'ANNOUNCEMENT': return <BellRing className="w-4 h-4" />
      default: return <Bell className="w-4 h-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'SYSTEM': return 'bg-gray-100 text-gray-800'
      case 'ACADEMIC': return 'bg-blue-100 text-blue-800'
      case 'PAYMENT': return 'bg-green-100 text-green-800'
      case 'CLASS': return 'bg-purple-100 text-purple-800'
      case 'EXAM': return 'bg-orange-100 text-orange-800'
      case 'GENERAL': return 'bg-indigo-100 text-indigo-800'
      case 'MARKETING': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'bg-gray-100 text-gray-800'
      case 'MEDIUM': return 'bg-blue-100 text-blue-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'URGENT': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'SCHEDULED': return 'bg-blue-100 text-blue-800'
      case 'SENDING': return 'bg-yellow-100 text-yellow-800'
      case 'SENT': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'CANCELLED': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DRAFT': return <Edit className="w-4 h-4" />
      case 'SCHEDULED': return <Clock className="w-4 h-4" />
      case 'SENDING': return <Send className="w-4 h-4" />
      case 'SENT': return <CheckCircle className="w-4 h-4" />
      case 'FAILED': return <XCircle className="w-4 h-4" />
      case 'CANCELLED': return <XCircle className="w-4 h-4" />
      default: return <Bell className="w-4 h-4" />
    }
  }

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'IN_APP': return <Bell className="w-4 h-4" />
      case 'EMAIL': return <Mail className="w-4 h-4" />
      case 'SMS': return <Smartphone className="w-4 h-4" />
      case 'PUSH': return <BellRing className="w-4 h-4" />
      case 'WHATSAPP': return <MessageSquare className="w-4 h-4" />
      default: return <Globe className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading notifications...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Notification Center</h1>
          <p className="text-gray-600 mt-1">Manage communications, alerts, and announcements</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </Button>
          <Button variant="outline" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Templates</span>
          </Button>
          <Button 
            className="flex items-center space-x-2"
            onClick={() => setShowAddModal(true)}
          >
            <Plus className="w-4 h-4" />
            <span>Create Notification</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-8 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sent</CardTitle>
              <Send className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalNotifications}</div>
              <p className="text-xs text-muted-foreground">All notifications</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.sentToday}</div>
              <p className="text-xs text-muted-foreground">Sent today</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.sentThisWeek}</div>
              <p className="text-xs text-muted-foreground">Weekly total</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Open Rate</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.avgOpenRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Average opens</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.avgClickRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Average clicks</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Delivery</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.avgDeliveryRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Delivery rate</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Engagement</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalEngagement}</div>
              <p className="text-xs text-muted-foreground">Total interactions</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+{stats.monthlyGrowth.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Monthly growth</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search notifications by title, message, or tags..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={filters.type || 'all'} onValueChange={(value) => handleFilterChange('type', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="INFO">Info</SelectItem>
                <SelectItem value="SUCCESS">Success</SelectItem>
                <SelectItem value="WARNING">Warning</SelectItem>
                <SelectItem value="ERROR">Error</SelectItem>
                <SelectItem value="ANNOUNCEMENT">Announcement</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="SYSTEM">System</SelectItem>
                <SelectItem value="ACADEMIC">Academic</SelectItem>
                <SelectItem value="PAYMENT">Payment</SelectItem>
                <SelectItem value="CLASS">Class</SelectItem>
                <SelectItem value="EXAM">Exam</SelectItem>
                <SelectItem value="GENERAL">General</SelectItem>
                <SelectItem value="MARKETING">Marketing</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                <SelectItem value="SENDING">Sending</SelectItem>
                <SelectItem value="SENT">Sent</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.priority || 'all'} onValueChange={(value) => handleFilterChange('priority', value === 'all' ? undefined : value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="URGENT">Urgent</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>More Filters</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Bulk Actions */}
          {selectedNotifications.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  {selectedNotifications.length} notification{selectedNotifications.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline">
                    <Copy className="w-4 h-4 mr-1" />
                    Duplicate
                  </Button>
                  <Button size="sm" variant="outline">
                    <Pause className="w-4 h-4 mr-1" />
                    Pause
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
            </div>
          )}

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedNotifications.length === notifications.length && notifications.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300"
                  />
                </TableHead>
                <TableHead>Notification Details</TableHead>
                <TableHead>Type & Category</TableHead>
                <TableHead>Recipients</TableHead>
                <TableHead>Channels</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Schedule</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {notifications.map((notification) => (
                <TableRow key={notification.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={() => handleSelectNotification(notification.id)}
                      className="rounded border-gray-300"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(notification.type)}
                        <div className="font-medium text-sm line-clamp-1">{notification.title}</div>
                      </div>
                      <div className="text-xs text-gray-500 line-clamp-2">{notification.message}</div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getPriorityColor(notification.priority)} variant="outline">
                          {notification.priority}
                        </Badge>
                        {notification.template && (
                          <Badge variant="outline" className="text-xs">
                            Template
                          </Badge>
                        )}
                        {notification.campaign && (
                          <Badge variant="outline" className="text-xs">
                            Campaign
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {notification.tags.slice(0, 2).map((tag, index) => (
                          <span key={index} className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                            {tag}
                          </span>
                        ))}
                        {notification.tags.length > 2 && (
                          <span className="text-xs text-gray-400">+{notification.tags.length - 2} more</span>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getTypeColor(notification.type)}>
                        <div className="flex items-center space-x-1">
                          {getTypeIcon(notification.type)}
                          <span>{notification.type}</span>
                        </div>
                      </Badge>
                      <Badge className={getCategoryColor(notification.category)} variant="outline">
                        {notification.category}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-1">
                        <Users className="w-3 h-3 text-gray-400" />
                        <span className="text-sm font-medium">{notification.recipientCount}</span>
                      </div>
                      <div className="text-xs text-gray-500">{notification.recipientType.replace('_', ' ')}</div>
                      {notification.recipientType === 'GROUP' && notification.recipients.length > 0 && (
                        <div className="text-xs text-blue-600">
                          {notification.recipients.length} group{notification.recipients.length > 1 ? 's' : ''}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      {notification.channels.filter(c => c.enabled).map((channel, index) => (
                        <div key={index} className="flex items-center space-x-1" title={channel.type}>
                          {getChannelIcon(channel.type)}
                        </div>
                      ))}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {notification.channels.filter(c => c.enabled).length} channel{notification.channels.filter(c => c.enabled).length > 1 ? 's' : ''}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{notification.analytics.openRate.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Target className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{notification.analytics.clickRate.toFixed(1)}%</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {notification.deliveryStatus.delivered}/{notification.deliveryStatus.total} delivered
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge className={getStatusColor(notification.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(notification.status)}
                          <span>{notification.status}</span>
                        </div>
                      </Badge>
                      {notification.status === 'FAILED' && (
                        <div className="text-xs text-red-600">
                          {notification.deliveryStatus.failed} failed
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {formatDateTime(notification.createdAt)}
                      </div>
                      {notification.scheduledAt && (
                        <div className="text-xs text-blue-600">
                          Scheduled: {formatDateTime(notification.scheduledAt)}
                        </div>
                      )}
                      {notification.sentAt && (
                        <div className="text-xs text-green-600">
                          Sent: {formatDateTime(notification.sentAt)}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Button variant="outline" size="sm" title="View Details">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="Edit Notification">
                        <Edit className="w-4 h-4" />
                      </Button>
                      {notification.status === 'DRAFT' && (
                        <Button variant="outline" size="sm" title="Send Now">
                          <Send className="w-4 h-4" />
                        </Button>
                      )}
                      {notification.status === 'SCHEDULED' && (
                        <Button variant="outline" size="sm" title="Pause">
                          <Pause className="w-4 h-4" />
                        </Button>
                      )}
                      <Button variant="outline" size="sm" title="Analytics">
                        <BarChart3 className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" title="More Options">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        title="Delete Notification"
                        onClick={() => handleDeleteNotification(notification.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {notifications.length === 0 && (
            <div className="text-center py-8">
              <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No notifications found matching your filters.</p>
              <Button onClick={() => setShowAddModal(true)}>
                Create your first notification
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Notification Modal */}
      <AddNotificationModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddNotification}
        templates={templates}
        groups={groups}
      />
    </div>
  )
}
