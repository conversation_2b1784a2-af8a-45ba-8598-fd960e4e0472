'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { FileUpload } from '@/components/ui/file-upload'
import { Progress } from '@/components/ui/progress'
import { VideoPlayer } from '@/components/ui/video-player'
import { X, Upload, CheckCircle, AlertCircle, Video, Clock, HardDrive } from 'lucide-react'
import { CreateVideoRequest } from '@/types/video'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'
import { Exam } from '@/types/exam'

interface VideoUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUpload: (data: CreateVideoRequest) => Promise<void>
  courses: Course[]
  subjects: Subject[]
  exams: Exam[]
}

interface UploadState {
  files: File[]
  uploading: boolean
  progress: number
  completed: boolean
  error?: string
  previewUrl?: string
  videoMetadata?: {
    duration: number
    resolution: string
    fileSize: number
  }
}

export function VideoUploadModal({ 
  isOpen, 
  onClose, 
  onUpload, 
  courses, 
  subjects, 
  exams 
}: VideoUploadModalProps) {
  const [formData, setFormData] = useState<Partial<CreateVideoRequest>>({
    category: 'LECTURE',
    language: 'ENGLISH',
    difficulty: 'MEDIUM',
    quality: 'FHD',
    isPublic: true,
    isFree: false,
    tags: []
  })
  const [uploadState, setUploadState] = useState<UploadState>({
    files: [],
    uploading: false,
    progress: 0,
    completed: false
  })
  const [tagInput, setTagInput] = useState('')
  const [chapters, setChapters] = useState<Array<{title: string, startTime: number, description?: string}>>([])

  const handleFileSelect = (files: File[]) => {
    if (files.length > 0) {
      const file = files[0]
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      
      // Extract video metadata (in real app, this would be more sophisticated)
      const video = document.createElement('video')
      video.preload = 'metadata'
      video.onloadedmetadata = () => {
        setUploadState(prev => ({
          ...prev,
          files: [file],
          previewUrl,
          videoMetadata: {
            duration: Math.round(video.duration),
            resolution: `${video.videoWidth}x${video.videoHeight}`,
            fileSize: file.size
          }
        }))
        
        // Determine quality based on resolution
        let quality: 'SD' | 'HD' | 'FHD' | '4K' = 'SD'
        if (video.videoHeight >= 2160) quality = '4K'
        else if (video.videoHeight >= 1080) quality = 'FHD'
        else if (video.videoHeight >= 720) quality = 'HD'
        
        setFormData(prev => ({
          ...prev,
          fileName: file.name,
          originalFileName: file.name,
          fileSize: file.size,
          duration: Math.round(video.duration),
          resolution: `${video.videoWidth}x${video.videoHeight}`,
          quality,
          format: file.type.split('/')[1] || 'mp4',
          title: prev.title || file.name.replace(/\.[^/.]+$/, '')
        }))
      }
      video.src = previewUrl
    }
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleAddChapter = () => {
    setChapters(prev => [...prev, { title: '', startTime: 0, description: '' }])
  }

  const handleUpdateChapter = (index: number, field: string, value: any) => {
    setChapters(prev => prev.map((chapter, i) => 
      i === index ? { ...chapter, [field]: value } : chapter
    ))
  }

  const handleRemoveChapter = (index: number) => {
    setChapters(prev => prev.filter((_, i) => i !== index))
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!uploadState.files.length || !formData.title) {
      return
    }

    setUploadState(prev => ({ ...prev, uploading: true, progress: 0, error: undefined }))

    try {
      // Simulate video upload and processing progress
      const progressSteps = [
        { step: 'Uploading video...', progress: 30 },
        { step: 'Processing video...', progress: 60 },
        { step: 'Generating thumbnail...', progress: 80 },
        { step: 'Creating streaming formats...', progress: 95 },
        { step: 'Finalizing...', progress: 100 }
      ]

      for (const { step, progress } of progressSteps) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        setUploadState(prev => ({ ...prev, progress }))
      }

      // In real app, upload file to cloud storage and get URLs
      const mockFileUrl = `https://example.com/videos/${formData.fileName}`
      
      const uploadData: CreateVideoRequest = {
        title: formData.title!,
        description: formData.description,
        fileName: formData.fileName!,
        originalFileName: formData.originalFileName!,
        fileUrl: mockFileUrl,
        fileSize: formData.fileSize!,
        duration: formData.duration!,
        resolution: formData.resolution!,
        quality: formData.quality!,
        format: formData.format!,
        examId: formData.examId,
        courseId: formData.courseId,
        subjectId: formData.subjectId,
        classId: formData.classId,
        category: formData.category!,
        tags: formData.tags || [],
        isPublic: formData.isPublic!,
        isFree: formData.isFree!,
        language: formData.language!,
        difficulty: formData.difficulty!,
        chapters: chapters.filter(ch => ch.title.trim())
      }

      await onUpload(uploadData)
      
      setUploadState(prev => ({ ...prev, completed: true, uploading: false }))
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setUploadState(prev => ({ 
        ...prev, 
        uploading: false, 
        error: error instanceof Error ? error.message : 'Upload failed' 
      }))
    }
  }

  const resetForm = () => {
    setFormData({
      category: 'LECTURE',
      language: 'ENGLISH',
      difficulty: 'MEDIUM',
      quality: 'FHD',
      isPublic: true,
      isFree: false,
      tags: []
    })
    setUploadState({
      files: [],
      uploading: false,
      progress: 0,
      completed: false
    })
    setTagInput('')
    setChapters([])
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold">Upload Video</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {uploadState.completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Upload Successful!</h3>
              <p className="text-gray-600">Your video has been uploaded and is being processed.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* File Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video File *
                </label>
                <FileUpload
                  accept=".mp4,.avi,.mov,.wmv,.flv,.webm"
                  maxSize={2 * 1024 * 1024 * 1024} // 2GB
                  onFileSelect={handleFileSelect}
                  disabled={uploadState.uploading}
                />
              </div>

              {/* Video Preview */}
              {uploadState.previewUrl && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Video Preview
                    </label>
                    <VideoPlayer
                      src={uploadState.previewUrl}
                      title={formData.title}
                      width={400}
                      height={225}
                      controls={true}
                    />
                  </div>
                  
                  {uploadState.videoMetadata && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Video Information
                      </label>
                      <Card className="p-4">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">Duration: {formatTime(uploadState.videoMetadata.duration)}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Video className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">Resolution: {uploadState.videoMetadata.resolution}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <HardDrive className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">Size: {formatFileSize(uploadState.videoMetadata.fileSize)}</span>
                          </div>
                        </div>
                      </Card>
                    </div>
                  )}
                </div>
              )}

              {/* Upload Progress */}
              {uploadState.uploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Processing video...</span>
                    <span className="text-sm text-gray-600">{uploadState.progress}%</span>
                  </div>
                  <Progress value={uploadState.progress} />
                </div>
              )}

              {/* Error Display */}
              {uploadState.error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{uploadState.error}</span>
                </div>
              )}

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title *
                  </label>
                  <Input
                    value={formData.title || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter video title"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category *
                  </label>
                  <Select 
                    value={formData.category} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LECTURE">Lecture</SelectItem>
                      <SelectItem value="TUTORIAL">Tutorial</SelectItem>
                      <SelectItem value="DEMO">Demo</SelectItem>
                      <SelectItem value="EXPLANATION">Explanation</SelectItem>
                      <SelectItem value="PRACTICE">Practice</SelectItem>
                      <SelectItem value="REVIEW">Review</SelectItem>
                      <SelectItem value="OTHER">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  value={formData.description || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter video description..."
                />
              </div>

              {/* Course/Subject Selection */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Exam
                  </label>
                  <Select 
                    value={formData.examId || 'none'} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, examId: value === 'none' ? undefined : value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select exam" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No specific exam</SelectItem>
                      {exams.map((exam) => (
                        <SelectItem key={exam.id} value={exam.id}>
                          {exam.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Course
                  </label>
                  <Select 
                    value={formData.courseId || 'none'} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, courseId: value === 'none' ? undefined : value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select course" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No specific course</SelectItem>
                      {courses.map((course) => (
                        <SelectItem key={course.id} value={course.id}>
                          {course.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <Select 
                    value={formData.subjectId || 'none'} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value === 'none' ? undefined : value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No specific subject</SelectItem>
                      {subjects.map((subject) => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Properties */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Language
                  </label>
                  <Select 
                    value={formData.language} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, language: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ENGLISH">English</SelectItem>
                      <SelectItem value="HINDI">Hindi</SelectItem>
                      <SelectItem value="BOTH">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Difficulty
                  </label>
                  <Select 
                    value={formData.difficulty} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EASY">Easy</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HARD">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Quality
                  </label>
                  <Select 
                    value={formData.quality} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, quality: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SD">SD (480p)</SelectItem>
                      <SelectItem value="HD">HD (720p)</SelectItem>
                      <SelectItem value="FHD">FHD (1080p)</SelectItem>
                      <SelectItem value="4K">4K (2160p)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <div className="flex items-center space-x-2 mb-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Add a tag"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                  />
                  <Button type="button" onClick={handleAddTag} size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.tags?.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Visibility Settings */}
              <div className="flex items-center space-x-6">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isPublic}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">Make public</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isFree}
                    onChange={(e) => setFormData(prev => ({ ...prev, isFree: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">Free to watch</span>
                </label>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!uploadState.files.length || !formData.title || uploadState.uploading}
                  className="flex items-center space-x-2"
                >
                  <Upload className="w-4 h-4" />
                  <span>{uploadState.uploading ? 'Uploading...' : 'Upload Video'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
