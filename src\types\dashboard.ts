export interface DashboardStats {
  totalUsers: number
  totalCourses: number
  totalVideos: number
  newSignupsThisWeek: number
  totalRevenue: number
  revenueGrowth: number
}

export interface RevenueData {
  date: string
  revenue: number
}

export interface EngagementData {
  course: string
  students: number
  completion: number
}

export interface PopularCourse {
  id: string
  name: string
  students: number
  revenue: number
  rating: number
}

export interface RecentActivity {
  id: string
  type: 'enrollment' | 'payment' | 'completion' | 'test'
  message: string
  timestamp: string
  user: string
}
