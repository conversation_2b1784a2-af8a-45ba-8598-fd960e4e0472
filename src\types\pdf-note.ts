export interface PDFNote {
  id: string
  title: string
  description?: string
  fileName: string
  fileUrl: string
  fileSize: number // in bytes
  thumbnailUrl?: string
  examId?: string
  examName?: string
  courseId?: string
  courseName?: string
  subjectId?: string
  subjectName?: string
  classId?: string
  className?: string
  category: 'NOTES' | 'FORMULA_SHEET' | 'SUMMARY' | 'PRACTICE_PROBLEMS' | 'REFERENCE' | 'OTHER'
  tags: string[]
  isPublic: boolean
  isFree: boolean
  downloadCount: number
  viewCount: number
  rating: number
  totalRatings: number
  uploadedBy: string
  uploadedAt: string
  updatedAt: string
  version: number
  language: 'ENGLISH' | 'HINDI' | 'BOTH'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  pageCount?: number
}

export interface CreatePDFNoteRequest {
  title: string
  description?: string
  fileName: string
  fileUrl: string
  fileSize: number
  thumbnailUrl?: string
  examId?: string
  courseId?: string
  subjectId?: string
  classId?: string
  category: PDFNote['category']
  tags: string[]
  isPublic: boolean
  isFree: boolean
  language: PDFNote['language']
  difficulty: PDFNote['difficulty']
  pageCount?: number
}

export interface UpdatePDFNoteRequest extends Partial<CreatePDFNoteRequest> {
  id: string
}

export interface PDFNoteFilters {
  examId?: string
  courseId?: string
  subjectId?: string
  classId?: string
  category?: PDFNote['category']
  isPublic?: boolean
  isFree?: boolean
  language?: PDFNote['language']
  difficulty?: PDFNote['difficulty']
  search?: string
  tags?: string[]
  sortBy?: 'title' | 'uploadedAt' | 'downloadCount' | 'rating' | 'fileSize'
  sortOrder?: 'asc' | 'desc'
}

export interface PDFNoteStats {
  totalNotes: number
  publicNotes: number
  freeNotes: number
  totalDownloads: number
  totalViews: number
  averageRating: number
  totalFileSize: number // in bytes
}

export interface PDFUploadProgress {
  id: string
  fileName: string
  progress: number
  status: 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
}
