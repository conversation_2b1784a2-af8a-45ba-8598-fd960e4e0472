globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/exams/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/sidebar.tsx":{"*":{"id":"(ssr)/./src/components/layout/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/engagement-chart.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/engagement-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/popular-courses.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/popular-courses.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/recent-activity.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/recent-activity.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/revenue-chart.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/revenue-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/stats-cards.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/stats-cards.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/exams/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/exams/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/courses/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/courses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/notifications/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/notifications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/subjects/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/subjects/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\projects\\utkrishta_admin\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\projects\\utkrishta_admin\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\projects\\utkrishta_admin\\src\\components\\layout\\sidebar.tsx":{"id":"(app-pages-browser)/./src/components/layout/sidebar.tsx","name":"*","chunks":["app/admin/layout","static/chunks/app/admin/layout.js"],"async":false},"D:\\projects\\utkrishta_admin\\src\\components\\dashboard\\engagement-chart.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/engagement-chart.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\components\\dashboard\\popular-courses.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/popular-courses.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\components\\dashboard\\recent-activity.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/recent-activity.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\components\\dashboard\\revenue-chart.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/revenue-chart.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\components\\dashboard\\stats-cards.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/stats-cards.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\app\\admin\\exams\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/exams/page.tsx","name":"*","chunks":["app/admin/exams/page","static/chunks/app/admin/exams/page.js"],"async":false},"D:\\projects\\utkrishta_admin\\src\\app\\admin\\courses\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/courses/page.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\app\\admin\\notifications\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/notifications/page.tsx","name":"*","chunks":[],"async":false},"D:\\projects\\utkrishta_admin\\src\\app\\admin\\subjects\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/subjects/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\projects\\utkrishta_admin\\src\\":[],"D:\\projects\\utkrishta_admin\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\projects\\utkrishta_admin\\src\\app\\page":[],"D:\\projects\\utkrishta_admin\\src\\app\\admin\\layout":[],"D:\\projects\\utkrishta_admin\\src\\app\\admin\\exams\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/sidebar.tsx":{"*":{"id":"(rsc)/./src/components/layout/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/engagement-chart.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/engagement-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/popular-courses.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/popular-courses.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/recent-activity.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/recent-activity.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/revenue-chart.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/revenue-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/stats-cards.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/stats-cards.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/exams/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/exams/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/courses/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/courses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/notifications/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/notifications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/subjects/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/subjects/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}