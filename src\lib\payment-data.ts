import { Payment, CreatePaymentRequest, UpdatePaymentRequest, PaymentFilters, PaymentStats, Subscription, Refund, PaymentPlan } from '@/types/payment'

// Mock data - In real app, this would come from your API
const mockPayments: Payment[] = [
  {
    id: '1',
    transactionId: 'TXN_001_2024',
    orderId: 'ORD_001_2024',
    studentId: '1',
    studentName: '<PERSON><PERSON><PERSON>',
    studentEmail: '<EMAIL>',
    studentPhone: '+91 9876543210',
    amount: 9999,
    currency: 'INR',
    status: 'SUCCESS',
    paymentMethod: 'UPI',
    paymentGateway: 'RAZORPAY',
    itemType: 'COURSE',
    itemId: '1',
    itemName: 'JEE Main Physics Complete Course',
    itemDescription: 'Complete physics course for JEE Main preparation',
    originalAmount: 12999,
    discountAmount: 3000,
    discountCode: 'EARLY30',
    taxAmount: 1800,
    processingFee: 199,
    finalAmount: 9999,
    gatewayTransactionId: 'pay_razorpay_123456',
    gatewayOrderId: 'order_razorpay_789012',
    gatewayPaymentId: 'pay_razorpay_345678',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:05:00Z',
    paidAt: '2024-01-15T10:05:00Z',
    tags: ['JEE', 'Physics', 'Premium'],
    billingAddress: {
      name: 'Arjun Sharma',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      address: '123 Main Street, Sector 15',
      city: 'New Delhi',
      state: 'Delhi',
      country: 'India',
      pincode: '110001'
    },
    conversionSource: 'website',
    deviceInfo: 'Chrome/Windows'
  },
  {
    id: '2',
    transactionId: 'TXN_002_2024',
    orderId: 'ORD_002_2024',
    studentId: '2',
    studentName: 'Priya Patel',
    studentEmail: '<EMAIL>',
    studentPhone: '+91 9876543212',
    amount: 12999,
    currency: 'INR',
    status: 'SUCCESS',
    paymentMethod: 'CREDIT_CARD',
    paymentGateway: 'RAZORPAY',
    itemType: 'COURSE',
    itemId: '2',
    itemName: 'NEET Biology Masterclass',
    itemDescription: 'Comprehensive biology course for NEET preparation',
    subscriptionId: 'SUB_002_2024',
    subscriptionType: 'YEARLY',
    subscriptionStartDate: '2024-01-10T09:00:00Z',
    subscriptionEndDate: '2025-01-10T09:00:00Z',
    originalAmount: 12999,
    taxAmount: 2340,
    processingFee: 260,
    finalAmount: 12999,
    gatewayTransactionId: 'pay_razorpay_234567',
    gatewayOrderId: 'order_razorpay_890123',
    gatewayPaymentId: 'pay_razorpay_456789',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:03:00Z',
    paidAt: '2024-01-10T09:03:00Z',
    tags: ['NEET', 'Biology', 'Premium'],
    billingAddress: {
      name: 'Priya Patel',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      address: '456 Park Avenue, Bandra',
      city: 'Mumbai',
      state: 'Maharashtra',
      country: 'India',
      pincode: '400050'
    },
    conversionSource: 'mobile_app',
    deviceInfo: 'Safari/iOS'
  },
  {
    id: '3',
    transactionId: 'TXN_003_2024',
    orderId: 'ORD_003_2024',
    studentId: '3',
    studentName: 'Rahul Kumar',
    studentEmail: '<EMAIL>',
    studentPhone: '+91 **********',
    amount: 15999,
    currency: 'INR',
    status: 'SUCCESS',
    paymentMethod: 'NET_BANKING',
    paymentGateway: 'RAZORPAY',
    itemType: 'COURSE',
    itemId: '3',
    itemName: 'JEE Advanced Mathematics',
    itemDescription: 'Advanced mathematics course for JEE Advanced',
    originalAmount: 15999,
    taxAmount: 2880,
    processingFee: 320,
    finalAmount: 15999,
    gatewayTransactionId: 'pay_razorpay_345678',
    gatewayOrderId: 'order_razorpay_901234',
    gatewayPaymentId: 'pay_razorpay_567890',
    createdAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-05T11:08:00Z',
    paidAt: '2024-01-05T11:08:00Z',
    tags: ['JEE Advanced', 'Mathematics', 'Premium'],
    billingAddress: {
      name: 'Rahul Kumar',
      email: '<EMAIL>',
      phone: '+91 **********',
      address: '789 Tech Park, Electronic City',
      city: 'Bangalore',
      state: 'Karnataka',
      country: 'India',
      pincode: '560100'
    },
    conversionSource: 'google_ads',
    campaignId: 'CAMP_JEE_ADV_001',
    deviceInfo: 'Chrome/Android'
  },
  {
    id: '4',
    transactionId: 'TXN_004_2024',
    orderId: 'ORD_004_2024',
    studentId: '4',
    studentName: 'Sneha Singh',
    studentEmail: '<EMAIL>',
    studentPhone: '+91 9876543216',
    amount: 7999,
    currency: 'INR',
    status: 'SUCCESS',
    paymentMethod: 'UPI',
    paymentGateway: 'RAZORPAY',
    itemType: 'COURSE',
    itemId: '4',
    itemName: 'Class 12 Physics CBSE',
    itemDescription: 'Complete physics course for Class 12 CBSE',
    originalAmount: 7999,
    taxAmount: 1440,
    processingFee: 160,
    finalAmount: 7999,
    gatewayTransactionId: 'pay_razorpay_456789',
    gatewayOrderId: 'order_razorpay_012345',
    gatewayPaymentId: 'pay_razorpay_678901',
    createdAt: '2024-01-12T08:30:00Z',
    updatedAt: '2024-01-12T08:32:00Z',
    paidAt: '2024-01-12T08:32:00Z',
    tags: ['CBSE', 'Physics', 'Class 12'],
    billingAddress: {
      name: 'Sneha Singh',
      email: '<EMAIL>',
      phone: '+91 9876543216',
      address: '321 Anna Nagar, T. Nagar',
      city: 'Chennai',
      state: 'Tamil Nadu',
      country: 'India',
      pincode: '600017'
    },
    conversionSource: 'referral',
    referralCode: 'REF_FRIEND_001',
    deviceInfo: 'Chrome/Windows'
  },
  {
    id: '5',
    transactionId: 'TXN_005_2024',
    orderId: 'ORD_005_2024',
    studentId: '1',
    studentName: 'Arjun Sharma',
    studentEmail: '<EMAIL>',
    studentPhone: '+91 9876543210',
    amount: 2999,
    currency: 'INR',
    status: 'FAILED',
    paymentMethod: 'CREDIT_CARD',
    paymentGateway: 'RAZORPAY',
    itemType: 'TEST_SERIES',
    itemId: '1',
    itemName: 'JEE Main Mock Test Series',
    itemDescription: 'Complete mock test series for JEE Main',
    originalAmount: 2999,
    taxAmount: 540,
    processingFee: 60,
    finalAmount: 2999,
    gatewayTransactionId: 'pay_razorpay_567890',
    gatewayOrderId: 'order_razorpay_123456',
    createdAt: '2024-01-18T14:00:00Z',
    updatedAt: '2024-01-18T14:02:00Z',
    failedAt: '2024-01-18T14:02:00Z',
    failureReason: 'Insufficient funds',
    tags: ['JEE', 'Test Series', 'Failed'],
    billingAddress: {
      name: 'Arjun Sharma',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      address: '123 Main Street, Sector 15',
      city: 'New Delhi',
      state: 'Delhi',
      country: 'India',
      pincode: '110001'
    },
    conversionSource: 'website',
    deviceInfo: 'Chrome/Windows'
  },
  {
    id: '6',
    transactionId: 'TXN_006_2024',
    orderId: 'ORD_006_2024',
    studentId: '2',
    studentName: 'Priya Patel',
    studentEmail: '<EMAIL>',
    studentPhone: '+91 9876543212',
    amount: 5999,
    currency: 'INR',
    status: 'REFUNDED',
    paymentMethod: 'UPI',
    paymentGateway: 'RAZORPAY',
    itemType: 'LIVE_CLASS',
    itemId: '1',
    itemName: 'NEET Biology Live Classes',
    itemDescription: 'Live interactive biology classes for NEET',
    originalAmount: 5999,
    taxAmount: 1080,
    processingFee: 120,
    finalAmount: 5999,
    gatewayTransactionId: 'pay_razorpay_678901',
    gatewayOrderId: 'order_razorpay_234567',
    gatewayPaymentId: 'pay_razorpay_789012',
    createdAt: '2024-01-08T16:00:00Z',
    updatedAt: '2024-01-20T10:00:00Z',
    paidAt: '2024-01-08T16:01:00Z',
    refundedAt: '2024-01-20T10:00:00Z',
    refundAmount: 5999,
    refundReason: 'Student requested cancellation',
    tags: ['NEET', 'Live Class', 'Refunded'],
    billingAddress: {
      name: 'Priya Patel',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      address: '456 Park Avenue, Bandra',
      city: 'Mumbai',
      state: 'Maharashtra',
      country: 'India',
      pincode: '400050'
    },
    conversionSource: 'email_campaign',
    campaignId: 'EMAIL_NEET_001',
    deviceInfo: 'Safari/iOS'
  }
]

const mockSubscriptions: Subscription[] = [
  {
    id: 'SUB_001_2024',
    studentId: '1',
    studentName: 'Arjun Sharma',
    studentEmail: '<EMAIL>',
    planId: 'PLAN_PREMIUM_YEARLY',
    planName: 'Premium Yearly Plan',
    planType: 'SUBSCRIPTION',
    status: 'ACTIVE',
    startDate: '2024-01-15T10:00:00Z',
    endDate: '2025-01-15T10:00:00Z',
    autoRenew: true,
    amount: 9999,
    currency: 'INR',
    billingCycle: 'YEARLY',
    nextBillingDate: '2025-01-15T10:00:00Z',
    payments: [mockPayments[0]],
    totalPaid: 9999,
    lastAccessDate: '2024-01-20T14:30:00Z',
    accessCount: 45,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 'SUB_002_2024',
    studentId: '2',
    studentName: 'Priya Patel',
    studentEmail: '<EMAIL>',
    planId: 'PLAN_PREMIUM_YEARLY',
    planName: 'Premium Yearly Plan',
    planType: 'SUBSCRIPTION',
    status: 'ACTIVE',
    startDate: '2024-01-10T09:00:00Z',
    endDate: '2025-01-10T09:00:00Z',
    autoRenew: true,
    amount: 12999,
    currency: 'INR',
    billingCycle: 'YEARLY',
    nextBillingDate: '2025-01-10T09:00:00Z',
    payments: [mockPayments[1]],
    totalPaid: 12999,
    lastAccessDate: '2024-01-20T16:45:00Z',
    accessCount: 38,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-20T16:45:00Z'
  }
]

const mockPaymentPlans: PaymentPlan[] = [
  {
    id: 'PLAN_FREE',
    name: 'Free Plan',
    description: 'Basic access to free content',
    type: 'SUBSCRIPTION',
    originalPrice: 0,
    currentPrice: 0,
    currency: 'INR',
    duration: 12,
    durationType: 'MONTHS',
    features: ['Access to free courses', 'Basic support', 'Limited tests'],
    limitations: ['No premium content', 'Limited support'],
    isActive: true,
    isPopular: false,
    isFeatured: false,
    currentUsers: 150,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'admin'
  },
  {
    id: 'PLAN_BASIC_MONTHLY',
    name: 'Basic Monthly Plan',
    description: 'Access to basic courses with monthly billing',
    type: 'SUBSCRIPTION',
    originalPrice: 999,
    currentPrice: 799,
    currency: 'INR',
    duration: 1,
    durationType: 'MONTHS',
    features: ['Access to basic courses', 'Email support', 'Monthly tests', 'Progress tracking'],
    isActive: true,
    isPopular: false,
    isFeatured: false,
    currentUsers: 45,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    createdBy: 'admin'
  },
  {
    id: 'PLAN_PREMIUM_YEARLY',
    name: 'Premium Yearly Plan',
    description: 'Full access to all content with yearly billing',
    type: 'SUBSCRIPTION',
    originalPrice: 12999,
    currentPrice: 9999,
    currency: 'INR',
    duration: 12,
    durationType: 'MONTHS',
    features: [
      'Access to all courses',
      'Priority support',
      'Unlimited tests',
      'Live classes',
      'Doubt clearing sessions',
      'Performance analytics',
      'Mobile app access'
    ],
    isActive: true,
    isPopular: true,
    isFeatured: true,
    currentUsers: 89,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
    createdBy: 'admin'
  }
]

export const getPayments = (filters?: PaymentFilters): Promise<Payment[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredPayments = [...mockPayments]
      
      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredPayments = filteredPayments.filter(p => 
            p.studentName.toLowerCase().includes(searchLower) ||
            p.studentEmail.toLowerCase().includes(searchLower) ||
            p.transactionId.toLowerCase().includes(searchLower) ||
            p.itemName.toLowerCase().includes(searchLower)
          )
        }
        
        if (filters.status) {
          filteredPayments = filteredPayments.filter(p => p.status === filters.status)
        }
        
        if (filters.paymentMethod) {
          filteredPayments = filteredPayments.filter(p => p.paymentMethod === filters.paymentMethod)
        }
        
        if (filters.paymentGateway) {
          filteredPayments = filteredPayments.filter(p => p.paymentGateway === filters.paymentGateway)
        }
        
        if (filters.itemType) {
          filteredPayments = filteredPayments.filter(p => p.itemType === filters.itemType)
        }
        
        if (filters.studentId) {
          filteredPayments = filteredPayments.filter(p => p.studentId === filters.studentId)
        }
        
        if (filters.amountMin !== undefined) {
          filteredPayments = filteredPayments.filter(p => p.amount >= filters.amountMin!)
        }
        
        if (filters.amountMax !== undefined) {
          filteredPayments = filteredPayments.filter(p => p.amount <= filters.amountMax!)
        }
        
        if (filters.dateFrom) {
          filteredPayments = filteredPayments.filter(p => 
            new Date(p.createdAt) >= new Date(filters.dateFrom!)
          )
        }
        
        if (filters.dateTo) {
          filteredPayments = filteredPayments.filter(p => 
            new Date(p.createdAt) <= new Date(filters.dateTo!)
          )
        }
        
        if (filters.tags && filters.tags.length > 0) {
          filteredPayments = filteredPayments.filter(p => 
            filters.tags!.some(tag => p.tags.includes(tag))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredPayments.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredPayments)
    }, 100)
  })
}

export const getPaymentById = (id: string): Promise<Payment | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const payment = mockPayments.find(p => p.id === id) || null
      resolve(payment)
    }, 100)
  })
}

export const getPaymentStats = (): Promise<PaymentStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const successfulPayments = mockPayments.filter(p => p.status === 'SUCCESS')
      const failedPayments = mockPayments.filter(p => p.status === 'FAILED')
      const refundedPayments = mockPayments.filter(p => p.status === 'REFUNDED')
      
      const totalRevenue = successfulPayments.reduce((sum, p) => sum + p.amount, 0)
      const totalRefunds = refundedPayments.reduce((sum, p) => sum + (p.refundAmount || 0), 0)
      
      const stats: PaymentStats = {
        totalRevenue,
        monthlyRevenue: totalRevenue, // Simplified for mock
        yearlyRevenue: totalRevenue,
        averageOrderValue: totalRevenue / successfulPayments.length,
        totalTransactions: mockPayments.length,
        successfulTransactions: successfulPayments.length,
        failedTransactions: failedPayments.length,
        pendingTransactions: mockPayments.filter(p => p.status === 'PENDING').length,
        successRate: (successfulPayments.length / mockPayments.length) * 100,
        failureRate: (failedPayments.length / mockPayments.length) * 100,
        paymentMethodDistribution: [
          { method: 'UPI', count: 3, amount: 18997, percentage: 50 },
          { method: 'CREDIT_CARD', count: 2, amount: 15998, percentage: 33.3 },
          { method: 'NET_BANKING', count: 1, amount: 15999, percentage: 16.7 }
        ],
        activeSubscriptions: mockSubscriptions.filter(s => s.status === 'ACTIVE').length,
        expiredSubscriptions: mockSubscriptions.filter(s => s.status === 'EXPIRED').length,
        cancelledSubscriptions: mockSubscriptions.filter(s => s.status === 'CANCELLED').length,
        subscriptionRevenue: mockSubscriptions.reduce((sum, s) => sum + s.totalPaid, 0),
        totalRefunds: refundedPayments.length,
        refundAmount: totalRefunds,
        refundRate: (refundedPayments.length / mockPayments.length) * 100,
        monthlyGrowth: 15.5,
        yearlyGrowth: 45.2,
        topCourses: [
          { id: '1', name: 'JEE Main Physics Complete Course', revenue: 9999, sales: 1 },
          { id: '2', name: 'NEET Biology Masterclass', revenue: 12999, sales: 1 },
          { id: '3', name: 'JEE Advanced Mathematics', revenue: 15999, sales: 1 }
        ],
        topPlans: [
          { id: 'PLAN_PREMIUM_YEARLY', name: 'Premium Yearly Plan', revenue: 22998, subscribers: 2 }
        ],
        dailyRevenue: [
          { date: '2024-01-20', revenue: 5999, transactions: 1 },
          { date: '2024-01-19', revenue: 0, transactions: 0 },
          { date: '2024-01-18', revenue: 0, transactions: 1 }
        ],
        monthlyRevenue: [
          { month: 'Jan 2024', revenue: totalRevenue, transactions: mockPayments.length }
        ]
      }
      resolve(stats)
    }, 100)
  })
}

export const getSubscriptions = (): Promise<Subscription[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockSubscriptions)
    }, 100)
  })
}

export const getPaymentPlans = (): Promise<PaymentPlan[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockPaymentPlans)
    }, 100)
  })
}

export const createPayment = (data: CreatePaymentRequest): Promise<Payment> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newPayment: Payment = {
        id: Date.now().toString(),
        transactionId: `TXN_${Date.now()}`,
        orderId: `ORD_${Date.now()}`,
        ...data,
        currency: data.currency || 'INR',
        status: 'PENDING',
        originalAmount: data.amount,
        finalAmount: data.amount,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: data.tags || []
      }
      mockPayments.push(newPayment)
      resolve(newPayment)
    }, 200)
  })
}

export const updatePayment = (data: UpdatePaymentRequest): Promise<Payment> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockPayments.findIndex(p => p.id === data.id)
      if (index === -1) {
        reject(new Error('Payment not found'))
        return
      }
      
      mockPayments[index] = {
        ...mockPayments[index],
        ...data,
        updatedAt: new Date().toISOString(),
        paidAt: data.status === 'SUCCESS' ? new Date().toISOString() : mockPayments[index].paidAt,
        failedAt: data.status === 'FAILED' ? new Date().toISOString() : mockPayments[index].failedAt
      }
      resolve(mockPayments[index])
    }, 200)
  })
}
