import { LiveClass, CreateLiveClassRequest, UpdateLiveClassRequest, LiveClassFilters, LiveClassStats, LiveClassRegistration, Instructor } from '@/types/live-class'

// Mock data - In real app, this would come from your API
const mockInstructors: Instructor[] = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    email: 'r<PERSON><PERSON>.<EMAIL>',
    phone: '+91 9876543210',
    bio: 'Physics expert with 15+ years of teaching experience. Specialized in JEE Main and Advanced preparation.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    specializations: ['Physics', 'JEE Main', 'JEE Advanced', 'Mechanics', 'Thermodynamics'],
    experience: 15,
    rating: 4.8,
    totalClasses: 245,
    totalStudents: 1250,
    isActive: true,
    availability: [
      { dayOfWeek: 1, startTime: '09:00', endTime: '12:00', timezone: 'Asia/Kolkata' },
      { dayOfWeek: 3, startTime: '14:00', endTime: '17:00', timezone: 'Asia/Kolkata' },
      { dayOfWeek: 5, startTime: '10:00', endTime: '13:00', timezone: 'Asia/Kolkata' }
    ],
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: '2',
    name: 'Dr. Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 9876543211',
    bio: 'Biology teacher with expertise in NEET preparation. Known for interactive teaching methods.',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=150&h=150&fit=crop&crop=face',
    specializations: ['Biology', 'NEET', 'Botany', 'Zoology', 'Human Physiology'],
    experience: 12,
    rating: 4.9,
    totalClasses: 189,
    totalStudents: 980,
    isActive: true,
    availability: [
      { dayOfWeek: 2, startTime: '10:00', endTime: '13:00', timezone: 'Asia/Kolkata' },
      { dayOfWeek: 4, startTime: '15:00', endTime: '18:00', timezone: 'Asia/Kolkata' },
      { dayOfWeek: 6, startTime: '09:00', endTime: '12:00', timezone: 'Asia/Kolkata' }
    ],
    createdAt: '2023-02-10T09:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z'
  },
  {
    id: '3',
    name: 'Prof. Amit Singh',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    bio: 'Mathematics professor specializing in advanced calculus and algebra for competitive exams.',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    specializations: ['Mathematics', 'Calculus', 'Algebra', 'JEE Advanced', 'Coordinate Geometry'],
    experience: 18,
    rating: 4.7,
    totalClasses: 312,
    totalStudents: 1450,
    isActive: true,
    availability: [
      { dayOfWeek: 1, startTime: '14:00', endTime: '17:00', timezone: 'Asia/Kolkata' },
      { dayOfWeek: 3, startTime: '09:00', endTime: '12:00', timezone: 'Asia/Kolkata' },
      { dayOfWeek: 5, startTime: '15:00', endTime: '18:00', timezone: 'Asia/Kolkata' }
    ],
    createdAt: '2023-01-05T11:00:00Z',
    updatedAt: '2024-01-19T10:15:00Z'
  }
]

const mockLiveClasses: LiveClass[] = [
  {
    id: '1',
    title: 'JEE Main Physics - Laws of Motion',
    description: 'Comprehensive session covering Newton\'s laws of motion with problem-solving techniques',
    examId: '1',
    examName: 'JEE Main',
    courseId: '1',
    courseName: 'JEE Main Physics Complete Course',
    subjectId: '1',
    subjectName: 'Mechanics',
    instructorId: '1',
    instructorName: 'Dr. Rajesh Kumar',
    instructorEmail: '<EMAIL>',
    instructorPhone: '+91 9876543210',
    instructorBio: 'Physics expert with 15+ years of teaching experience',
    instructorImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    scheduledDate: '2024-01-25',
    startTime: '10:00',
    endTime: '11:30',
    duration: 90,
    timezone: 'Asia/Kolkata',
    type: 'LIVE',
    mode: 'ONLINE',
    platform: 'ZOOM',
    maxParticipants: 100,
    currentParticipants: 78,
    topics: ['Newton\'s First Law', 'Newton\'s Second Law', 'Newton\'s Third Law', 'Applications', 'Problem Solving'],
    prerequisites: ['Basic understanding of vectors', 'Kinematics concepts'],
    materials: ['Physics Formula Sheet', 'Practice Problems PDF', 'Reference Notes'],
    agenda: '1. Review of concepts (15 min)\n2. Detailed explanation (45 min)\n3. Problem solving (25 min)\n4. Q&A session (5 min)',
    isPublic: true,
    isFree: false,
    price: 299,
    currency: 'INR',
    accessLevel: 'PREMIUM',
    meetingId: 'zoom_meeting_123456',
    meetingPassword: 'physics123',
    meetingUrl: 'https://zoom.us/j/123456789',
    status: 'SCHEDULED',
    isRecurring: false,
    registrations: [
      {
        id: 'reg1',
        classId: '1',
        studentId: '1',
        studentName: 'Arjun Sharma',
        studentEmail: '<EMAIL>',
        studentPhone: '+91 9876543210',
        registeredAt: '2024-01-20T10:00:00Z',
        registrationSource: 'WEBSITE',
        paymentStatus: 'PAID',
        paymentId: 'pay_123456',
        amountPaid: 299,
        attended: false,
        status: 'REGISTERED',
        reminderSent: false,
        confirmationSent: true
      }
    ],
    totalRegistrations: 78,
    attendanceCount: 0,
    averageRating: 4.8,
    totalRatings: 45,
    reminderSent: false,
    followUpSent: false,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    createdBy: 'admin',
    tags: ['JEE Main', 'Physics', 'Mechanics', 'Live Class'],
    notes: 'High-demand topic, expect full attendance'
  },
  {
    id: '2',
    title: 'NEET Biology - Human Physiology',
    description: 'Interactive session on human circulatory and respiratory systems',
    examId: '3',
    examName: 'NEET UG',
    courseId: '2',
    courseName: 'NEET Biology Masterclass',
    subjectId: '3',
    subjectName: 'Botany',
    instructorId: '2',
    instructorName: 'Dr. Priya Sharma',
    instructorEmail: '<EMAIL>',
    instructorPhone: '+91 9876543211',
    instructorBio: 'Biology teacher with expertise in NEET preparation',
    instructorImage: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=150&h=150&fit=crop&crop=face',
    scheduledDate: '2024-01-26',
    startTime: '15:00',
    endTime: '16:30',
    duration: 90,
    timezone: 'Asia/Kolkata',
    type: 'LIVE',
    mode: 'ONLINE',
    platform: 'GOOGLE_MEET',
    maxParticipants: 80,
    currentParticipants: 65,
    topics: ['Circulatory System', 'Heart Structure', 'Blood Circulation', 'Respiratory System', 'Gas Exchange'],
    prerequisites: ['Basic anatomy knowledge', 'Cell biology concepts'],
    materials: ['Biology Diagrams', 'System Charts', 'Practice MCQs'],
    agenda: '1. System overview (20 min)\n2. Detailed anatomy (40 min)\n3. Physiological processes (25 min)\n4. Q&A (5 min)',
    isPublic: true,
    isFree: false,
    price: 249,
    currency: 'INR',
    accessLevel: 'PREMIUM',
    meetingId: 'meet_biology_456',
    meetingUrl: 'https://meet.google.com/abc-defg-hij',
    status: 'SCHEDULED',
    isRecurring: true,
    recurrencePattern: {
      type: 'WEEKLY',
      interval: 1,
      daysOfWeek: [4], // Thursday
      endDate: '2024-06-26'
    },
    registrations: [],
    totalRegistrations: 65,
    attendanceCount: 0,
    averageRating: 4.9,
    totalRatings: 32,
    reminderSent: false,
    followUpSent: false,
    createdAt: '2024-01-12T09:00:00Z',
    updatedAt: '2024-01-20T16:45:00Z',
    createdBy: 'admin',
    tags: ['NEET', 'Biology', 'Human Physiology', 'Live Class'],
    notes: 'Weekly recurring session, very popular among students'
  },
  {
    id: '3',
    title: 'JEE Advanced Mathematics - Calculus Masterclass',
    description: 'Advanced calculus concepts with focus on JEE Advanced level problems',
    examId: '2',
    examName: 'JEE Advanced',
    courseId: '3',
    courseName: 'JEE Advanced Mathematics',
    subjectId: '5',
    subjectName: 'Calculus',
    instructorId: '3',
    instructorName: 'Prof. Amit Singh',
    instructorEmail: '<EMAIL>',
    instructorPhone: '+91 9876543212',
    instructorBio: 'Mathematics professor specializing in advanced calculus',
    instructorImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    scheduledDate: '2024-01-24',
    startTime: '14:00',
    endTime: '16:00',
    duration: 120,
    timezone: 'Asia/Kolkata',
    type: 'LIVE',
    mode: 'ONLINE',
    platform: 'ZOOM',
    maxParticipants: 60,
    currentParticipants: 45,
    topics: ['Limits and Continuity', 'Differentiation', 'Integration', 'Applications', 'Advanced Problems'],
    prerequisites: ['Algebra proficiency', 'Coordinate geometry basics', 'Trigonometry'],
    materials: ['Calculus Formula Sheet', 'Previous Year Questions', 'Practice Problems'],
    agenda: '1. Concept revision (30 min)\n2. Advanced techniques (60 min)\n3. Problem solving (25 min)\n4. Doubt clearing (5 min)',
    isPublic: true,
    isFree: false,
    price: 399,
    currency: 'INR',
    accessLevel: 'PREMIUM',
    meetingId: 'zoom_math_789',
    meetingPassword: 'calculus123',
    meetingUrl: 'https://zoom.us/j/987654321',
    status: 'COMPLETED',
    isRecurring: false,
    registrations: [],
    totalRegistrations: 45,
    attendanceCount: 42,
    averageRating: 4.7,
    totalRatings: 38,
    reminderSent: true,
    followUpSent: true,
    recordingUrl: 'https://recordings.zoom.us/calculus-masterclass-jan24',
    createdAt: '2024-01-10T11:00:00Z',
    updatedAt: '2024-01-24T16:30:00Z',
    createdBy: 'admin',
    tags: ['JEE Advanced', 'Mathematics', 'Calculus', 'Live Class'],
    notes: 'Excellent attendance and engagement, recording available'
  },
  {
    id: '4',
    title: 'Free CBSE Physics Demo Class',
    description: 'Free demo class for Class 12 CBSE Physics students',
    examId: '4',
    examName: 'Class 12 CBSE',
    courseId: '4',
    courseName: 'Class 12 Physics CBSE',
    subjectId: '7',
    subjectName: 'Optics',
    instructorId: '1',
    instructorName: 'Dr. Rajesh Kumar',
    instructorEmail: '<EMAIL>',
    instructorPhone: '+91 9876543210',
    instructorBio: 'Physics expert with 15+ years of teaching experience',
    instructorImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    scheduledDate: '2024-01-27',
    startTime: '16:00',
    endTime: '17:00',
    duration: 60,
    timezone: 'Asia/Kolkata',
    type: 'LIVE',
    mode: 'ONLINE',
    platform: 'GOOGLE_MEET',
    maxParticipants: 200,
    currentParticipants: 156,
    topics: ['Ray Optics', 'Reflection', 'Refraction', 'Lens Formula', 'Practical Applications'],
    prerequisites: ['Basic physics knowledge'],
    materials: ['Demo Slides', 'Basic Formula Sheet'],
    agenda: '1. Introduction (10 min)\n2. Core concepts (35 min)\n3. Demo problems (10 min)\n4. Q&A (5 min)',
    isPublic: true,
    isFree: true,
    accessLevel: 'FREE',
    meetingId: 'meet_demo_cbse',
    meetingUrl: 'https://meet.google.com/demo-cbse-physics',
    status: 'SCHEDULED',
    isRecurring: false,
    registrations: [],
    totalRegistrations: 156,
    attendanceCount: 0,
    reminderSent: false,
    followUpSent: false,
    createdAt: '2024-01-18T14:00:00Z',
    updatedAt: '2024-01-22T12:20:00Z',
    createdBy: 'admin',
    tags: ['CBSE', 'Physics', 'Free', 'Demo Class'],
    notes: 'High registration for free demo, good conversion opportunity'
  },
  {
    id: '5',
    title: 'Organic Chemistry Doubt Clearing Session',
    description: 'Special doubt clearing session for organic chemistry concepts',
    examId: '3',
    examName: 'NEET UG',
    courseId: '5',
    courseName: 'Free NEET Chemistry Basics',
    subjectId: '8',
    subjectName: 'Organic Chemistry Basics',
    instructorId: '2',
    instructorName: 'Dr. Priya Sharma',
    instructorEmail: '<EMAIL>',
    instructorPhone: '+91 9876543211',
    instructorBio: 'Biology teacher with expertise in NEET preparation',
    instructorImage: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=150&h=150&fit=crop&crop=face',
    scheduledDate: '2024-01-23',
    startTime: '18:00',
    endTime: '19:00',
    duration: 60,
    timezone: 'Asia/Kolkata',
    type: 'LIVE',
    mode: 'ONLINE',
    platform: 'ZOOM',
    maxParticipants: 50,
    currentParticipants: 38,
    topics: ['Nomenclature', 'Isomerism', 'Reaction Mechanisms', 'Common Doubts', 'Problem Solving'],
    prerequisites: ['Basic organic chemistry knowledge'],
    materials: ['Doubt List', 'Solution Guide'],
    agenda: '1. Common doubts (20 min)\n2. Concept clarification (25 min)\n3. Quick problems (10 min)\n4. Open Q&A (5 min)',
    isPublic: true,
    isFree: true,
    accessLevel: 'FREE',
    meetingId: 'zoom_doubt_organic',
    meetingPassword: 'organic123',
    meetingUrl: 'https://zoom.us/j/doubt-organic-123',
    status: 'CANCELLED',
    isRecurring: false,
    registrations: [],
    totalRegistrations: 38,
    attendanceCount: 0,
    reminderSent: false,
    followUpSent: false,
    createdAt: '2024-01-15T15:00:00Z',
    updatedAt: '2024-01-23T10:00:00Z',
    createdBy: 'admin',
    tags: ['NEET', 'Chemistry', 'Organic', 'Doubt Clearing'],
    notes: 'Cancelled due to instructor unavailability, rescheduled for next week'
  }
]

export const getLiveClasses = (filters?: LiveClassFilters): Promise<LiveClass[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredClasses = [...mockLiveClasses]
      
      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredClasses = filteredClasses.filter(c => 
            c.title.toLowerCase().includes(searchLower) ||
            c.description?.toLowerCase().includes(searchLower) ||
            c.instructorName.toLowerCase().includes(searchLower) ||
            c.topics.some(topic => topic.toLowerCase().includes(searchLower))
          )
        }
        
        if (filters.instructorId) {
          filteredClasses = filteredClasses.filter(c => c.instructorId === filters.instructorId)
        }
        
        if (filters.examId) {
          filteredClasses = filteredClasses.filter(c => c.examId === filters.examId)
        }
        
        if (filters.courseId) {
          filteredClasses = filteredClasses.filter(c => c.courseId === filters.courseId)
        }
        
        if (filters.subjectId) {
          filteredClasses = filteredClasses.filter(c => c.subjectId === filters.subjectId)
        }
        
        if (filters.status) {
          filteredClasses = filteredClasses.filter(c => c.status === filters.status)
        }
        
        if (filters.type) {
          filteredClasses = filteredClasses.filter(c => c.type === filters.type)
        }
        
        if (filters.mode) {
          filteredClasses = filteredClasses.filter(c => c.mode === filters.mode)
        }
        
        if (filters.platform) {
          filteredClasses = filteredClasses.filter(c => c.platform === filters.platform)
        }
        
        if (filters.accessLevel) {
          filteredClasses = filteredClasses.filter(c => c.accessLevel === filters.accessLevel)
        }
        
        if (filters.isPublic !== undefined) {
          filteredClasses = filteredClasses.filter(c => c.isPublic === filters.isPublic)
        }
        
        if (filters.isFree !== undefined) {
          filteredClasses = filteredClasses.filter(c => c.isFree === filters.isFree)
        }
        
        if (filters.dateFrom) {
          filteredClasses = filteredClasses.filter(c => 
            new Date(c.scheduledDate) >= new Date(filters.dateFrom!)
          )
        }
        
        if (filters.dateTo) {
          filteredClasses = filteredClasses.filter(c => 
            new Date(c.scheduledDate) <= new Date(filters.dateTo!)
          )
        }
        
        if (filters.tags && filters.tags.length > 0) {
          filteredClasses = filteredClasses.filter(c => 
            filters.tags!.some(tag => c.tags.includes(tag))
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredClasses.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredClasses)
    }, 100)
  })
}

export const getLiveClassById = (id: string): Promise<LiveClass | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const liveClass = mockLiveClasses.find(c => c.id === id) || null
      resolve(liveClass)
    }, 100)
  })
}

export const getLiveClassStats = (): Promise<LiveClassStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: LiveClassStats = {
        totalClasses: mockLiveClasses.length,
        scheduledClasses: mockLiveClasses.filter(c => c.status === 'SCHEDULED').length,
        liveClasses: mockLiveClasses.filter(c => c.status === 'LIVE').length,
        completedClasses: mockLiveClasses.filter(c => c.status === 'COMPLETED').length,
        cancelledClasses: mockLiveClasses.filter(c => c.status === 'CANCELLED').length,
        totalRegistrations: mockLiveClasses.reduce((sum, c) => sum + c.totalRegistrations, 0),
        totalAttendance: mockLiveClasses.reduce((sum, c) => sum + c.attendanceCount, 0),
        averageAttendanceRate: 85.5,
        totalRevenue: mockLiveClasses.filter(c => !c.isFree).reduce((sum, c) => sum + (c.price || 0) * c.totalRegistrations, 0),
        averageClassRevenue: 8500,
        totalInstructors: mockInstructors.length,
        activeInstructors: mockInstructors.filter(i => i.isActive).length,
        platformDistribution: [
          { platform: 'ZOOM', count: 3, percentage: 60 },
          { platform: 'GOOGLE_MEET', count: 2, percentage: 40 }
        ],
        popularTimeSlots: [
          { timeSlot: '10:00-12:00', count: 2, attendanceRate: 88.5 },
          { timeSlot: '14:00-16:00', count: 2, attendanceRate: 82.3 },
          { timeSlot: '16:00-18:00', count: 1, attendanceRate: 90.1 }
        ],
        subjectDistribution: [
          { subjectId: '1', subjectName: 'Mechanics', classCount: 1, registrations: 78, attendanceRate: 85.5 },
          { subjectId: '3', subjectName: 'Botany', classCount: 2, registrations: 103, attendanceRate: 87.2 },
          { subjectId: '5', subjectName: 'Calculus', classCount: 1, registrations: 45, attendanceRate: 93.3 }
        ],
        monthlyTrends: [
          { month: 'Jan 2024', classes: mockLiveClasses.length, registrations: 382, attendance: 327, revenue: 45000 }
        ],
        instructorPerformance: [
          { instructorId: '1', instructorName: 'Dr. Rajesh Kumar', classesCount: 2, totalRegistrations: 234, averageRating: 4.8, attendanceRate: 87.5 },
          { instructorId: '2', instructorName: 'Dr. Priya Sharma', classesCount: 2, totalRegistrations: 103, averageRating: 4.9, attendanceRate: 87.2 },
          { instructorId: '3', instructorName: 'Prof. Amit Singh', classesCount: 1, totalRegistrations: 45, averageRating: 4.7, attendanceRate: 93.3 }
        ]
      }
      resolve(stats)
    }, 100)
  })
}

export const getInstructors = (): Promise<Instructor[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockInstructors)
    }, 100)
  })
}

export const createLiveClass = (data: CreateLiveClassRequest): Promise<LiveClass> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const instructor = mockInstructors.find(i => i.id === data.instructorId)
      
      const newClass: LiveClass = {
        id: Date.now().toString(),
        ...data,
        instructorName: instructor?.name || 'Unknown Instructor',
        instructorEmail: instructor?.email || '',
        instructorPhone: instructor?.phone,
        instructorBio: instructor?.bio,
        instructorImage: instructor?.image,
        timezone: data.timezone || 'Asia/Kolkata',
        currentParticipants: 0,
        registrations: [],
        totalRegistrations: 0,
        attendanceCount: 0,
        totalRatings: 0,
        reminderSent: false,
        followUpSent: false,
        status: 'SCHEDULED',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin',
        tags: data.tags || []
      }
      mockLiveClasses.push(newClass)
      resolve(newClass)
    }, 200)
  })
}

export const updateLiveClass = (data: UpdateLiveClassRequest): Promise<LiveClass> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockLiveClasses.findIndex(c => c.id === data.id)
      if (index === -1) {
        reject(new Error('Live class not found'))
        return
      }
      
      mockLiveClasses[index] = {
        ...mockLiveClasses[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockLiveClasses[index])
    }, 200)
  })
}

export const deleteLiveClass = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockLiveClasses.findIndex(c => c.id === id)
      if (index === -1) {
        reject(new Error('Live class not found'))
        return
      }
      
      mockLiveClasses.splice(index, 1)
      resolve()
    }, 200)
  })
}
