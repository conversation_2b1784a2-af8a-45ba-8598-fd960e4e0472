import { Student, CreateStudentRequest, UpdateStudentRequest, StudentFilters, StudentStats, StudentEnrollment, StudentActivity, StudentPerformance } from '@/types/student'

// Mock data - In real app, this would come from your API
const mockStudents: Student[] = [
  {
    id: '1',
    firstName: 'Arjun',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    dateOfBirth: '2005-03-15',
    gender: 'MALE',
    profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    targetExam: '1',
    targetExamName: 'JEE Main',
    currentClass: '12th',
    school: 'Delhi Public School',
    city: 'New Delhi',
    state: 'Delhi',
    country: 'India',
    status: 'ACTIVE',
    registrationDate: '2024-01-15T10:00:00Z',
    lastLoginDate: '2024-01-20T14:30:00Z',
    emailVerified: true,
    phoneVerified: true,
    enrolledCourses: [
      {
        id: 'e1',
        studentId: '1',
        courseId: '1',
        courseName: 'JEE Main Physics Complete Course',
        examId: '1',
        examName: 'JEE Main',
        enrollmentDate: '2024-01-15T10:00:00Z',
        status: 'IN_PROGRESS',
        progress: 75,
        lastAccessedDate: '2024-01-20T14:30:00Z',
        testsAttempted: 12,
        testsPassed: 10,
        averageScore: 78.5,
        totalStudyTime: 2400, // 40 hours
        paymentStatus: 'PAID',
        amountPaid: 9999,
        paymentDate: '2024-01-15T10:00:00Z',
        accessExpiryDate: '2024-07-15T10:00:00Z',
        isAccessActive: true
      }
    ],
    totalCoursesEnrolled: 1,
    activeCourses: 1,
    completedCourses: 0,
    overallProgress: 75,
    averageScore: 78.5,
    totalTestsAttempted: 12,
    totalTestsPassed: 10,
    totalStudyTime: 2400,
    streakDays: 15,
    lastActivityDate: '2024-01-20T14:30:00Z',
    subscriptionType: 'PREMIUM',
    subscriptionStatus: 'ACTIVE',
    subscriptionStartDate: '2024-01-15T10:00:00Z',
    subscriptionEndDate: '2024-07-15T10:00:00Z',
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    marketingEmails: false,
    parentName: 'Rajesh Sharma',
    parentEmail: '<EMAIL>',
    parentPhone: '+91 9876543211',
    notes: 'Excellent student with strong analytical skills',
    tags: ['High Performer', 'JEE Aspirant', 'Premium'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    createdBy: 'admin'
  },
  {
    id: '2',
    firstName: 'Priya',
    lastName: 'Patel',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    dateOfBirth: '2005-07-22',
    gender: 'FEMALE',
    profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=150&h=150&fit=crop&crop=face',
    targetExam: '3',
    targetExamName: 'NEET UG',
    currentClass: '12th',
    school: 'Kendriya Vidyalaya',
    city: 'Mumbai',
    state: 'Maharashtra',
    country: 'India',
    status: 'ACTIVE',
    registrationDate: '2024-01-10T09:00:00Z',
    lastLoginDate: '2024-01-20T16:45:00Z',
    emailVerified: true,
    phoneVerified: true,
    enrolledCourses: [
      {
        id: 'e2',
        studentId: '2',
        courseId: '2',
        courseName: 'NEET Biology Masterclass',
        examId: '3',
        examName: 'NEET UG',
        enrollmentDate: '2024-01-10T09:00:00Z',
        status: 'IN_PROGRESS',
        progress: 65,
        lastAccessedDate: '2024-01-20T16:45:00Z',
        testsAttempted: 8,
        testsPassed: 7,
        averageScore: 82.3,
        totalStudyTime: 1800, // 30 hours
        paymentStatus: 'PAID',
        amountPaid: 12999,
        paymentDate: '2024-01-10T09:00:00Z',
        accessExpiryDate: '2024-07-10T09:00:00Z',
        isAccessActive: true
      }
    ],
    totalCoursesEnrolled: 1,
    activeCourses: 1,
    completedCourses: 0,
    overallProgress: 65,
    averageScore: 82.3,
    totalTestsAttempted: 8,
    totalTestsPassed: 7,
    totalStudyTime: 1800,
    streakDays: 12,
    lastActivityDate: '2024-01-20T16:45:00Z',
    subscriptionType: 'PREMIUM',
    subscriptionStatus: 'ACTIVE',
    subscriptionStartDate: '2024-01-10T09:00:00Z',
    subscriptionEndDate: '2024-07-10T09:00:00Z',
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: true,
    parentName: 'Suresh Patel',
    parentEmail: '<EMAIL>',
    parentPhone: '+91 9876543213',
    notes: 'Dedicated student with consistent performance',
    tags: ['NEET Aspirant', 'Consistent', 'Premium'],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-20T16:45:00Z',
    createdBy: 'admin'
  },
  {
    id: '3',
    firstName: 'Rahul',
    lastName: 'Kumar',
    email: '<EMAIL>',
    phone: '+91 9876543214',
    dateOfBirth: '2006-11-08',
    gender: 'MALE',
    targetExam: '2',
    targetExamName: 'JEE Advanced',
    currentClass: '11th',
    school: 'St. Xavier\'s School',
    city: 'Bangalore',
    state: 'Karnataka',
    country: 'India',
    status: 'ACTIVE',
    registrationDate: '2024-01-05T11:00:00Z',
    lastLoginDate: '2024-01-19T10:15:00Z',
    emailVerified: true,
    phoneVerified: false,
    enrolledCourses: [
      {
        id: 'e3',
        studentId: '3',
        courseId: '3',
        courseName: 'JEE Advanced Mathematics',
        examId: '2',
        examName: 'JEE Advanced',
        enrollmentDate: '2024-01-05T11:00:00Z',
        status: 'IN_PROGRESS',
        progress: 45,
        lastAccessedDate: '2024-01-19T10:15:00Z',
        testsAttempted: 6,
        testsPassed: 4,
        averageScore: 68.7,
        totalStudyTime: 1200, // 20 hours
        paymentStatus: 'PAID',
        amountPaid: 15999,
        paymentDate: '2024-01-05T11:00:00Z',
        accessExpiryDate: '2024-07-05T11:00:00Z',
        isAccessActive: true
      }
    ],
    totalCoursesEnrolled: 1,
    activeCourses: 1,
    completedCourses: 0,
    overallProgress: 45,
    averageScore: 68.7,
    totalTestsAttempted: 6,
    totalTestsPassed: 4,
    totalStudyTime: 1200,
    streakDays: 8,
    lastActivityDate: '2024-01-19T10:15:00Z',
    subscriptionType: 'BASIC',
    subscriptionStatus: 'ACTIVE',
    subscriptionStartDate: '2024-01-05T11:00:00Z',
    subscriptionEndDate: '2024-07-05T11:00:00Z',
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: false,
    marketingEmails: false,
    parentName: 'Amit Kumar',
    parentEmail: '<EMAIL>',
    parentPhone: '+91 9876543215',
    notes: 'Needs improvement in consistency',
    tags: ['JEE Advanced', 'Needs Support', 'Basic'],
    createdAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-19T10:15:00Z',
    createdBy: 'admin'
  },
  {
    id: '4',
    firstName: 'Sneha',
    lastName: 'Singh',
    email: '<EMAIL>',
    phone: '+91 9876543216',
    dateOfBirth: '2005-09-12',
    gender: 'FEMALE',
    targetExam: '4',
    targetExamName: 'Class 12 CBSE',
    currentClass: '12th',
    school: 'Modern School',
    city: 'Chennai',
    state: 'Tamil Nadu',
    country: 'India',
    status: 'ACTIVE',
    registrationDate: '2024-01-12T08:30:00Z',
    lastLoginDate: '2024-01-20T12:20:00Z',
    emailVerified: true,
    phoneVerified: true,
    enrolledCourses: [
      {
        id: 'e4',
        studentId: '4',
        courseId: '4',
        courseName: 'Class 12 Physics CBSE',
        examId: '4',
        examName: 'Class 12 CBSE',
        enrollmentDate: '2024-01-12T08:30:00Z',
        status: 'IN_PROGRESS',
        progress: 85,
        lastAccessedDate: '2024-01-20T12:20:00Z',
        testsAttempted: 15,
        testsPassed: 14,
        averageScore: 89.2,
        totalStudyTime: 3000, // 50 hours
        paymentStatus: 'PAID',
        amountPaid: 7999,
        paymentDate: '2024-01-12T08:30:00Z',
        accessExpiryDate: '2024-07-12T08:30:00Z',
        isAccessActive: true
      }
    ],
    totalCoursesEnrolled: 1,
    activeCourses: 1,
    completedCourses: 0,
    overallProgress: 85,
    averageScore: 89.2,
    totalTestsAttempted: 15,
    totalTestsPassed: 14,
    totalStudyTime: 3000,
    streakDays: 22,
    lastActivityDate: '2024-01-20T12:20:00Z',
    subscriptionType: 'PREMIUM',
    subscriptionStatus: 'ACTIVE',
    subscriptionStartDate: '2024-01-12T08:30:00Z',
    subscriptionEndDate: '2024-07-12T08:30:00Z',
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    marketingEmails: true,
    parentName: 'Vikram Singh',
    parentEmail: '<EMAIL>',
    parentPhone: '+91 9876543217',
    notes: 'Outstanding performance, very dedicated',
    tags: ['Top Performer', 'CBSE', 'Consistent', 'Premium'],
    createdAt: '2024-01-12T08:30:00Z',
    updatedAt: '2024-01-20T12:20:00Z',
    createdBy: 'admin'
  },
  {
    id: '5',
    firstName: 'Aditya',
    lastName: 'Gupta',
    email: '<EMAIL>',
    phone: '+91 9876543218',
    dateOfBirth: '2006-01-25',
    gender: 'MALE',
    targetExam: '3',
    targetExamName: 'NEET UG',
    currentClass: '11th',
    school: 'DAV Public School',
    city: 'Pune',
    state: 'Maharashtra',
    country: 'India',
    status: 'INACTIVE',
    registrationDate: '2024-01-08T15:00:00Z',
    lastLoginDate: '2024-01-15T09:45:00Z',
    emailVerified: true,
    phoneVerified: false,
    enrolledCourses: [
      {
        id: 'e5',
        studentId: '5',
        courseId: '5',
        courseName: 'Free NEET Chemistry Basics',
        examId: '3',
        examName: 'NEET UG',
        enrollmentDate: '2024-01-08T15:00:00Z',
        status: 'ENROLLED',
        progress: 25,
        lastAccessedDate: '2024-01-15T09:45:00Z',
        testsAttempted: 3,
        testsPassed: 2,
        averageScore: 65.0,
        totalStudyTime: 600, // 10 hours
        paymentStatus: 'FREE',
        isAccessActive: true
      }
    ],
    totalCoursesEnrolled: 1,
    activeCourses: 1,
    completedCourses: 0,
    overallProgress: 25,
    averageScore: 65.0,
    totalTestsAttempted: 3,
    totalTestsPassed: 2,
    totalStudyTime: 600,
    streakDays: 3,
    lastActivityDate: '2024-01-15T09:45:00Z',
    subscriptionType: 'FREE',
    subscriptionStatus: 'ACTIVE',
    emailNotifications: false,
    smsNotifications: false,
    pushNotifications: false,
    marketingEmails: false,
    parentName: 'Rajesh Gupta',
    parentEmail: '<EMAIL>',
    parentPhone: '+91 9876543219',
    notes: 'Free user, low engagement',
    tags: ['Free User', 'Low Engagement', 'NEET'],
    createdAt: '2024-01-08T15:00:00Z',
    updatedAt: '2024-01-15T09:45:00Z',
    createdBy: 'admin'
  }
]

export const getStudents = (filters?: StudentFilters): Promise<Student[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredStudents = [...mockStudents]
      
      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase()
          filteredStudents = filteredStudents.filter(s => 
            s.firstName.toLowerCase().includes(searchLower) ||
            s.lastName.toLowerCase().includes(searchLower) ||
            s.email.toLowerCase().includes(searchLower) ||
            s.phone?.toLowerCase().includes(searchLower) ||
            s.school?.toLowerCase().includes(searchLower) ||
            s.city?.toLowerCase().includes(searchLower)
          )
        }
        
        if (filters.status) {
          filteredStudents = filteredStudents.filter(s => s.status === filters.status)
        }
        
        if (filters.subscriptionType) {
          filteredStudents = filteredStudents.filter(s => s.subscriptionType === filters.subscriptionType)
        }
        
        if (filters.subscriptionStatus) {
          filteredStudents = filteredStudents.filter(s => s.subscriptionStatus === filters.subscriptionStatus)
        }
        
        if (filters.targetExam) {
          filteredStudents = filteredStudents.filter(s => s.targetExam === filters.targetExam)
        }
        
        if (filters.city) {
          filteredStudents = filteredStudents.filter(s => s.city?.toLowerCase().includes(filters.city!.toLowerCase()))
        }
        
        if (filters.state) {
          filteredStudents = filteredStudents.filter(s => s.state?.toLowerCase().includes(filters.state!.toLowerCase()))
        }
        
        if (filters.tags && filters.tags.length > 0) {
          filteredStudents = filteredStudents.filter(s => 
            filters.tags!.some(tag => s.tags.includes(tag))
          )
        }
        
        // Date filters
        if (filters.registrationDateFrom) {
          filteredStudents = filteredStudents.filter(s => 
            new Date(s.registrationDate) >= new Date(filters.registrationDateFrom!)
          )
        }
        
        if (filters.registrationDateTo) {
          filteredStudents = filteredStudents.filter(s => 
            new Date(s.registrationDate) <= new Date(filters.registrationDateTo!)
          )
        }
        
        // Sorting
        if (filters.sortBy) {
          filteredStudents.sort((a, b) => {
            const aVal = a[filters.sortBy!]
            const bVal = b[filters.sortBy!]
            const order = filters.sortOrder === 'desc' ? -1 : 1
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return aVal.localeCompare(bVal) * order
            }
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return (aVal - bVal) * order
            }
            return 0
          })
        }
      }
      
      resolve(filteredStudents)
    }, 100)
  })
}

export const getStudentById = (id: string): Promise<Student | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const student = mockStudents.find(s => s.id === id) || null
      resolve(student)
    }, 100)
  })
}

export const getStudentStats = (): Promise<StudentStats> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats: StudentStats = {
        totalStudents: mockStudents.length,
        activeStudents: mockStudents.filter(s => s.status === 'ACTIVE').length,
        newStudentsThisMonth: mockStudents.filter(s => {
          const registrationDate = new Date(s.registrationDate)
          const now = new Date()
          return registrationDate.getMonth() === now.getMonth() && 
                 registrationDate.getFullYear() === now.getFullYear()
        }).length,
        totalEnrollments: mockStudents.reduce((sum, s) => sum + s.totalCoursesEnrolled, 0),
        averageProgress: mockStudents.reduce((sum, s) => sum + s.overallProgress, 0) / mockStudents.length,
        averageScore: mockStudents.reduce((sum, s) => sum + s.averageScore, 0) / mockStudents.length,
        totalStudyTime: mockStudents.reduce((sum, s) => sum + s.totalStudyTime, 0),
        subscriptionDistribution: {
          free: mockStudents.filter(s => s.subscriptionType === 'FREE').length,
          basic: mockStudents.filter(s => s.subscriptionType === 'BASIC').length,
          premium: mockStudents.filter(s => s.subscriptionType === 'PREMIUM').length,
          enterprise: mockStudents.filter(s => s.subscriptionType === 'ENTERPRISE').length
        },
        statusDistribution: {
          active: mockStudents.filter(s => s.status === 'ACTIVE').length,
          inactive: mockStudents.filter(s => s.status === 'INACTIVE').length,
          suspended: mockStudents.filter(s => s.status === 'SUSPENDED').length,
          pending: mockStudents.filter(s => s.status === 'PENDING').length
        },
        examDistribution: [
          { examId: '1', examName: 'JEE Main', studentCount: mockStudents.filter(s => s.targetExam === '1').length },
          { examId: '2', examName: 'JEE Advanced', studentCount: mockStudents.filter(s => s.targetExam === '2').length },
          { examId: '3', examName: 'NEET UG', studentCount: mockStudents.filter(s => s.targetExam === '3').length },
          { examId: '4', examName: 'Class 12 CBSE', studentCount: mockStudents.filter(s => s.targetExam === '4').length }
        ],
        cityDistribution: [
          { city: 'New Delhi', studentCount: mockStudents.filter(s => s.city === 'New Delhi').length },
          { city: 'Mumbai', studentCount: mockStudents.filter(s => s.city === 'Mumbai').length },
          { city: 'Bangalore', studentCount: mockStudents.filter(s => s.city === 'Bangalore').length },
          { city: 'Chennai', studentCount: mockStudents.filter(s => s.city === 'Chennai').length },
          { city: 'Pune', studentCount: mockStudents.filter(s => s.city === 'Pune').length }
        ],
        monthlyRegistrations: [
          { month: 'Jan 2024', count: mockStudents.length }
        ]
      }
      resolve(stats)
    }, 100)
  })
}

export const createStudent = (data: CreateStudentRequest): Promise<Student> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newStudent: Student = {
        id: Date.now().toString(),
        ...data,
        status: 'ACTIVE',
        registrationDate: new Date().toISOString(),
        emailVerified: false,
        phoneVerified: false,
        enrolledCourses: [],
        totalCoursesEnrolled: 0,
        activeCourses: 0,
        completedCourses: 0,
        overallProgress: 0,
        averageScore: 0,
        totalTestsAttempted: 0,
        totalTestsPassed: 0,
        totalStudyTime: 0,
        streakDays: 0,
        subscriptionStatus: 'ACTIVE',
        emailNotifications: data.emailNotifications ?? true,
        smsNotifications: data.smsNotifications ?? true,
        pushNotifications: data.pushNotifications ?? true,
        marketingEmails: false,
        tags: data.tags || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      }
      mockStudents.push(newStudent)
      resolve(newStudent)
    }, 200)
  })
}

export const updateStudent = (data: UpdateStudentRequest): Promise<Student> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockStudents.findIndex(s => s.id === data.id)
      if (index === -1) {
        reject(new Error('Student not found'))
        return
      }
      
      mockStudents[index] = {
        ...mockStudents[index],
        ...data,
        updatedAt: new Date().toISOString()
      }
      resolve(mockStudents[index])
    }, 200)
  })
}

export const deleteStudent = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockStudents.findIndex(s => s.id === id)
      if (index === -1) {
        reject(new Error('Student not found'))
        return
      }
      
      mockStudents.splice(index, 1)
      resolve()
    }, 200)
  })
}
