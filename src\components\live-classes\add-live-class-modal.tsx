'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X, Plus, CheckCircle, AlertCircle, Video, Calendar, Clock, Users, Globe, Settings } from 'lucide-react'
import { CreateLiveClassRequest } from '@/types/live-class'
import { Instructor } from '@/types/live-class'
import { Exam } from '@/types/exam'
import { Course } from '@/types/course'
import { Subject } from '@/types/subject'

interface AddLiveClassModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (data: CreateLiveClassRequest) => Promise<void>
  instructors: Instructor[]
  exams: Exam[]
  courses: Course[]
  subjects: Subject[]
}

export function AddLiveClassModal({ 
  isOpen, 
  onClose, 
  onAdd, 
  instructors, 
  exams, 
  courses, 
  subjects 
}: AddLiveClassModalProps) {
  const [formData, setFormData] = useState<Partial<CreateLiveClassRequest>>({
    type: 'LIVE',
    mode: 'ONLINE',
    platform: 'ZOOM',
    isPublic: true,
    isFree: false,
    currency: 'INR',
    accessLevel: 'PREMIUM',
    isRecurring: false,
    timezone: 'Asia/Kolkata',
    topics: [],
    prerequisites: [],
    materials: [],
    tags: []
  })
  const [loading, setLoading] = useState(false)
  const [completed, setCompleted] = useState(false)
  const [error, setError] = useState<string>()
  const [topicInput, setTopicInput] = useState('')
  const [prerequisiteInput, setPrerequisiteInput] = useState('')
  const [materialInput, setMaterialInput] = useState('')
  const [tagInput, setTagInput] = useState('')

  const platforms = [
    { value: 'ZOOM', label: 'Zoom' },
    { value: 'GOOGLE_MEET', label: 'Google Meet' },
    { value: 'MICROSOFT_TEAMS', label: 'Microsoft Teams' },
    { value: 'CUSTOM', label: 'Custom Platform' },
    { value: 'OFFLINE', label: 'Offline/Physical' }
  ]

  const accessLevels = [
    { value: 'FREE', label: 'Free Access' },
    { value: 'BASIC', label: 'Basic Plan' },
    { value: 'PREMIUM', label: 'Premium Plan' },
    { value: 'ENTERPRISE', label: 'Enterprise Plan' }
  ]

  const currencies = [
    { value: 'INR', label: 'INR (₹)', symbol: '₹' },
    { value: 'USD', label: 'USD ($)', symbol: '$' },
    { value: 'EUR', label: 'EUR (€)', symbol: '€' }
  ]

  const handleAddTopic = () => {
    if (topicInput.trim() && !formData.topics?.includes(topicInput.trim())) {
      setFormData(prev => ({
        ...prev,
        topics: [...(prev.topics || []), topicInput.trim()]
      }))
      setTopicInput('')
    }
  }

  const handleRemoveTopic = (topicToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      topics: prev.topics?.filter(topic => topic !== topicToRemove) || []
    }))
  }

  const handleAddPrerequisite = () => {
    if (prerequisiteInput.trim() && !formData.prerequisites?.includes(prerequisiteInput.trim())) {
      setFormData(prev => ({
        ...prev,
        prerequisites: [...(prev.prerequisites || []), prerequisiteInput.trim()]
      }))
      setPrerequisiteInput('')
    }
  }

  const handleRemovePrerequisite = (prerequisiteToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      prerequisites: prev.prerequisites?.filter(prerequisite => prerequisite !== prerequisiteToRemove) || []
    }))
  }

  const handleAddMaterial = () => {
    if (materialInput.trim() && !formData.materials?.includes(materialInput.trim())) {
      setFormData(prev => ({
        ...prev,
        materials: [...(prev.materials || []), materialInput.trim()]
      }))
      setMaterialInput('')
    }
  }

  const handleRemoveMaterial = (materialToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      materials: prev.materials?.filter(material => material !== materialToRemove) || []
    }))
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title || !formData.instructorId || !formData.scheduledDate || !formData.startTime || !formData.endTime) {
      setError('Please fill in all required fields')
      return
    }

    if (!formData.topics || formData.topics.length === 0) {
      setError('Please add at least one topic')
      return
    }

    if (!formData.isFree && (!formData.price || formData.price <= 0)) {
      setError('Please set a valid price for paid classes')
      return
    }

    // Calculate duration
    const startTime = new Date(`${formData.scheduledDate}T${formData.startTime}`)
    const endTime = new Date(`${formData.scheduledDate}T${formData.endTime}`)
    const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60))

    if (duration <= 0) {
      setError('End time must be after start time')
      return
    }

    setLoading(true)
    setError(undefined)

    try {
      const classData: CreateLiveClassRequest = {
        title: formData.title!,
        description: formData.description,
        examId: formData.examId,
        courseId: formData.courseId,
        subjectId: formData.subjectId,
        instructorId: formData.instructorId!,
        scheduledDate: formData.scheduledDate!,
        startTime: formData.startTime!,
        endTime: formData.endTime!,
        timezone: formData.timezone!,
        type: formData.type!,
        mode: formData.mode!,
        platform: formData.platform!,
        maxParticipants: formData.maxParticipants,
        topics: formData.topics!,
        prerequisites: formData.prerequisites,
        materials: formData.materials,
        agenda: formData.agenda,
        isPublic: formData.isPublic!,
        isFree: formData.isFree!,
        price: formData.price,
        currency: formData.currency,
        accessLevel: formData.accessLevel!,
        isRecurring: formData.isRecurring!,
        recurrencePattern: formData.recurrencePattern,
        tags: formData.tags,
        notes: formData.notes
      }

      await onAdd(classData)
      setCompleted(true)
      
      // Auto close after success
      setTimeout(() => {
        onClose()
        resetForm()
      }, 2000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to schedule live class')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      type: 'LIVE',
      mode: 'ONLINE',
      platform: 'ZOOM',
      isPublic: true,
      isFree: false,
      currency: 'INR',
      accessLevel: 'PREMIUM',
      isRecurring: false,
      timezone: 'Asia/Kolkata',
      topics: [],
      prerequisites: [],
      materials: [],
      tags: []
    })
    setLoading(false)
    setCompleted(false)
    setError(undefined)
    setTopicInput('')
    setPrerequisiteInput('')
    setMaterialInput('')
    setTagInput('')
  }

  const selectedInstructor = instructors.find(i => i.id === formData.instructorId)
  const currencySymbol = currencies.find(c => c.value === formData.currency)?.symbol || '₹'

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-semibold flex items-center space-x-2">
            <Video className="w-5 h-5 text-blue-600" />
            <span>Schedule Live Class</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {completed ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-700 mb-2">Live Class Scheduled Successfully!</h3>
              <p className="text-gray-600">The class has been scheduled and notifications will be sent.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Display */}
              {error && (
                <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Class Title *
                  </label>
                  <Input
                    value={formData.title || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="e.g., JEE Main Physics - Laws of Motion"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.description || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of the class content and objectives..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Exam
                    </label>
                    <Select 
                      value={formData.examId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, examId: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select exam" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific exam</SelectItem>
                        {exams.map((exam) => (
                          <SelectItem key={exam.id} value={exam.id}>
                            {exam.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Course
                    </label>
                    <Select 
                      value={formData.courseId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, courseId: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select course" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific course</SelectItem>
                        {courses.map((course) => (
                          <SelectItem key={course.id} value={course.id}>
                            {course.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Subject
                    </label>
                    <Select 
                      value={formData.subjectId || ''} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific subject</SelectItem>
                        {subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Content & Topics */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Content & Topics</h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Topics *
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <Input
                      value={topicInput}
                      onChange={(e) => setTopicInput(e.target.value)}
                      placeholder="Add a topic"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTopic())}
                    />
                    <Button type="button" onClick={handleAddTopic} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.topics?.map((topic, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                        <span>{topic}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveTopic(topic)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Agenda
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    value={formData.agenda || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, agenda: e.target.value }))}
                    placeholder="Class agenda and timeline..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Prerequisites
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <Input
                      value={prerequisiteInput}
                      onChange={(e) => setPrerequisiteInput(e.target.value)}
                      placeholder="Add a prerequisite"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddPrerequisite())}
                    />
                    <Button type="button" onClick={handleAddPrerequisite} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.prerequisites?.map((prerequisite, index) => (
                      <Badge key={index} variant="outline" className="flex items-center space-x-1">
                        <span>{prerequisite}</span>
                        <button
                          type="button"
                          onClick={() => handleRemovePrerequisite(prerequisite)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Materials
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <Input
                      value={materialInput}
                      onChange={(e) => setMaterialInput(e.target.value)}
                      placeholder="Add class material"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddMaterial())}
                    />
                    <Button type="button" onClick={handleAddMaterial} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.materials?.map((material, index) => (
                      <Badge key={index} variant="outline" className="flex items-center space-x-1">
                        <span>{material}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveMaterial(material)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tags
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add a tag"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                    />
                    <Button type="button" onClick={handleAddTag} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags?.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                        <span>{tag}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    value={formData.notes || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Additional notes for this class..."
                  />
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <Video className="w-4 h-4" />
                  <span>{loading ? 'Scheduling...' : 'Schedule Class'}</span>
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
