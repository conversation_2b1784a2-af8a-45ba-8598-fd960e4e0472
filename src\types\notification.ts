export interface Notification {
  id: string
  title: string
  message: string
  
  // Type and Category
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'ANNOUNCEMENT'
  category: 'SYSTEM' | 'ACADEMIC' | 'PAYMENT' | 'CLASS' | 'EXAM' | 'GENERAL' | 'MARKETING'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  
  // Recipients
  recipientType: 'ALL' | 'STUDENTS' | 'INSTRUCTORS' | 'ADMINS' | 'SPECIFIC' | 'GROUP'
  recipients: string[] // User IDs or group IDs
  recipientCount: number
  
  // Delivery Channels
  channels: NotificationChannel[]
  
  // Content
  content: {
    shortText: string
    fullText?: string
    htmlContent?: string
    attachments?: NotificationAttachment[]
    actionButtons?: NotificationAction[]
  }
  
  // Scheduling
  scheduledAt?: string
  sendImmediately: boolean
  timezone: string
  
  // Status and Tracking
  status: 'DRAFT' | 'SCHEDULED' | 'SENDING' | 'SENT' | 'FAILED' | 'CANCELLED'
  deliveryStatus: {
    total: number
    sent: number
    delivered: number
    read: number
    failed: number
    clicked: number
  }
  
  // Engagement
  readBy: string[] // User IDs who read the notification
  clickedBy: string[] // User IDs who clicked actions
  dismissedBy: string[] // User IDs who dismissed the notification
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  sentAt?: string
  expiresAt?: string
  
  // Analytics
  analytics: {
    openRate: number
    clickRate: number
    dismissalRate: number
    avgReadTime: number
  }
  
  // Tags and Organization
  tags: string[]
  campaign?: string
  template?: string
}

export interface NotificationChannel {
  type: 'IN_APP' | 'EMAIL' | 'SMS' | 'PUSH' | 'WHATSAPP' | 'TELEGRAM'
  enabled: boolean
  config?: {
    subject?: string // For email
    template?: string
    customData?: Record<string, any>
  }
}

export interface NotificationAttachment {
  id: string
  name: string
  type: 'IMAGE' | 'PDF' | 'DOCUMENT' | 'VIDEO' | 'AUDIO' | 'LINK'
  url: string
  size?: number
  mimeType?: string
}

export interface NotificationAction {
  id: string
  label: string
  type: 'BUTTON' | 'LINK' | 'DISMISS'
  action: string // URL or action identifier
  style: 'PRIMARY' | 'SECONDARY' | 'SUCCESS' | 'WARNING' | 'DANGER'
  trackClicks: boolean
}

export interface NotificationTemplate {
  id: string
  name: string
  description: string
  category: Notification['category']
  type: Notification['type']
  
  // Template Content
  title: string
  message: string
  htmlContent?: string
  
  // Variables
  variables: NotificationVariable[]
  
  // Default Settings
  defaultChannels: NotificationChannel[]
  defaultPriority: Notification['priority']
  defaultRecipientType: Notification['recipientType']
  
  // Usage
  usageCount: number
  lastUsed?: string
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  isActive: boolean
}

export interface NotificationVariable {
  key: string
  label: string
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'BOOLEAN' | 'URL'
  required: boolean
  defaultValue?: string
  description?: string
}

export interface NotificationGroup {
  id: string
  name: string
  description: string
  type: 'STATIC' | 'DYNAMIC'
  
  // Members
  members: string[] // User IDs for static groups
  criteria?: GroupCriteria // For dynamic groups
  memberCount: number
  
  // Settings
  isActive: boolean
  allowSelfSubscribe: boolean
  allowSelfUnsubscribe: boolean
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  tags: string[]
}

export interface GroupCriteria {
  userType?: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN'
  examIds?: string[]
  courseIds?: string[]
  subjectIds?: string[]
  classIds?: string[]
  paymentStatus?: 'PAID' | 'PENDING' | 'FAILED'
  subscriptionStatus?: 'ACTIVE' | 'EXPIRED' | 'CANCELLED'
  registrationDateFrom?: string
  registrationDateTo?: string
  lastActiveFrom?: string
  lastActiveTo?: string
  tags?: string[]
  customFilters?: Record<string, any>
}

export interface NotificationCampaign {
  id: string
  name: string
  description: string
  type: 'ONE_TIME' | 'RECURRING' | 'DRIP' | 'TRIGGERED'
  
  // Campaign Settings
  startDate: string
  endDate?: string
  timezone: string
  
  // Notifications
  notifications: CampaignNotification[]
  
  // Targeting
  targetGroups: string[]
  targetCriteria?: GroupCriteria
  
  // Status and Performance
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
  totalRecipients: number
  totalSent: number
  totalDelivered: number
  totalRead: number
  totalClicked: number
  
  // Analytics
  performance: {
    deliveryRate: number
    openRate: number
    clickRate: number
    conversionRate: number
    unsubscribeRate: number
  }
  
  // Metadata
  createdAt: string
  updatedAt: string
  createdBy: string
  tags: string[]
}

export interface CampaignNotification {
  id: string
  notificationId: string
  delay: number // Minutes after campaign start or previous notification
  conditions?: TriggerCondition[]
}

export interface TriggerCondition {
  type: 'USER_ACTION' | 'TIME_BASED' | 'DATA_CHANGE' | 'EXTERNAL_EVENT'
  condition: string
  value: any
  operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS'
}

export interface NotificationStats {
  totalNotifications: number
  sentToday: number
  sentThisWeek: number
  sentThisMonth: number
  
  // Status Distribution
  statusDistribution: {
    status: string
    count: number
    percentage: number
  }[]
  
  // Channel Performance
  channelPerformance: {
    channel: string
    sent: number
    delivered: number
    opened: number
    clicked: number
    deliveryRate: number
    openRate: number
    clickRate: number
  }[]
  
  // Category Performance
  categoryPerformance: {
    category: string
    count: number
    openRate: number
    clickRate: number
  }[]
  
  // Recent Activity
  recentActivity: {
    date: string
    sent: number
    delivered: number
    opened: number
    clicked: number
  }[]
  
  // Top Performing
  topNotifications: {
    id: string
    title: string
    openRate: number
    clickRate: number
    sentCount: number
  }[]
  
  // Engagement Metrics
  avgOpenRate: number
  avgClickRate: number
  avgDeliveryRate: number
  totalEngagement: number
  
  // Growth Metrics
  monthlyGrowth: number
  weeklyGrowth: number
}

export interface CreateNotificationRequest {
  title: string
  message: string
  type: Notification['type']
  category: Notification['category']
  priority: Notification['priority']
  recipientType: Notification['recipientType']
  recipients?: string[]
  channels: NotificationChannel[]
  content: Notification['content']
  scheduledAt?: string
  sendImmediately: boolean
  timezone?: string
  expiresAt?: string
  tags?: string[]
  campaign?: string
  template?: string
}

export interface UpdateNotificationRequest extends Partial<CreateNotificationRequest> {
  id: string
  status?: Notification['status']
}

export interface NotificationFilters {
  search?: string
  type?: Notification['type']
  category?: Notification['category']
  priority?: Notification['priority']
  status?: Notification['status']
  recipientType?: Notification['recipientType']
  channel?: string
  createdBy?: string
  dateFrom?: string
  dateTo?: string
  tags?: string[]
  campaign?: string
  sortBy?: 'createdAt' | 'scheduledAt' | 'sentAt' | 'title' | 'recipientCount' | 'openRate'
  sortOrder?: 'asc' | 'desc'
}

export interface NotificationRecipient {
  id: string
  notificationId: string
  userId: string
  userType: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN'
  userName: string
  userEmail: string
  userPhone?: string
  
  // Delivery Status per Channel
  deliveryStatus: {
    channel: string
    status: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED'
    sentAt?: string
    deliveredAt?: string
    failureReason?: string
  }[]
  
  // Engagement
  readAt?: string
  clickedAt?: string
  dismissedAt?: string
  actions: {
    actionId: string
    clickedAt: string
  }[]
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface NotificationSettings {
  id: string
  userId: string
  userType: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN'
  
  // Channel Preferences
  channelPreferences: {
    channel: string
    enabled: boolean
    quietHours?: {
      start: string
      end: string
      timezone: string
    }
  }[]
  
  // Category Preferences
  categoryPreferences: {
    category: string
    enabled: boolean
    priority: 'ALL' | 'HIGH_ONLY' | 'URGENT_ONLY'
  }[]
  
  // Frequency Settings
  frequency: 'IMMEDIATE' | 'HOURLY' | 'DAILY' | 'WEEKLY'
  digestEnabled: boolean
  digestTime?: string
  digestDays?: number[]
  
  // Subscription Management
  subscriptions: string[] // Group IDs
  unsubscribedFrom: string[] // Notification IDs or campaign IDs
  
  // Metadata
  createdAt: string
  updatedAt: string
}

export interface NotificationAnalytics {
  notificationId: string
  
  // Delivery Analytics
  deliveryMetrics: {
    totalRecipients: number
    successfulDeliveries: number
    failedDeliveries: number
    deliveryRate: number
    avgDeliveryTime: number
  }
  
  // Engagement Analytics
  engagementMetrics: {
    totalOpened: number
    uniqueOpened: number
    totalClicks: number
    uniqueClicks: number
    openRate: number
    clickRate: number
    avgReadTime: number
  }
  
  // Channel Analytics
  channelBreakdown: {
    channel: string
    sent: number
    delivered: number
    opened: number
    clicked: number
    failed: number
  }[]
  
  // Time-based Analytics
  timeAnalytics: {
    hour: number
    opened: number
    clicked: number
  }[]
  
  // Geographic Analytics
  geographicData: {
    country: string
    state: string
    city: string
    count: number
  }[]
  
  // Device Analytics
  deviceData: {
    device: string
    platform: string
    count: number
  }[]
  
  // Action Analytics
  actionAnalytics: {
    actionId: string
    actionLabel: string
    clicks: number
    clickRate: number
  }[]
}
