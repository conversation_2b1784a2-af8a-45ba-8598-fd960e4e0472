const { validationResult } = require('express-validator');
const dashboardService = require('../services/dashboardService');
const { successResponse, errorResponse } = require('../utils/responseHelper');
const { AppError } = require('../utils/errorHandler');

/**
 * Dashboard Analytics Controller
 * Handles all dashboard-related API endpoints
 */
class DashboardController {
  /**
   * GET /dashboard/overview
   * Get comprehensive dashboard statistics and metrics
   */
  async getOverview(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const { period = '30d', compare_previous = 'false', timezone = 'Asia/Kolkata' } = req.query;
      const userId = req.user.id;

      const overviewData = await dashboardService.getOverviewData({
        period,
        comparePrevious: compare_previous === 'true',
        timezone,
        userId
      });

      return successResponse(res, overviewData, 'Dashboard overview retrieved successfully', {
        period,
        timezone,
        last_updated: new Date().toISOString(),
        cache_expires: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /dashboard/analytics
   * Get detailed analytics data with period comparisons
   */
  async getAnalytics(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const {
        period,
        metrics,
        compare_previous = 'true',
        granularity = 'day',
        timezone = 'Asia/Kolkata'
      } = req.query;

      if (!period) {
        throw new AppError('Period parameter is required', 400);
      }

      const analyticsData = await dashboardService.getDetailedAnalytics({
        period,
        metrics: metrics ? metrics.split(',') : null,
        comparePrevious: compare_previous === 'true',
        granularity,
        timezone,
        userId: req.user.id
      });

      return successResponse(res, analyticsData, 'Analytics data retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /dashboard/widgets
   * Get customizable dashboard widgets with chart data
   */
  async getWidgets(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const {
        widget_types,
        period = '30d'
      } = req.query;

      const widgetData = await dashboardService.getWidgetData({
        widgetTypes: widget_types ? widget_types.split(',') : null,
        period,
        userId: req.user.id
      });

      return successResponse(res, widgetData, 'Widget data retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * POST /dashboard/widgets/layout
   * Save custom dashboard widget layout
   */
  async saveWidgetLayout(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const { layout } = req.body;
      const userId = req.user.id;

      if (!layout || !Array.isArray(layout)) {
        throw new AppError('Layout must be an array', 400);
      }

      const savedLayout = await dashboardService.saveWidgetLayout({
        layout,
        userId
      });

      return successResponse(res, savedLayout, 'Dashboard layout saved successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /dashboard/reports
   * Get comprehensive dashboard reports with insights
   */
  async getReports(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const {
        report_type,
        date,
        include_recommendations = 'true'
      } = req.query;

      if (!report_type) {
        throw new AppError('Report type is required', 400);
      }

      const reportData = await dashboardService.generateReport({
        reportType: report_type,
        date: date || new Date().toISOString().split('T')[0],
        includeRecommendations: include_recommendations === 'true',
        userId: req.user.id
      });

      return successResponse(res, reportData, 'Report generated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /dashboard/export
   * Export dashboard data in various formats
   */
  async exportData(req, res, next) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const {
        format,
        data_type,
        period = '30d',
        include_charts = 'true'
      } = req.query;

      if (!format || !data_type) {
        throw new AppError('Format and data_type parameters are required', 400);
      }

      const exportData = await dashboardService.exportData({
        format,
        dataType: data_type,
        period,
        includeCharts: include_charts === 'true',
        userId: req.user.id
      });

      return successResponse(res, exportData, 'Export generated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * GET /dashboard/real-time
   * WebSocket endpoint for real-time dashboard updates
   */
  async handleRealTimeConnection(ws, req) {
    try {
      const token = req.query.token;
      if (!token) {
        ws.close(1008, 'Authentication token required');
        return;
      }

      // Verify JWT token and get user
      const user = await dashboardService.verifyWebSocketToken(token);
      if (!user) {
        ws.close(1008, 'Invalid authentication token');
        return;
      }

      // Subscribe to real-time updates
      await dashboardService.subscribeToRealTimeUpdates(ws, user.id);

      ws.on('close', () => {
        dashboardService.unsubscribeFromRealTimeUpdates(ws, user.id);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        dashboardService.unsubscribeFromRealTimeUpdates(ws, user.id);
      });

    } catch (error) {
      console.error('WebSocket connection error:', error);
      ws.close(1011, 'Internal server error');
    }
  }
}

module.exports = new DashboardController();
